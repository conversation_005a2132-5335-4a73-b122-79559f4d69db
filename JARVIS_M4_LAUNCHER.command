#!/bin/bash

# 🤖 JARVIS M4 COMPLET - LANCEUR BUREAU
# <PERSON><PERSON><PERSON> - Lien de bureau pour démarrage rapide

echo "🤖 JARVIS M4 COMPLET - DÉMARRAGE RAPIDE"
echo "======================================"
echo "👤 Jean-Luc Passave"
echo "📅 $(date)"
echo ""

# Aller dans le bon répertoire
cd "/Volumes/seagate/Louna_Electron_Latest"

echo "📁 Répertoire: $(pwd)"
echo ""

# Vérifier que nous sommes dans le bon dossier
if [ ! -f "jarvis_electron_complete_m4.js" ]; then
    echo "❌ ERREUR: Fichier principal non trouvé"
    echo "📁 Vérifiez que vous êtes dans le bon répertoire"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "✅ Fichiers JARVIS M4 trouvés"
echo ""

# Afficher les options
echo "🚀 OPTIONS DE DÉMARRAGE:"
echo "========================"
echo "1. 🤖 Démarrage Complet (Electron + JARVIS)"
echo "2. 🐍 JARVIS Python seulement"
echo "3. 🖥️ Electron seulement"
echo "4. 📊 Statut des services"
echo "5. 🔧 Maintenance"
echo "6. ❌ Quitter"
echo ""

read -p "Choisissez une option (1-6): " choice

case $choice in
    1)
        echo ""
        echo "🚀 DÉMARRAGE COMPLET JARVIS M4"
        echo "=============================="
        echo "🍎 Apple Silicon M4 Optimisé"
        echo "⚡ Toutes les fonctionnalités activées"
        echo ""
        npm run complete
        ;;
    2)
        echo ""
        echo "🐍 DÉMARRAGE JARVIS PYTHON"
        echo "=========================="
        source venv_deepseek/bin/activate
        python3 jarvis_architecture_multi_fenetres.py
        ;;
    3)
        echo ""
        echo "🖥️ DÉMARRAGE ELECTRON"
        echo "===================="
        electron jarvis_electron_complete_m4.js
        ;;
    4)
        echo ""
        echo "📊 STATUT DES SERVICES"
        echo "====================="
        echo "🔍 Vérification des ports..."
        
        ports=(7866 7867 7868 7869 7874 7876 7877 7878 7879 7880)
        for port in "${ports[@]}"; do
            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
                echo "✅ Port $port: ACTIF"
            else
                echo "❌ Port $port: LIBRE"
            fi
        done
        
        echo ""
        echo "🤖 Processus JARVIS:"
        ps aux | grep python | grep jarvis | head -5
        
        echo ""
        read -p "Appuyez sur Entrée pour continuer..."
        ;;
    5)
        echo ""
        echo "🔧 MAINTENANCE JARVIS M4"
        echo "======================="
        echo "1. 🧹 Nettoyer les logs"
        echo "2. 📦 Mettre à jour les dépendances"
        echo "3. 🔄 Redémarrer tous les services"
        echo "4. 📊 Vérifier l'intégrité"
        echo ""
        
        read -p "Choisissez une option (1-4): " maint_choice
        
        case $maint_choice in
            1)
                echo "🧹 Nettoyage des logs..."
                rm -f logs/*.log 2>/dev/null
                echo "✅ Logs nettoyés"
                ;;
            2)
                echo "📦 Mise à jour des dépendances..."
                npm update
                source venv_deepseek/bin/activate
                pip install --upgrade -r requirements.txt 2>/dev/null
                echo "✅ Dépendances mises à jour"
                ;;
            3)
                echo "🔄 Redémarrage des services..."
                pkill -f "python.*jarvis" 2>/dev/null
                pkill -f "electron.*jarvis" 2>/dev/null
                sleep 2
                echo "✅ Services arrêtés"
                ;;
            4)
                echo "📊 Vérification de l'intégrité..."
                echo "✅ package.json: $([ -f package.json ] && echo "OK" || echo "MANQUANT")"
                echo "✅ jarvis_electron_complete_m4.js: $([ -f jarvis_electron_complete_m4.js ] && echo "OK" || echo "MANQUANT")"
                echo "✅ jarvis_architecture_multi_fenetres.py: $([ -f jarvis_architecture_multi_fenetres.py ] && echo "OK" || echo "MANQUANT")"
                echo "✅ venv_deepseek: $([ -d venv_deepseek ] && echo "OK" || echo "MANQUANT")"
                ;;
        esac
        
        echo ""
        read -p "Appuyez sur Entrée pour continuer..."
        ;;
    6)
        echo ""
        echo "👋 Au revoir Jean-Luc !"
        exit 0
        ;;
    *)
        echo ""
        echo "❌ Option invalide"
        read -p "Appuyez sur Entrée pour fermer..."
        exit 1
        ;;
esac

echo ""
echo "🎉 Opération terminée"
read -p "Appuyez sur Entrée pour fermer..."
