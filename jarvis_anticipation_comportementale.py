#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS ANTICIPATION COMPORTEMENTALE
Jean-Luc <PERSON> - 2025
Système d'anticipation et de prédiction comportementale proactive
"""

import json
import os
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict

class JarvisAnticipationComportementale:
    """Système d'anticipation comportementale proactive pour JARVIS"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS Anticipation Comportementale"
        self.version = "1.0.0"
        
        # Patterns comportementaux détectés
        self.patterns_utilisateur = {
            'horaires_activite': {},  # {heure: frequence_activite}
            'sequences_actions': [],  # [action1, action2, action3] -> probabilité
            'preferences_contextuelles': {},  # {contexte: preferences}
            'cycles_productivite': {},  # {periode: niveau_productivite}
            'habitudes_recurrentes': [],  # [{pattern, frequence, derniere_occurrence}]
            'reactions_emotionnelles': {}  # {situation: emotion_probable}
        }
        
        # Prédictions actives
        self.predictions_actives = {
            'prochaine_action': None,
            'besoin_probable': None,
            'moment_optimal': None,
            'contexte_predit': None,
            'niveau_confiance': 0.0
        }
        
        # Historique des interactions
        self.historique_interactions = []
        
        # Contexte actuel
        self.contexte_actuel = {
            'heure': datetime.now().hour,
            'jour_semaine': datetime.now().weekday(),
            'periode_journee': self._determiner_periode_journee(),
            'activite_en_cours': None,
            'humeur_detectee': 'neutre',
            'niveau_stress': 0.5,
            'productivite_estimee': 0.7
        }
        
        # Seuils de confiance pour les prédictions
        self.seuils_confiance = {
            'action_immediate': 0.7,
            'suggestion_proactive': 0.6,
            'alerte_preventive': 0.8,
            'adaptation_automatique': 0.9
        }
        
        # Charger les données
        self.load_anticipation_data()
    
    def load_anticipation_data(self):
        """Charge les données d'anticipation"""
        try:
            if os.path.exists('jarvis_anticipation_comportementale.json'):
                with open('jarvis_anticipation_comportementale.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    self.patterns_utilisateur = data.get('patterns_utilisateur', self.patterns_utilisateur)
                    self.historique_interactions = data.get('historique_interactions', [])
                    self.contexte_actuel = data.get('contexte_actuel', self.contexte_actuel)
                    
        except Exception as e:
            print(f"❌ Erreur chargement anticipation: {e}")
    
    def save_anticipation_data(self):
        """Sauvegarde les données d'anticipation"""
        try:
            data = {
                'patterns_utilisateur': self.patterns_utilisateur,
                'historique_interactions': self.historique_interactions,
                'contexte_actuel': self.contexte_actuel,
                'last_update': datetime.now().isoformat()
            }
            
            with open('jarvis_anticipation_comportementale.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde anticipation: {e}")
    
    def _determiner_periode_journee(self) -> str:
        """Détermine la période de la journée"""
        heure = datetime.now().hour
        
        if 5 <= heure < 9:
            return "matin_debut"
        elif 9 <= heure < 12:
            return "matin_travail"
        elif 12 <= heure < 14:
            return "midi_pause"
        elif 14 <= heure < 18:
            return "apres_midi_travail"
        elif 18 <= heure < 21:
            return "soiree_detente"
        elif 21 <= heure < 24:
            return "soiree_tardive"
        else:
            return "nuit"
    
    def enregistrer_interaction(self, action: str, contexte: Dict[str, Any] = None, 
                              resultat: str = "succes", emotion: str = "neutre"):
        """Enregistre une interaction pour l'apprentissage comportemental"""
        
        if contexte is None:
            contexte = self.contexte_actuel.copy()
        
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'contexte': contexte,
            'resultat': resultat,
            'emotion': emotion,
            'heure': datetime.now().hour,
            'jour_semaine': datetime.now().weekday(),
            'periode_journee': self._determiner_periode_journee()
        }
        
        self.historique_interactions.append(interaction)
        
        # Garder seulement les 1000 dernières interactions
        if len(self.historique_interactions) > 1000:
            self.historique_interactions = self.historique_interactions[-1000:]
        
        # Mettre à jour les patterns
        self._analyser_patterns_comportementaux()
        
        # Sauvegarder
        self.save_anticipation_data()
        
        print(f"📝 Interaction enregistrée: {action} ({emotion})")
    
    def _analyser_patterns_comportementaux(self):
        """Analyse les patterns comportementaux à partir de l'historique"""
        
        if len(self.historique_interactions) < 5:
            return
        
        # Analyser les horaires d'activité
        horaires_activite = defaultdict(int)
        for interaction in self.historique_interactions[-100:]:
            heure = interaction['heure']
            horaires_activite[heure] += 1
        
        # Normaliser les fréquences
        total_interactions = sum(horaires_activite.values())
        self.patterns_utilisateur['horaires_activite'] = {
            str(heure): freq / total_interactions 
            for heure, freq in horaires_activite.items()
        }
        
        # Analyser les séquences d'actions
        sequences = []
        for i in range(len(self.historique_interactions) - 2):
            sequence = [
                self.historique_interactions[i]['action'],
                self.historique_interactions[i+1]['action'],
                self.historique_interactions[i+2]['action']
            ]
            sequences.append(sequence)
        
        # Compter les séquences fréquentes
        sequences_comptees = defaultdict(int)
        for seq in sequences:
            seq_str = " -> ".join(seq)
            sequences_comptees[seq_str] += 1
        
        # Garder les séquences qui apparaissent au moins 2 fois
        self.patterns_utilisateur['sequences_actions'] = [
            {'sequence': seq, 'frequence': count}
            for seq, count in sequences_comptees.items() if count >= 2
        ]
        
        # Analyser les préférences contextuelles
        preferences_contexte = defaultdict(lambda: defaultdict(int))
        for interaction in self.historique_interactions[-50:]:
            periode = interaction['periode_journee']
            action = interaction['action']
            preferences_contexte[periode][action] += 1
        
        self.patterns_utilisateur['preferences_contextuelles'] = {
            periode: dict(actions) for periode, actions in preferences_contexte.items()
        }
        
        # Analyser les cycles de productivité
        productivite_par_heure = defaultdict(list)
        for interaction in self.historique_interactions[-100:]:
            if 'productivite_estimee' in interaction['contexte']:
                heure = interaction['heure']
                productivite = interaction['contexte']['productivite_estimee']
                productivite_par_heure[heure].append(productivite)
        
        # Calculer la productivité moyenne par heure
        self.patterns_utilisateur['cycles_productivite'] = {
            str(heure): sum(values) / len(values)
            for heure, values in productivite_par_heure.items() if values
        }
        
        # Analyser les réactions émotionnelles
        reactions_emotionnelles = defaultdict(lambda: defaultdict(int))
        for interaction in self.historique_interactions[-100:]:
            action = interaction['action']
            emotion = interaction['emotion']
            reactions_emotionnelles[action][emotion] += 1
        
        # Calculer l'émotion la plus probable pour chaque action
        self.patterns_utilisateur['reactions_emotionnelles'] = {}
        for action, emotions in reactions_emotionnelles.items():
            emotion_dominante = max(emotions, key=emotions.get)
            total = sum(emotions.values())
            probabilite = emotions[emotion_dominante] / total
            
            self.patterns_utilisateur['reactions_emotionnelles'][action] = {
                'emotion': emotion_dominante,
                'probabilite': probabilite
            }
    
    def predire_prochaine_action(self) -> Dict[str, Any]:
        """Prédit la prochaine action probable de l'utilisateur"""
        
        maintenant = datetime.now()
        heure_actuelle = maintenant.hour
        periode_actuelle = self._determiner_periode_journee()
        
        predictions = []
        
        # Prédiction basée sur les horaires d'activité
        horaires = self.patterns_utilisateur.get('horaires_activite', {})
        if str(heure_actuelle) in horaires:
            probabilite_activite = horaires[str(heure_actuelle)]
            predictions.append({
                'type': 'activite_horaire',
                'action': 'activite_probable',
                'probabilite': probabilite_activite,
                'raison': f'Activité fréquente à {heure_actuelle}h'
            })
        
        # Prédiction basée sur les préférences contextuelles
        preferences = self.patterns_utilisateur.get('preferences_contextuelles', {})
        if periode_actuelle in preferences:
            actions_periode = preferences[periode_actuelle]
            if actions_periode:
                action_probable = max(actions_periode, key=actions_periode.get)
                total_actions = sum(actions_periode.values())
                probabilite = actions_periode[action_probable] / total_actions
                
                predictions.append({
                    'type': 'preference_contextuelle',
                    'action': action_probable,
                    'probabilite': probabilite,
                    'raison': f'Action fréquente en {periode_actuelle}'
                })
        
        # Prédiction basée sur les séquences d'actions
        if len(self.historique_interactions) >= 2:
            derniere_action = self.historique_interactions[-1]['action']
            avant_derniere = self.historique_interactions[-2]['action']
            
            for seq_data in self.patterns_utilisateur.get('sequences_actions', []):
                sequence = seq_data['sequence']
                if sequence.startswith(f"{avant_derniere} -> {derniere_action}"):
                    prochaine_action = sequence.split(" -> ")[-1]
                    probabilite = seq_data['frequence'] / 10  # Normalisation approximative
                    
                    predictions.append({
                        'type': 'sequence_actions',
                        'action': prochaine_action,
                        'probabilite': min(probabilite, 1.0),
                        'raison': f'Séquence détectée: {sequence}'
                    })
        
        # Sélectionner la prédiction avec la plus haute probabilité
        if predictions:
            meilleure_prediction = max(predictions, key=lambda p: p['probabilite'])
            
            self.predictions_actives['prochaine_action'] = meilleure_prediction['action']
            self.predictions_actives['niveau_confiance'] = meilleure_prediction['probabilite']
            
            return meilleure_prediction
        
        return {
            'type': 'aucune_prediction',
            'action': None,
            'probabilite': 0.0,
            'raison': 'Données insuffisantes'
        }
    
    def anticiper_besoins(self) -> List[Dict[str, Any]]:
        """Anticipe les besoins probables de l'utilisateur"""
        
        besoins_anticipes = []
        maintenant = datetime.now()
        heure_actuelle = maintenant.hour
        
        # Anticiper selon les cycles de productivité
        cycles = self.patterns_utilisateur.get('cycles_productivite', {})
        if str(heure_actuelle) in cycles:
            productivite = cycles[str(heure_actuelle)]
            
            if productivite > 0.8:
                besoins_anticipes.append({
                    'type': 'optimisation_performance',
                    'besoin': 'Mode performance maximale',
                    'urgence': 'moyenne',
                    'raison': f'Pic de productivité détecté ({productivite:.1%})'
                })
            elif productivite < 0.4:
                besoins_anticipes.append({
                    'type': 'pause_recommandee',
                    'besoin': 'Pause ou détente',
                    'urgence': 'faible',
                    'raison': f'Productivité faible ({productivite:.1%})'
                })
        
        # Anticiper selon l'historique récent
        if len(self.historique_interactions) >= 5:
            dernieres_emotions = [
                interaction['emotion'] 
                for interaction in self.historique_interactions[-5:]
            ]
            
            # Si beaucoup de frustration récente
            if dernieres_emotions.count('frustre') >= 3:
                besoins_anticipes.append({
                    'type': 'assistance_technique',
                    'besoin': 'Aide ou simplification',
                    'urgence': 'elevee',
                    'raison': 'Frustration répétée détectée'
                })
            
            # Si beaucoup de satisfaction
            elif dernieres_emotions.count('satisfait') >= 3:
                besoins_anticipes.append({
                    'type': 'nouveaux_defis',
                    'besoin': 'Fonctionnalités avancées',
                    'urgence': 'faible',
                    'raison': 'Satisfaction élevée, prêt pour plus'
                })
        
        # Anticiper selon l'heure
        if 12 <= heure_actuelle <= 14:
            besoins_anticipes.append({
                'type': 'pause_dejeuner',
                'besoin': 'Pause déjeuner',
                'urgence': 'moyenne',
                'raison': 'Heure de déjeuner habituelle'
            })
        elif heure_actuelle >= 18:
            besoins_anticipes.append({
                'type': 'fin_journee',
                'besoin': 'Sauvegarde et résumé',
                'urgence': 'moyenne',
                'raison': 'Fin de journée de travail'
            })
        
        return besoins_anticipes
    
    def generer_suggestions_proactives(self) -> List[Dict[str, Any]]:
        """Génère des suggestions proactives basées sur l'anticipation"""
        
        suggestions = []
        
        # Prédire la prochaine action
        prediction = self.predire_prochaine_action()
        if prediction['probabilite'] > self.seuils_confiance['suggestion_proactive']:
            suggestions.append({
                'type': 'action_predictive',
                'titre': f"Prêt pour: {prediction['action']}",
                'description': prediction['raison'],
                'confiance': prediction['probabilite'],
                'action_suggeree': f"preparer_{prediction['action']}"
            })
        
        # Anticiper les besoins
        besoins = self.anticiper_besoins()
        for besoin in besoins:
            if besoin['urgence'] in ['moyenne', 'elevee']:
                suggestions.append({
                    'type': 'besoin_anticipe',
                    'titre': besoin['besoin'],
                    'description': besoin['raison'],
                    'urgence': besoin['urgence'],
                    'action_suggeree': f"activer_{besoin['type']}"
                })
        
        # Suggestions basées sur le contexte actuel
        periode = self._determiner_periode_journee()
        
        if periode == "matin_debut":
            suggestions.append({
                'type': 'routine_matinale',
                'titre': 'Planification de la journée',
                'description': 'Organiser les tâches prioritaires',
                'confiance': 0.8,
                'action_suggeree': 'ouvrir_agenda'
            })
        elif periode == "soiree_detente":
            suggestions.append({
                'type': 'routine_soiree',
                'titre': 'Résumé de la journée',
                'description': 'Bilan des accomplissements',
                'confiance': 0.7,
                'action_suggeree': 'generer_resume_journee'
            })
        
        # Trier par confiance/urgence
        suggestions.sort(key=lambda s: s.get('confiance', 0.5), reverse=True)
        
        return suggestions[:5]  # Top 5 suggestions
    
    def adapter_comportement_jarvis(self) -> Dict[str, Any]:
        """Adapte le comportement de JARVIS selon les patterns détectés"""
        
        adaptations = {
            'style_communication': 'standard',
            'niveau_detail': 'moyen',
            'proactivite': 'normale',
            'frequence_suggestions': 'reguliere',
            'mode_interaction': 'equilibre'
        }
        
        # Adapter selon les réactions émotionnelles
        reactions = self.patterns_utilisateur.get('reactions_emotionnelles', {})
        
        emotions_dominantes = []
        for action, reaction in reactions.items():
            if reaction['probabilite'] > 0.6:
                emotions_dominantes.append(reaction['emotion'])
        
        if emotions_dominantes.count('frustre') > emotions_dominantes.count('satisfait'):
            adaptations.update({
                'style_communication': 'simplifie',
                'niveau_detail': 'minimal',
                'proactivite': 'reduite',
                'mode_interaction': 'assistance'
            })
        elif emotions_dominantes.count('satisfait') > 2:
            adaptations.update({
                'style_communication': 'avance',
                'niveau_detail': 'detaille',
                'proactivite': 'elevee',
                'mode_interaction': 'collaboration'
            })
        
        # Adapter selon les cycles de productivité
        cycles = self.patterns_utilisateur.get('cycles_productivite', {})
        heure_actuelle = str(datetime.now().hour)
        
        if heure_actuelle in cycles:
            productivite = cycles[heure_actuelle]
            
            if productivite > 0.8:
                adaptations['frequence_suggestions'] = 'elevee'
            elif productivite < 0.4:
                adaptations['frequence_suggestions'] = 'reduite'
        
        print(f"🎯 Adaptation comportement JARVIS:")
        for aspect, valeur in adaptations.items():
            print(f"   📋 {aspect}: {valeur}")
        
        return adaptations
    
    def get_rapport_anticipation(self) -> Dict[str, Any]:
        """Génère un rapport d'anticipation complet"""
        
        prediction = self.predire_prochaine_action()
        besoins = self.anticiper_besoins()
        suggestions = self.generer_suggestions_proactives()
        adaptations = self.adapter_comportement_jarvis()
        
        rapport = {
            'contexte_actuel': self.contexte_actuel,
            'prediction_prochaine_action': prediction,
            'besoins_anticipes': besoins,
            'suggestions_proactives': suggestions,
            'adaptations_comportement': adaptations,
            'patterns_detectes': {
                'horaires_activite': len(self.patterns_utilisateur.get('horaires_activite', {})),
                'sequences_actions': len(self.patterns_utilisateur.get('sequences_actions', [])),
                'preferences_contextuelles': len(self.patterns_utilisateur.get('preferences_contextuelles', {})),
                'cycles_productivite': len(self.patterns_utilisateur.get('cycles_productivite', {})),
                'reactions_emotionnelles': len(self.patterns_utilisateur.get('reactions_emotionnelles', {}))
            },
            'historique_interactions': len(self.historique_interactions),
            'niveau_confiance_global': prediction['probabilite']
        }
        
        return rapport

def test_anticipation_comportementale():
    """Test du système d'anticipation comportementale"""
    
    print("🔮 TEST ANTICIPATION COMPORTEMENTALE JARVIS")
    print("=" * 50)
    print("👤 Jean-Luc Passave")
    print()
    
    # Créer le système d'anticipation
    anticipation = JarvisAnticipationComportementale()
    
    # Simuler des interactions
    print("📝 SIMULATION INTERACTIONS:")
    interactions_test = [
        ("developper_jarvis", "satisfait"),
        ("tester_fonctionnalites", "satisfait"),
        ("corriger_bugs", "frustre"),
        ("optimiser_code", "satisfait"),
        ("pause_cafe", "neutre"),
        ("developper_jarvis", "satisfait"),
        ("presenter_demo", "excite")
    ]
    
    for action, emotion in interactions_test:
        anticipation.enregistrer_interaction(action, emotion=emotion)
    
    # Test prédiction
    print(f"\n🔮 TEST PRÉDICTION PROCHAINE ACTION:")
    prediction = anticipation.predire_prochaine_action()
    print(f"   🎯 Action prédite: {prediction['action']}")
    print(f"   📊 Probabilité: {prediction['probabilite']:.1%}")
    print(f"   💭 Raison: {prediction['raison']}")
    
    # Test anticipation besoins
    print(f"\n🎯 TEST ANTICIPATION BESOINS:")
    besoins = anticipation.anticiper_besoins()
    for besoin in besoins:
        print(f"   📋 {besoin['besoin']} ({besoin['urgence']})")
        print(f"      💭 {besoin['raison']}")
    
    # Test suggestions proactives
    print(f"\n💡 TEST SUGGESTIONS PROACTIVES:")
    suggestions = anticipation.generer_suggestions_proactives()
    for i, suggestion in enumerate(suggestions, 1):
        print(f"   {i}. {suggestion['titre']}")
        print(f"      📝 {suggestion['description']}")
        if 'confiance' in suggestion:
            print(f"      🎯 Confiance: {suggestion['confiance']:.1%}")
    
    # Test adaptation comportement
    print(f"\n🎭 TEST ADAPTATION COMPORTEMENT:")
    adaptations = anticipation.adapter_comportement_jarvis()
    
    # Rapport final
    print(f"\n📊 RAPPORT ANTICIPATION:")
    rapport = anticipation.get_rapport_anticipation()
    patterns = rapport['patterns_detectes']
    print(f"   🔍 Patterns détectés:")
    print(f"      ⏰ Horaires activité: {patterns['horaires_activite']}")
    print(f"      🔄 Séquences actions: {patterns['sequences_actions']}")
    print(f"      🎯 Préférences contextuelles: {patterns['preferences_contextuelles']}")
    print(f"      📈 Cycles productivité: {patterns['cycles_productivite']}")
    print(f"      😊 Réactions émotionnelles: {patterns['reactions_emotionnelles']}")
    print(f"   📊 Interactions totales: {rapport['historique_interactions']}")
    print(f"   🎯 Confiance globale: {rapport['niveau_confiance_global']:.1%}")
    
    print(f"\n✅ ANTICIPATION COMPORTEMENTALE TESTÉE!")
    print(f"🔮 JARVIS peut maintenant anticiper vos besoins!")

if __name__ == "__main__":
    test_anticipation_comportementale()
