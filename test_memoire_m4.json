{"neuron_memories": {"2025-06-20": [{"id": "9f28d924-dabe-45b1-9cdd-62bca1527254", "content": "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007238", "tags": ["test", "m4", "optimisation", "batch_0"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "2fbb2cfb-a049-4a0b-9e17-6e09fcf097fe", "content": "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007256", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "e4a9d694-3f48-475d-83b8-090b6c791203", "content": "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007266", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "697cfc05-af3b-4f48-81ab-4e59e81d3acc", "content": "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007272", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c7338af8-d0d3-4267-823d-181b22a160c8", "content": "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007278", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c7f233d7-a446-45e7-b9d1-87dfa86b5bb6", "content": "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007284", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "f8c8a700-bc97-4942-a697-4198d3dd1afd", "content": "Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007289", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "3674394a-f619-4d5f-b783-75e157912a47", "content": "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007294", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "28e5918b-43a8-4587-b773-d4f10826ae1b", "content": "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007298", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "ce1936ef-fce3-4efa-aac2-515f0f7e4d75", "content": "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007303", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "1dc35616-634d-4e76-b9ea-807eee9f6187", "content": "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007307", "tags": ["test", "m4", "optimisation", "batch_1"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "1f682834-8657-479a-a1a0-9c10a88c3451", "content": "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007313", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "8f392766-caec-4476-a48d-01ed8e270037", "content": "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007319", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "777f2f60-67fa-48f1-87d9-38672c715654", "content": "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007324", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "cbcc1c63-d4b1-4340-9370-38a9b9bc6265", "content": "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007329", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "bef34a6f-a7cf-4245-9693-9d95bfa62ed1", "content": "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007333", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "37a0b608-b0ed-42d8-8414-44ca41c330a4", "content": "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007338", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "4a5f6f0e-9541-4ada-b2fa-7570e2aafee0", "content": "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007342", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "1800a394-f9df-4e40-a632-45754df02266", "content": "Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007346", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "6034e796-aa8b-48d0-a055-a48fb647570f", "content": "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007352", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "35552423-7585-45f2-81ea-c4697eb153fa", "content": "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007356", "tags": ["test", "m4", "optimisation", "batch_2"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "b470efcf-f3e9-4170-940e-5648467dde07", "content": "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007360", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "4a7b599c-fd16-4849-a734-c9a27a101a95", "content": "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007367", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "9f677d8a-1446-462f-9118-870bc927d38c", "content": "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007373", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "5ddb1123-5623-4008-adb8-30a14a5f486a", "content": "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007377", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "7084b7ea-532f-4105-813e-33d9d736ca73", "content": "Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007382", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "aef45dfb-918d-47f3-8488-f777a1868823", "content": "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007386", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "3eb5a9b2-3749-473e-96cb-45680a61d86f", "content": "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007390", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "3bf4a8a4-871f-4e21-93bd-d93c50f3518c", "content": "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007395", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "ff414ae9-24f3-45db-87f4-87cc8eb07250", "content": "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007399", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "771535e8-0d4a-4462-9bc7-995a82faee9d", "content": "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007403", "tags": ["test", "m4", "optimisation", "batch_3"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "171241f9-3a59-4ef4-99e8-9e3bf88a7e45", "content": "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007408", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c8fb5db8-2f65-41b6-abe0-ad8e121ae885", "content": "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007412", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "cb83b786-625e-4a4e-a11e-e760cb0b5ac9", "content": "Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007416", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "38c3381f-e09d-4185-833c-a26e67a4678e", "content": "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007420", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "7b2929e0-c706-46b4-9acb-065d53c3afac", "content": "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007424", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "2bab8697-04dd-4890-a3df-c4327684de79", "content": "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007428", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "ac170373-bbe5-41fc-bb5e-3d9dfad11549", "content": "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007433", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "71ce593e-a1e6-4afe-9b66-b882be0e410f", "content": "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007438", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "0f7505df-ce26-49c8-8397-62201882f47d", "content": "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007442", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c7f21fd4-618f-4c5f-b51e-4dd5f25e244b", "content": "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007447", "tags": ["test", "m4", "optimisation", "batch_4"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "4dd31a5f-b5e8-4173-8539-9081bc1d11e6", "content": "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007451", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "db136089-be10-4dff-a212-d0123c1eb376", "content": "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007456", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "eda7ac66-3e31-4b15-8f56-2460a0691584", "content": "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007461", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "7620649d-7b34-4bb7-b368-257fef33e285", "content": "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007466", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "f399f80f-7e36-4206-a4ae-e18baf6cada2", "content": "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007470", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b2af166f-5d88-4145-8246-4e01338c865d", "content": "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007474", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "12e532c3-b582-42b8-aa35-25a6b66c9b2b", "content": "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007479", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "5c50f78c-ba26-4f84-a114-20950aaf944e", "content": "Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007483", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b873cea5-9d44-4484-80ed-5a2cb706b2d2", "content": "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007487", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "8bb8767c-25b6-4c0a-b4ff-8ffc718b6b8a", "content": "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007491", "tags": ["test", "m4", "optimisation", "batch_5"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "fa80479b-690e-414f-8ddd-c206eed71a45", "content": "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007496", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "6ea42a74-1de6-4a93-b323-6ee6c122e97e", "content": "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007500", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "9ef738b8-7bd8-466c-a1fe-2d194e9e4d5f", "content": "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007504", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "ba1a78d6-c9a3-4343-9ecc-991a4289895c", "content": "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007509", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "5bbaae1b-f87d-4544-b5b9-ef7a28a0c361", "content": "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007513", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "6458e855-ab58-4d5d-bf0c-8c5bc86d6324", "content": "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007517", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b293a68e-75bc-4eb5-b33a-b5c6a475b0e1", "content": "Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007521", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "2d2d2045-681a-4893-92be-562e3ae17664", "content": "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007526", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "d3a4b275-68dc-457f-b12f-f6754a009b7f", "content": "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007530", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "7d9c891f-b494-4ad7-94de-3b35de79a931", "content": "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007534", "tags": ["test", "m4", "optimisation", "batch_6"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "1f6d07c8-4091-46d7-8789-c0e42f75fa21", "content": "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007562", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "864b66c9-9d4b-46ec-bbed-f53d92866cf4", "content": "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007567", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "a2d252e2-44b1-4ad2-8889-2d2a8151f1f1", "content": "Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007571", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "82a1ddfb-eab1-4927-94b4-e5dbf67c43b0", "content": "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007575", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "cec7df35-7407-4172-9a0e-d3224c557891", "content": "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007580", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "f5aeacd6-c5ae-4293-8cc3-4a0207bfae87", "content": "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007584", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b6eba323-59f6-4a84-9c10-43b8d4e10f71", "content": "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007588", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "3ed70a40-b15c-4b01-944f-4be95c198b48", "content": "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007592", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "dd3fc3ca-781d-46df-99d0-8fc61a93d461", "content": "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007596", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c247a4b4-3826-4534-bbd8-daaa711ea6f9", "content": "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007600", "tags": ["test", "m4", "optimisation", "batch_7"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "f9cbde01-0580-4f75-98e3-5aa25b5ae629", "content": "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007605", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "feb5bef4-251a-473b-817a-8cfbfa305cc7", "content": "Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007609", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c9f0ddf1-bd76-4bab-9548-247b24d1479f", "content": "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007613", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "5c296c6e-2ead-4ea4-902b-a74cce0bf099", "content": "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007736", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "c400e6de-0bfd-4e61-9fae-a942af99ecbf", "content": "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007741", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b129530d-06c0-4151-b735-a131bb9050d5", "content": "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007746", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "5366833b-f3a3-497c-8489-738e282215e2", "content": "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007754", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "29992ef8-c94e-4776-9a3a-76a65db6cbb3", "content": "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007758", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "ad8706be-0cd4-41e1-92cb-069e87ede708", "content": "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007762", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "a3920511-46d0-4e20-9a09-15a6b911edfd", "content": "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007767", "tags": ["test", "m4", "optimisation", "batch_8"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "884ef894-91ec-43f1-9b52-235410e7fe5b", "content": "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007772", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "cbe455ed-f34c-47a0-84e4-13326ce5cf0a", "content": "Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007777", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "0af46e72-15bf-4181-a7e1-853aae820035", "content": "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007781", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "daf8e807-a057-43aa-a3f1-9de882643001", "content": "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007785", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "80c961d9-a5e6-484b-a7ce-ab472373c069", "content": "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007789", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "2dacebf0-222b-47d9-bc43-d18d74ad22c2", "content": "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007795", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "ba0f8ed4-f5d1-4dc3-8cda-8d9c24f0a411", "content": "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007801", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b099de52-5ad0-4b5c-b3de-d3654ac078fd", "content": "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007805", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "1b869b31-1b14-4dd5-8b64-e1f88d4485fa", "content": "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007810", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "e09a8622-e987-4bc6-8361-b61dec1fed81", "content": "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007814", "tags": ["test", "m4", "optimisation", "batch_9"], "important": true, "emotional_context": "test_performance", "version": 1}, {"id": "e7192782-954b-4c3d-930d-a59979eb11f4", "content": "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007818", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "b7eb37cc-ced1-4003-ab21-a19086bb74c0", "content": "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007822", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "46a8cebd-9919-438a-a1ef-4a18d3e616c9", "content": "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007826", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "eb3af1c7-da9c-4fa5-ad21-516e10b33e0c", "content": "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007831", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "071837a8-e17b-4ba0-a565-75c6456844d3", "content": "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007835", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "8acb1639-71f0-4c20-8ac3-00a70ff63b35", "content": "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007839", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "261dff68-2d65-4cba-871b-a4dbe7adcb14", "content": "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007843", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "219516ae-ad43-4333-90a6-5f8d1d04faeb", "content": "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007847", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, {"id": "3265a574-597d-4bb6-a80d-b04dc7395b8d", "content": "Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007852", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}]}, "index_global": {"test": ["Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "m4": ["Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "optimisation": ["Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_0": ["Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_1": ["Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_2": ["Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_3": ["Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_4": ["Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_5": ["Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_6": ["Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_7": ["Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_8": ["Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"], "batch_9": ["Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"]}, "cache_lru": {"Test optimisation M4 #0 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "9f28d924-dabe-45b1-9cdd-62bca1527254", "content": "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007238", "tags": ["test", "m4", "optimisation", "batch_0"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007252, "access_count": 1}, "Test optimisation M4 #1 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "2fbb2cfb-a049-4a0b-9e17-6e09fcf097fe", "content": "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007256", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007263, "access_count": 1}, "Test optimisation M4 #2 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "e4a9d694-3f48-475d-83b8-090b6c791203", "content": "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007266", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0072699, "access_count": 1}, "Test optimisation M4 #3 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "697cfc05-af3b-4f48-81ab-4e59e81d3acc", "content": "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007272", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007276, "access_count": 1}, "Test optimisation M4 #4 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c7338af8-d0d3-4267-823d-181b22a160c8", "content": "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007278", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007282, "access_count": 1}, "Test optimisation M4 #5 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c7f233d7-a446-45e7-b9d1-87dfa86b5bb6", "content": "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007284", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007287, "access_count": 1}, "Test optimisation M4 #6 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "f8c8a700-bc97-4942-a697-4198d3dd1afd", "content": "Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007289", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0072918, "access_count": 1}, "Test optimisation M4 #7 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "3674394a-f619-4d5f-b783-75e157912a47", "content": "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007294", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007297, "access_count": 1}, "Test optimisation M4 #8 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "28e5918b-43a8-4587-b773-d4f10826ae1b", "content": "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007298", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007301, "access_count": 1}, "Test optimisation M4 #9 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "ce1936ef-fce3-4efa-aac2-515f0f7e4d75", "content": "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007303", "tags": ["test", "m4", "optimisation", "batch_0"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007306, "access_count": 1}, "Test optimisation M4 #10 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "1dc35616-634d-4e76-b9ea-807eee9f6187", "content": "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007307", "tags": ["test", "m4", "optimisation", "batch_1"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007311, "access_count": 1}, "Test optimisation M4 #11 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "1f682834-8657-479a-a1a0-9c10a88c3451", "content": "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007313", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007317, "access_count": 1}, "Test optimisation M4 #12 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "8f392766-caec-4476-a48d-01ed8e270037", "content": "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007319", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0073228, "access_count": 1}, "Test optimisation M4 #13 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "777f2f60-67fa-48f1-87d9-38672c715654", "content": "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007324", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007328, "access_count": 1}, "Test optimisation M4 #14 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "cbcc1c63-d4b1-4340-9370-38a9b9bc6265", "content": "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007329", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007332, "access_count": 1}, "Test optimisation M4 #15 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "bef34a6f-a7cf-4245-9693-9d95bfa62ed1", "content": "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007333", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007336, "access_count": 1}, "Test optimisation M4 #16 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "37a0b608-b0ed-42d8-8414-44ca41c330a4", "content": "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007338", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0073411, "access_count": 1}, "Test optimisation M4 #17 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "4a5f6f0e-9541-4ada-b2fa-7570e2aafee0", "content": "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007342", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007345, "access_count": 1}, "Test optimisation M4 #18 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "1800a394-f9df-4e40-a632-45754df02266", "content": "Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007346", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00735, "access_count": 1}, "Test optimisation M4 #19 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "6034e796-aa8b-48d0-a055-a48fb647570f", "content": "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007352", "tags": ["test", "m4", "optimisation", "batch_1"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007354, "access_count": 1}, "Test optimisation M4 #20 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "35552423-7585-45f2-81ea-c4697eb153fa", "content": "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007356", "tags": ["test", "m4", "optimisation", "batch_2"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007359, "access_count": 1}, "Test optimisation M4 #21 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b470efcf-f3e9-4170-940e-5648467dde07", "content": "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007360", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007364, "access_count": 1}, "Test optimisation M4 #22 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "4a7b599c-fd16-4849-a734-c9a27a101a95", "content": "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007367", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00737, "access_count": 1}, "Test optimisation M4 #23 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "9f677d8a-1446-462f-9118-870bc927d38c", "content": "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007373", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007376, "access_count": 1}, "Test optimisation M4 #24 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "5ddb1123-5623-4008-adb8-30a14a5f486a", "content": "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007377", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00738, "access_count": 1}, "Test optimisation M4 #25 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "7084b7ea-532f-4105-813e-33d9d736ca73", "content": "Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007382", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007385, "access_count": 1}, "Test optimisation M4 #26 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "aef45dfb-918d-47f3-8488-f777a1868823", "content": "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007386", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0073888, "access_count": 1}, "Test optimisation M4 #27 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "3eb5a9b2-3749-473e-96cb-45680a61d86f", "content": "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007390", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0073931, "access_count": 1}, "Test optimisation M4 #28 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "3bf4a8a4-871f-4e21-93bd-d93c50f3518c", "content": "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007395", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0073972, "access_count": 1}, "Test optimisation M4 #29 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "ff414ae9-24f3-45db-87f4-87cc8eb07250", "content": "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007399", "tags": ["test", "m4", "optimisation", "batch_2"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007402, "access_count": 1}, "Test optimisation M4 #30 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "771535e8-0d4a-4462-9bc7-995a82faee9d", "content": "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007403", "tags": ["test", "m4", "optimisation", "batch_3"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007406, "access_count": 1}, "Test optimisation M4 #31 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "171241f9-3a59-4ef4-99e8-9e3bf88a7e45", "content": "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007408", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0074098, "access_count": 1}, "Test optimisation M4 #32 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c8fb5db8-2f65-41b6-abe0-ad8e121ae885", "content": "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007412", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007415, "access_count": 1}, "Test optimisation M4 #33 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "cb83b786-625e-4a4e-a11e-e760cb0b5ac9", "content": "Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007416", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0074189, "access_count": 1}, "Test optimisation M4 #34 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "38c3381f-e09d-4185-833c-a26e67a4678e", "content": "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007420", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007423, "access_count": 1}, "Test optimisation M4 #35 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "7b2929e0-c706-46b4-9acb-065d53c3afac", "content": "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007424", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007427, "access_count": 1}, "Test optimisation M4 #36 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "2bab8697-04dd-4890-a3df-c4327684de79", "content": "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007428", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007431, "access_count": 1}, "Test optimisation M4 #37 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "ac170373-bbe5-41fc-bb5e-3d9dfad11549", "content": "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007433", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007436, "access_count": 1}, "Test optimisation M4 #38 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "71ce593e-a1e6-4afe-9b66-b882be0e410f", "content": "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007438", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0074408, "access_count": 1}, "Test optimisation M4 #39 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "0f7505df-ce26-49c8-8397-62201882f47d", "content": "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007442", "tags": ["test", "m4", "optimisation", "batch_3"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007445, "access_count": 1}, "Test optimisation M4 #40 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c7f21fd4-618f-4c5f-b51e-4dd5f25e244b", "content": "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007447", "tags": ["test", "m4", "optimisation", "batch_4"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00745, "access_count": 1}, "Test optimisation M4 #41 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "4dd31a5f-b5e8-4173-8539-9081bc1d11e6", "content": "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007451", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007454, "access_count": 1}, "Test optimisation M4 #42 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "db136089-be10-4dff-a212-d0123c1eb376", "content": "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007456", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0074592, "access_count": 1}, "Test optimisation M4 #43 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "eda7ac66-3e31-4b15-8f56-2460a0691584", "content": "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007461", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007464, "access_count": 1}, "Test optimisation M4 #44 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "7620649d-7b34-4bb7-b368-257fef33e285", "content": "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007466", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007469, "access_count": 1}, "Test optimisation M4 #45 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "f399f80f-7e36-4206-a4ae-e18baf6cada2", "content": "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007470", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007473, "access_count": 1}, "Test optimisation M4 #46 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b2af166f-5d88-4145-8246-4e01338c865d", "content": "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007474", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007477, "access_count": 1}, "Test optimisation M4 #47 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "12e532c3-b582-42b8-aa35-25a6b66c9b2b", "content": "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007479", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007482, "access_count": 1}, "Test optimisation M4 #48 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "5c50f78c-ba26-4f84-a114-20950aaf944e", "content": "Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007483", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0074859, "access_count": 1}, "Test optimisation M4 #49 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b873cea5-9d44-4484-80ed-5a2cb706b2d2", "content": "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007487", "tags": ["test", "m4", "optimisation", "batch_4"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0074902, "access_count": 1}, "Test optimisation M4 #50 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "8bb8767c-25b6-4c0a-b4ff-8ffc718b6b8a", "content": "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007491", "tags": ["test", "m4", "optimisation", "batch_5"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007494, "access_count": 1}, "Test optimisation M4 #51 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "fa80479b-690e-414f-8ddd-c206eed71a45", "content": "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007496", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007499, "access_count": 1}, "Test optimisation M4 #52 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "6ea42a74-1de6-4a93-b323-6ee6c122e97e", "content": "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007500", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007503, "access_count": 1}, "Test optimisation M4 #53 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "9ef738b8-7bd8-466c-a1fe-2d194e9e4d5f", "content": "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007504", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0075068, "access_count": 1}, "Test optimisation M4 #54 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "ba1a78d6-c9a3-4343-9ecc-991a4289895c", "content": "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007509", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007512, "access_count": 1}, "Test optimisation M4 #55 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "5bbaae1b-f87d-4544-b5b9-ef7a28a0c361", "content": "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007513", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007516, "access_count": 1}, "Test optimisation M4 #56 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "6458e855-ab58-4d5d-bf0c-8c5bc86d6324", "content": "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007517", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00752, "access_count": 1}, "Test optimisation M4 #57 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b293a68e-75bc-4eb5-b33a-b5c6a475b0e1", "content": "Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007521", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007524, "access_count": 1}, "Test optimisation M4 #58 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "2d2d2045-681a-4893-92be-562e3ae17664", "content": "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007526", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0075278, "access_count": 1}, "Test optimisation M4 #59 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "d3a4b275-68dc-457f-b12f-f6754a009b7f", "content": "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007530", "tags": ["test", "m4", "optimisation", "batch_5"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0075321, "access_count": 1}, "Test optimisation M4 #60 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "7d9c891f-b494-4ad7-94de-3b35de79a931", "content": "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007534", "tags": ["test", "m4", "optimisation", "batch_6"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00756, "access_count": 1}, "Test optimisation M4 #61 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "1f6d07c8-4091-46d7-8789-c0e42f75fa21", "content": "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007562", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007565, "access_count": 1}, "Test optimisation M4 #62 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "864b66c9-9d4b-46ec-bbed-f53d92866cf4", "content": "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007567", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00757, "access_count": 1}, "Test optimisation M4 #63 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "a2d252e2-44b1-4ad2-8889-2d2a8151f1f1", "content": "Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007571", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007574, "access_count": 1}, "Test optimisation M4 #64 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "82a1ddfb-eab1-4927-94b4-e5dbf67c43b0", "content": "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007575", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0075781, "access_count": 1}, "Test optimisation M4 #65 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "cec7df35-7407-4172-9a0e-d3224c557891", "content": "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007580", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007583, "access_count": 1}, "Test optimisation M4 #66 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "f5aeacd6-c5ae-4293-8cc3-4a0207bfae87", "content": "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007584", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007587, "access_count": 1}, "Test optimisation M4 #67 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b6eba323-59f6-4a84-9c10-43b8d4e10f71", "content": "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007588", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007591, "access_count": 1}, "Test optimisation M4 #68 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "3ed70a40-b15c-4b01-944f-4be95c198b48", "content": "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007592", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007595, "access_count": 1}, "Test optimisation M4 #69 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "dd3fc3ca-781d-46df-99d0-8fc61a93d461", "content": "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007596", "tags": ["test", "m4", "optimisation", "batch_6"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007599, "access_count": 1}, "Test optimisation M4 #70 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c247a4b4-3826-4534-bbd8-daaa711ea6f9", "content": "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007600", "tags": ["test", "m4", "optimisation", "batch_7"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007603, "access_count": 1}, "Test optimisation M4 #71 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "f9cbde01-0580-4f75-98e3-5aa25b5ae629", "content": "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007605", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0076082, "access_count": 1}, "Test optimisation M4 #72 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "feb5bef4-251a-473b-817a-8cfbfa305cc7", "content": "Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007609", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007612, "access_count": 1}, "Test optimisation M4 #73 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c9f0ddf1-bd76-4bab-9548-247b24d1479f", "content": "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007613", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007734, "access_count": 1}, "Test optimisation M4 #74 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "5c296c6e-2ead-4ea4-902b-a74cce0bf099", "content": "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007736", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00774, "access_count": 1}, "Test optimisation M4 #75 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "c400e6de-0bfd-4e61-9fae-a942af99ecbf", "content": "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007741", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007744, "access_count": 1}, "Test optimisation M4 #76 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b129530d-06c0-4151-b735-a131bb9050d5", "content": "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007746", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007752, "access_count": 1}, "Test optimisation M4 #77 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "5366833b-f3a3-497c-8489-738e282215e2", "content": "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007754", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007757, "access_count": 1}, "Test optimisation M4 #78 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "29992ef8-c94e-4776-9a3a-76a65db6cbb3", "content": "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007758", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007761, "access_count": 1}, "Test optimisation M4 #79 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "ad8706be-0cd4-41e1-92cb-069e87ede708", "content": "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007762", "tags": ["test", "m4", "optimisation", "batch_7"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007765, "access_count": 1}, "Test optimisation M4 #80 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "a3920511-46d0-4e20-9a09-15a6b911edfd", "content": "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007767", "tags": ["test", "m4", "optimisation", "batch_8"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0077708, "access_count": 1}, "Test optimisation M4 #81 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "884ef894-91ec-43f1-9b52-235410e7fe5b", "content": "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007772", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007775, "access_count": 1}, "Test optimisation M4 #82 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "cbe455ed-f34c-47a0-84e4-13326ce5cf0a", "content": "Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007777", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0077791, "access_count": 1}, "Test optimisation M4 #83 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "0af46e72-15bf-4181-a7e1-853aae820035", "content": "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007781", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007784, "access_count": 1}, "Test optimisation M4 #84 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "daf8e807-a057-43aa-a3f1-9de882643001", "content": "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007785", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007788, "access_count": 1}, "Test optimisation M4 #85 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "80c961d9-a5e6-484b-a7ce-ab472373c069", "content": "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007789", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007793, "access_count": 1}, "Test optimisation M4 #86 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "2dacebf0-222b-47d9-bc43-d18d74ad22c2", "content": "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007795", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007798, "access_count": 1}, "Test optimisation M4 #87 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "ba0f8ed4-f5d1-4dc3-8cda-8d9c24f0a411", "content": "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007801", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007804, "access_count": 1}, "Test optimisation M4 #88 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b099de52-5ad0-4b5c-b3de-d3654ac078fd", "content": "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007805", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007808, "access_count": 1}, "Test optimisation M4 #89 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "1b869b31-1b14-4dd5-8b64-e1f88d4485fa", "content": "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007810", "tags": ["test", "m4", "optimisation", "batch_8"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007812, "access_count": 1}, "Test optimisation M4 #90 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "e09a8622-e987-4bc6-8361-b61dec1fed81", "content": "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007814", "tags": ["test", "m4", "optimisation", "batch_9"], "important": true, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007817, "access_count": 1}, "Test optimisation M4 #91 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "e7192782-954b-4c3d-930d-a59979eb11f4", "content": "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007818", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007821, "access_count": 1}, "Test optimisation M4 #92 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "b7eb37cc-ced1-4003-ab21-a19086bb74c0", "content": "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007822", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007825, "access_count": 1}, "Test optimisation M4 #93 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "46a8cebd-9919-438a-a1ef-4a18d3e616c9", "content": "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007826", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007829, "access_count": 1}, "Test optimisation M4 #94 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "eb3af1c7-da9c-4fa5-ad21-516e10b33e0c", "content": "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007831", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007833, "access_count": 1}, "Test optimisation M4 #95 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "071837a8-e17b-4ba0-a565-75c6456844d3", "content": "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007835", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007838, "access_count": 1}, "Test optimisation M4 #96 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "8acb1639-71f0-4c20-8ac3-00a70ff63b35", "content": "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007839", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007842, "access_count": 1}, "Test optimisation M4 #97 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "261dff68-2d65-4cba-871b-a4dbe7adcb14", "content": "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007843", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.0078459, "access_count": 1}, "Test optimisation M4 #98 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "219516ae-ad43-4333-90a6-5f8d1d04faeb", "content": "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007847", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.00785, "access_count": 1}, "Test optimisation M4 #99 - Jean-Luc Passave utilise les P-cores et E-cores": {"data": {"id": "3265a574-597d-4bb6-a80d-b04dc7395b8d", "content": "Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "timestamp": "2025-06-20T16:37:11.007852", "tags": ["test", "m4", "optimisation", "batch_9"], "important": false, "emotional_context": "test_performance", "version": 1}, "access_time": 1750451831.007854, "access_count": 1}}, "thermal_zones": {"important": ["Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"]}, "emotional_tags": {"test_performance": ["Test optimisation M4 #99 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #18 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #71 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #69 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #95 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #16 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #24 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #26 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #20 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #6 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #43 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #68 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #62 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #15 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #96 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #75 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #9 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #4 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #92 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #13 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #86 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #64 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #39 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #54 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #67 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #63 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #27 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #70 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #66 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #79 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #53 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #55 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #47 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #80 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #49 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #2 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #58 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #87 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #56 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #91 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #51 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #22 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #78 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #33 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #31 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #28 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #84 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #97 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #32 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #17 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #7 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #36 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #83 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #8 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #14 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #11 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #29 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #81 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #0 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #41 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #60 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #37 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #46 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #48 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #40 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #74 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #5 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #45 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #23 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #42 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #3 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #21 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #90 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #38 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #10 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #50 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #94 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #93 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #88 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #19 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #30 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #44 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #89 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #73 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #59 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #57 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #34 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #35 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #85 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #77 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #52 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #72 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #12 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #82 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #1 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #98 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #76 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #65 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #61 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores", "Test optimisation M4 #25 - <PERSON><PERSON><PERSON> utilise les P-cores et E-cores"]}, "timestamps": {"Test optimisation M4 #0 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007238", "Test optimisation M4 #1 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007256", "Test optimisation M4 #2 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007266", "Test optimisation M4 #3 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007272", "Test optimisation M4 #4 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007278", "Test optimisation M4 #5 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007284", "Test optimisation M4 #6 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007289", "Test optimisation M4 #7 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007294", "Test optimisation M4 #8 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007298", "Test optimisation M4 #9 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007303", "Test optimisation M4 #10 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007307", "Test optimisation M4 #11 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007313", "Test optimisation M4 #12 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007319", "Test optimisation M4 #13 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007324", "Test optimisation M4 #14 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007329", "Test optimisation M4 #15 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007333", "Test optimisation M4 #16 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007338", "Test optimisation M4 #17 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007342", "Test optimisation M4 #18 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007346", "Test optimisation M4 #19 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007352", "Test optimisation M4 #20 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007356", "Test optimisation M4 #21 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007360", "Test optimisation M4 #22 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007367", "Test optimisation M4 #23 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007373", "Test optimisation M4 #24 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007377", "Test optimisation M4 #25 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007382", "Test optimisation M4 #26 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007386", "Test optimisation M4 #27 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007390", "Test optimisation M4 #28 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007395", "Test optimisation M4 #29 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007399", "Test optimisation M4 #30 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007403", "Test optimisation M4 #31 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007408", "Test optimisation M4 #32 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007412", "Test optimisation M4 #33 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007416", "Test optimisation M4 #34 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007420", "Test optimisation M4 #35 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007424", "Test optimisation M4 #36 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007428", "Test optimisation M4 #37 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007433", "Test optimisation M4 #38 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007438", "Test optimisation M4 #39 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007442", "Test optimisation M4 #40 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007447", "Test optimisation M4 #41 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007451", "Test optimisation M4 #42 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007456", "Test optimisation M4 #43 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007461", "Test optimisation M4 #44 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007466", "Test optimisation M4 #45 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007470", "Test optimisation M4 #46 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007474", "Test optimisation M4 #47 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007479", "Test optimisation M4 #48 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007483", "Test optimisation M4 #49 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007487", "Test optimisation M4 #50 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007491", "Test optimisation M4 #51 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007496", "Test optimisation M4 #52 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007500", "Test optimisation M4 #53 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007504", "Test optimisation M4 #54 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007509", "Test optimisation M4 #55 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007513", "Test optimisation M4 #56 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007517", "Test optimisation M4 #57 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007521", "Test optimisation M4 #58 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007526", "Test optimisation M4 #59 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007530", "Test optimisation M4 #60 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007534", "Test optimisation M4 #61 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007562", "Test optimisation M4 #62 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007567", "Test optimisation M4 #63 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007571", "Test optimisation M4 #64 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007575", "Test optimisation M4 #65 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007580", "Test optimisation M4 #66 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007584", "Test optimisation M4 #67 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007588", "Test optimisation M4 #68 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007592", "Test optimisation M4 #69 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007596", "Test optimisation M4 #70 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007600", "Test optimisation M4 #71 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007605", "Test optimisation M4 #72 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007609", "Test optimisation M4 #73 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007613", "Test optimisation M4 #74 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007736", "Test optimisation M4 #75 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007741", "Test optimisation M4 #76 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007746", "Test optimisation M4 #77 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007754", "Test optimisation M4 #78 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007758", "Test optimisation M4 #79 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007762", "Test optimisation M4 #80 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007767", "Test optimisation M4 #81 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007772", "Test optimisation M4 #82 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007777", "Test optimisation M4 #83 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007781", "Test optimisation M4 #84 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007785", "Test optimisation M4 #85 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007789", "Test optimisation M4 #86 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007795", "Test optimisation M4 #87 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007801", "Test optimisation M4 #88 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007805", "Test optimisation M4 #89 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007810", "Test optimisation M4 #90 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007814", "Test optimisation M4 #91 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007818", "Test optimisation M4 #92 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007822", "Test optimisation M4 #93 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007826", "Test optimisation M4 #94 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007831", "Test optimisation M4 #95 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007835", "Test optimisation M4 #96 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007839", "Test optimisation M4 #97 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007843", "Test optimisation M4 #98 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007847", "Test optimisation M4 #99 - Jean-Luc Passave utilise les P-cores et E-cores": "2025-06-20T16:37:11.007852"}, "user_preferences": {"frequent_words": {}, "last_analysis": "2025-06-20T16:37:11.007115"}, "notifications": [], "proactive_suggestions": [], "auto_summaries": [], "m4_optimized": true, "architecture": "apple_silicon_m4", "save_timestamp": "2025-06-20T16:37:11.013856"}