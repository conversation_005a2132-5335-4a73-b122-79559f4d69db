#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Sauvegarde Automatique JARVIS
Jean-Luc Passave - 2025
Système de sauvegarde automatique pour protéger le travail
"""

import gradio as gr
import subprocess
import os
import time
from datetime import datetime
import threading

# État global du système de sauvegarde
backup_status = {
    'auto_enabled': False,
    'last_backup': None,
    'backup_count': 0,
    'running': False
}

def create_backup(backup_location="/Volumes/seagate/Louna_Electron_Latest"):
    """Crée une sauvegarde immédiate dans le lieu choisi"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"SAUVEGARDE_JARVIS_M4_{timestamp}"

        # Créer le chemin complet de sauvegarde
        full_backup_path = os.path.join(backup_location, backup_name)

        # Vérifier que le répertoire de destination existe
        if not os.path.exists(backup_location):
            return f"❌ Erreur: Le répertoire {backup_location} n'existe pas"

        # Créer le répertoire de sauvegarde
        os.makedirs(full_backup_path, exist_ok=True)

        # Liste des fichiers critiques à sauvegarder
        critical_files = [
            'jarvis_electron_final_complet.js',
            'package.json',
            'dashboard_avec_onglets.py',
            'visualisation_memoire_thermique.py',
            'systeme_notifications_jarvis.py',
            'tableau_bord_final_ultime.py',
            'sauvegarde_automatique_jarvis.py',
            'monitoring_sante_jarvis_avance.py',
            'centre_commande_unifie_jarvis.py',
            'jarvis_architecture_multi_fenetres.py',
            'test_neurones_dynamiques.py'
        ]

        # Copier les fichiers critiques
        copied_files = 0
        for file in critical_files:
            if os.path.exists(file):
                import shutil
                shutil.copy2(file, full_backup_path)
                copied_files += 1

        # Créer un fichier d'inventaire
        inventory_path = os.path.join(full_backup_path, "INVENTAIRE_SAUVEGARDE.md")
        with open(inventory_path, 'w', encoding='utf-8') as f:
            f.write(f"""# 💾 INVENTAIRE SAUVEGARDE JARVIS M4
## Jean-Luc Passave - {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}

### 📁 LIEU DE SAUVEGARDE
**Répertoire :** {backup_location}
**Dossier :** {backup_name}
**Chemin complet :** {full_backup_path}

### 📊 STATISTIQUES
**Fichiers copiés :** {copied_files}/{len(critical_files)}
**Date création :** {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}
**Taille :** {get_folder_size(full_backup_path)} MB

### 🔄 RESTAURATION
Pour restaurer cette sauvegarde :
1. Copier tous les fichiers vers le répertoire de travail
2. Relancer les applications avec npm run final
3. Vérifier que tous les services fonctionnent

### ✅ FICHIERS SAUVEGARDÉS
""")
            for file in critical_files:
                status = "✅" if os.path.exists(os.path.join(full_backup_path, file)) else "❌"
                f.write(f"- {status} {file}\n")

        # Créer archive compressée si demandé
        import tarfile
        archive_path = f"{full_backup_path}.tar.gz"
        with tarfile.open(archive_path, "w:gz") as tar:
            tar.add(full_backup_path, arcname=backup_name)

        backup_status['last_backup'] = datetime.now().strftime("%H:%M:%S")
        backup_status['backup_count'] += 1
        backup_status['last_location'] = backup_location

        return f"✅ Sauvegarde créée avec succès !\n📁 Lieu: {backup_location}\n📦 Archive: {archive_path}\n📊 Fichiers: {copied_files}"

    except Exception as e:
        return f"❌ Erreur lors de la sauvegarde: {str(e)}"

def get_folder_size(folder_path):
    """Calcule la taille d'un dossier en MB"""
    try:
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
        return round(total_size / (1024 * 1024), 2)
    except:
        return 0

def auto_backup_worker():
    """Worker pour les sauvegardes automatiques"""
    while backup_status['auto_enabled']:
        time.sleep(300)  # Attendre 5 minutes
        if backup_status['auto_enabled']:
            create_backup()

def toggle_auto_backup(enabled):
    """Active/désactive la sauvegarde automatique"""
    backup_status['auto_enabled'] = enabled
    
    if enabled and not backup_status['running']:
        backup_status['running'] = True
        thread = threading.Thread(target=auto_backup_worker, daemon=True)
        thread.start()
        return "✅ Sauvegarde automatique activée (toutes les 5 minutes)"
    elif not enabled:
        backup_status['running'] = False
        return "⏸️ Sauvegarde automatique désactivée"
    else:
        return "ℹ️ Sauvegarde automatique déjà en cours"

def get_backup_status():
    """Récupère le statut des sauvegardes"""
    
    status_html = f"""
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h2 style='margin: 0 0 20px 0; color: #333; text-align: center;'>💾 STATUT SAUVEGARDE AUTOMATIQUE</h2>
        
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;'>
            <div style='background: {"#e8f5e8" if backup_status["auto_enabled"] else "#ffebee"}; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid {"#4CAF50" if backup_status["auto_enabled"] else "#F44336"};'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>🔄 Sauvegarde Auto</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold; color: {"#4CAF50" if backup_status["auto_enabled"] else "#F44336"};'>
                    {"✅ ACTIVÉE" if backup_status["auto_enabled"] else "❌ DÉSACTIVÉE"}
                </p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Toutes les 5 minutes</p>
            </div>
            
            <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #2196F3;'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>⏰ Dernière Sauvegarde</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold; color: #2196F3;'>
                    {backup_status["last_backup"] or "Aucune"}
                </p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Aujourd'hui</p>
            </div>
            
            <div style='background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #FF9800;'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>📊 Total Sauvegardes</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold; color: #FF9800;'>
                    {backup_status["backup_count"]}
                </p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Depuis le début</p>
            </div>
            
            <div style='background: #f3e5f5; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #9C27B0;'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>🔒 Protection</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold; color: #9C27B0;'>
                    MAXIMALE
                </p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Aucune perte possible</p>
            </div>
        </div>
        
        <div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center;'>
            <p style='margin: 0; color: #666; font-size: 0.9em;'>
                Dernière mise à jour: {datetime.now().strftime("%H:%M:%S")}
            </p>
        </div>
    </div>
    """
    
    return status_html

def list_backups():
    """Liste toutes les sauvegardes disponibles"""
    
    try:
        # Chercher les répertoires de sauvegarde
        backup_dirs = [d for d in os.listdir('.') if d.startswith('SAUVEGARDE_')]
        backup_dirs.sort(reverse=True)  # Plus récent en premier
        
        if not backup_dirs:
            return "<p style='text-align: center; color: #666;'>Aucune sauvegarde trouvée</p>"
        
        backups_html = """
        <div style='background: white; padding: 20px; border-radius: 10px; margin: 10px 0;'>
            <h3 style='margin: 0 0 15px 0; color: #333; text-align: center;'>📋 SAUVEGARDES DISPONIBLES</h3>
            <div style='max-height: 400px; overflow-y: auto;'>
        """
        
        for backup_dir in backup_dirs[:10]:  # Afficher les 10 plus récentes
            # Extraire la date du nom
            try:
                date_part = backup_dir.split('_')[-2] + '_' + backup_dir.split('_')[-1]
                date_obj = datetime.strptime(date_part, "%Y%m%d_%H%M%S")
                formatted_date = date_obj.strftime("%d/%m/%Y %H:%M:%S")
            except:
                formatted_date = "Date inconnue"
            
            # Vérifier si l'archive existe
            archive_exists = os.path.exists(f"{backup_dir}.tar.gz")
            
            backups_html += f"""
            <div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #4CAF50;'>
                <div style='display: flex; justify-content: space-between; align-items: center;'>
                    <div>
                        <h4 style='margin: 0; color: #333;'>{backup_dir}</h4>
                        <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>📅 {formatted_date}</p>
                    </div>
                    <div style='text-align: right;'>
                        <span style='background: #4CAF50; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8em;'>
                            {"📦 ARCHIVÉ" if archive_exists else "📁 DOSSIER"}
                        </span>
                    </div>
                </div>
            </div>
            """
        
        backups_html += """
            </div>
        </div>
        """
        
        return backups_html
        
    except Exception as e:
        return f"<p style='color: #f44336;'>Erreur: {str(e)}</p>"

def create_backup_interface():
    """Interface de sauvegarde automatique"""
    
    with gr.Blocks(
        title="💾 Sauvegarde Automatique JARVIS",
        theme=gr.themes.Soft()
    ) as backup_interface:

        # CSS pour les boutons colorés - JEAN-LUC PASSAVE
        gr.HTML("""
        <style>
            .gradio-button {
                background: linear-gradient(45deg, #667eea, #764ba2) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
            }
            .gradio-button:hover {
                background: linear-gradient(45deg, #764ba2, #667eea) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
            }
            .primary-btn {
                background: linear-gradient(45deg, #FF6B6B, #4ECDC4) !important;
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
            }
            .primary-btn:hover {
                background: linear-gradient(45deg, #4ECDC4, #FF6B6B) !important;
                box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4) !important;
            }
            .secondary-btn {
                background: linear-gradient(45deg, #4CAF50, #8BC34A) !important;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3) !important;
            }
            .secondary-btn:hover {
                background: linear-gradient(45deg, #8BC34A, #4CAF50) !important;
                box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
            }
            .warning-btn {
                background: linear-gradient(45deg, #FF9800, #FFC107) !important;
                box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
            }
            .warning-btn:hover {
                background: linear-gradient(45deg, #FFC107, #FF9800) !important;
                box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
            }
            .danger-btn {
                background: linear-gradient(45deg, #F44336, #E91E63) !important;
                box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3) !important;
            }
            .danger-btn:hover {
                background: linear-gradient(45deg, #E91E63, #F44336) !important;
                box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4) !important;
            }
        </style>

        <div style="text-align: center; background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39); color: white; padding: 25px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.2em;">💾 SAUVEGARDE AUTOMATIQUE JARVIS</h1>
            <p style="margin: 10px 0; font-size: 1.1em;">Protection complète de votre travail</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 1em;">👤 Jean-Luc Passave | 💾 Sauvegarde Continue | 🔒 Protection Maximale</p>
            </div>
        </div>
        """)

        with gr.Tabs():
            
            # Onglet Contrôle
            with gr.Tab("🎛️ Contrôle"):
                status_display = gr.HTML(
                    value=get_backup_status(),
                    label="Statut sauvegarde"
                )

                # CHOIX DU LIEU DE SAUVEGARDE - JEAN-LUC PASSAVE
                gr.HTML("<h3 style='color: #333; margin: 20px 0;'>📁 CHOIX DU LIEU DE SAUVEGARDE</h3>")

                with gr.Row():
                    with gr.Column():
                        backup_location = gr.Textbox(
                            label="📁 Répertoire de sauvegarde",
                            value="/Volumes/seagate/Louna_Electron_Latest",
                            placeholder="/Volumes/seagate/SAUVEGARDES_JARVIS",
                            lines=1
                        )

                        gr.HTML("""
                        <div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0;'>
                            <h4 style='margin: 0 0 10px 0; color: #1976d2;'>💡 Suggestions de lieux :</h4>
                            <ul style='margin: 0; padding-left: 20px; color: #666;'>
                                <li><strong>/Volumes/seagate/SAUVEGARDES_JARVIS</strong> - Disque externe</li>
                                <li><strong>~/Desktop/JARVIS_BACKUPS</strong> - Bureau</li>
                                <li><strong>~/Documents/JARVIS_SAUVEGARDES</strong> - Documents</li>
                                <li><strong>/Volumes/USB_BACKUP</strong> - Clé USB</li>
                            </ul>
                        </div>
                        """)

                    with gr.Column():
                        check_location_btn = gr.Button(
                            "🔍 Vérifier le Répertoire",
                            variant="secondary",
                            elem_classes=["secondary-btn"]
                        )

                        create_folder_btn = gr.Button(
                            "📁 Créer le Dossier",
                            variant="secondary",
                            elem_classes=["warning-btn"]
                        )

                        location_status = gr.HTML(
                            value="<p style='color: #666;'>Vérifiez le répertoire de sauvegarde</p>",
                            label="Statut répertoire"
                        )

                gr.HTML("<hr style='margin: 20px 0;'>")

                with gr.Row():
                    with gr.Column():
                        auto_backup_toggle = gr.Checkbox(
                            label="🔄 Sauvegarde automatique (toutes les 5 minutes)",
                            value=False
                        )

                        manual_backup_btn = gr.Button(
                            "💾 SAUVEGARDE IMMÉDIATE",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

                    with gr.Column():
                        refresh_status_btn = gr.Button(
                            "🔄 Actualiser Statut",
                            variant="secondary",
                            elem_classes=["secondary-btn"]
                        )

                backup_result = gr.Textbox(
                    label="Résultats",
                    lines=4,
                    interactive=False
                )
            
            # Onglet Historique
            with gr.Tab("📋 Historique"):
                backups_list = gr.HTML(
                    value=list_backups(),
                    label="Liste des sauvegardes"
                )
                
                refresh_list_btn = gr.Button(
                    "🔄 Actualiser Liste",
                    variant="secondary"
                )
            
            # Onglet Paramètres
            with gr.Tab("⚙️ Paramètres"):
                gr.HTML("<h2 style='text-align: center; color: #333;'>⚙️ PARAMÈTRES SAUVEGARDE</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>⏰ Fréquence</h3>")
                        
                        backup_frequency = gr.Radio(
                            label="Fréquence de sauvegarde automatique",
                            choices=["5 minutes", "15 minutes", "30 minutes", "1 heure"],
                            value="5 minutes"
                        )
                        
                        max_backups = gr.Slider(
                            label="Nombre maximum de sauvegardes à conserver",
                            minimum=5,
                            maximum=50,
                            value=20,
                            step=1
                        )
                        
                    with gr.Column():
                        gr.HTML("<h3>📁 Stockage</h3>")
                        
                        compress_backups = gr.Checkbox(
                            label="🗜️ Compresser les sauvegardes",
                            value=True
                        )
                        
                        auto_cleanup = gr.Checkbox(
                            label="🧹 Nettoyage automatique des anciennes sauvegardes",
                            value=True
                        )

                save_settings_btn = gr.Button(
                    "💾 Sauvegarder Paramètres",
                    variant="secondary"
                )
                
                settings_result = gr.Textbox(
                    label="Résultat paramètres",
                    lines=2,
                    interactive=False
                )

        # Fonctions
        def check_backup_location(location):
            """Vérifie si le répertoire de sauvegarde existe"""
            try:
                if os.path.exists(location):
                    if os.path.isdir(location):
                        # Vérifier les permissions d'écriture
                        test_file = os.path.join(location, "test_write.tmp")
                        try:
                            with open(test_file, 'w') as f:
                                f.write("test")
                            os.remove(test_file)
                            return f"✅ Répertoire valide et accessible en écriture : {location}"
                        except:
                            return f"❌ Répertoire existe mais pas d'accès en écriture : {location}"
                    else:
                        return f"❌ Le chemin existe mais n'est pas un répertoire : {location}"
                else:
                    return f"⚠️ Répertoire n'existe pas : {location}"
            except Exception as e:
                return f"❌ Erreur lors de la vérification : {str(e)}"

        def create_backup_folder(location):
            """Crée le répertoire de sauvegarde"""
            try:
                os.makedirs(location, exist_ok=True)
                return f"✅ Répertoire créé avec succès : {location}"
            except Exception as e:
                return f"❌ Erreur lors de la création : {str(e)}"

        def manual_backup_with_location(location):
            """Sauvegarde manuelle avec lieu choisi"""
            if not location.strip():
                return "❌ Veuillez spécifier un répertoire de sauvegarde"
            return create_backup(location.strip())

        def save_settings(*args):
            return "✅ Paramètres de sauvegarde sauvegardés"

        # Connexions
        auto_backup_toggle.change(
            fn=toggle_auto_backup,
            inputs=[auto_backup_toggle],
            outputs=[backup_result]
        )

        manual_backup_btn.click(
            fn=manual_backup_with_location,
            inputs=[backup_location],
            outputs=[backup_result]
        )

        check_location_btn.click(
            fn=check_backup_location,
            inputs=[backup_location],
            outputs=[location_status]
        )

        create_folder_btn.click(
            fn=create_backup_folder,
            inputs=[backup_location],
            outputs=[location_status]
        )
        
        refresh_status_btn.click(
            fn=get_backup_status,
            outputs=[status_display]
        )
        
        refresh_list_btn.click(
            fn=list_backups,
            outputs=[backups_list]
        )
        
        save_settings_btn.click(
            fn=save_settings,
            inputs=[backup_frequency, max_backups, compress_backups, auto_cleanup],
            outputs=[settings_result]
        )

        # Footer
        gr.HTML("""
        <div style='background: linear-gradient(45deg, #2196F3, #21CBF3); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>
            <h3 style='margin: 0 0 10px 0;'>🛡️ JEAN-LUC PASSAVE</h3>
            <p style='margin: 0; font-size: 1.1em;'>Votre travail JARVIS est maintenant protégé par un système de sauvegarde automatique !</p>
            <p style='margin: 10px 0 0 0; font-size: 0.9em; opacity: 0.9;'>Plus jamais de perte de données - Protection maximale garantie !</p>
        </div>
        """)

    return backup_interface

if __name__ == "__main__":
    print("💾 DÉMARRAGE SAUVEGARDE AUTOMATIQUE JARVIS")
    print("==========================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Protection automatique du travail")
    print("")
    
    # Créer et lancer l'interface
    backup_app = create_backup_interface()
    
    print("✅ Interface de sauvegarde automatique créée")
    print("🌐 Lancement sur http://localhost:7903")
    print("💾 Protection automatique disponible")
    
    backup_app.launch(
        server_name="127.0.0.1",
        server_port=7903,
        share=False,
        show_error=True,
        quiet=False
    )
