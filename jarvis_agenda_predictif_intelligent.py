#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS AGENDA PRÉDICTIF INTELLIGENT
Jean-<PERSON> - 2025
Système de planification intelligente et anticipation (grand frère passionné 😂)
"""

import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import calendar

class AgendaPredictifIntelligent:
    """Système d'agenda prédictif et de planification intelligente"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS Agenda Prédictif"
        self.version = "1.0.0"
        
        # Patterns comportementaux détectés
        self.patterns_comportementaux = {
            'horaires_travail': {},  # {jour_semaine: [heures_actives]}
            'activites_recurrentes': [],  # [{nom, frequence, derniere_fois}]
            'preferences_temporelles': {},  # {type_activite: heure_preferee}
            'cycles_productivite': {},  # {periode: niveau_productivite}
            'habitudes_detectees': []  # [habitude_description]
        }
        
        # Événements planifiés et prédictions
        self.evenements = {
            'planifies': [],  # Événements explicitement planifiés
            'predits': [],    # Événements prédits par l'IA
            'recurrents': [], # Événements récurrents détectés
            'suggestions': [] # Suggestions d'optimisation
        }
        
        # Contexte et mémoire
        self.contexte_actuel = {
            'jour_semaine': datetime.now().weekday(),
            'heure_actuelle': datetime.now().hour,
            'periode_journee': self._determiner_periode(),
            'activite_en_cours': None,
            'niveau_energie_estime': 0.8
        }
        
        # Historique des activités
        self.historique_activites = []
        
        # Charger les données existantes
        self.load_agenda_data()
    
    def load_agenda_data(self):
        """Charge les données d'agenda et patterns"""
        try:
            if os.path.exists('jarvis_agenda_predictif.json'):
                with open('jarvis_agenda_predictif.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    self.patterns_comportementaux = data.get('patterns_comportementaux', self.patterns_comportementaux)
                    self.evenements = data.get('evenements', self.evenements)
                    self.historique_activites = data.get('historique_activites', [])
                    
        except Exception as e:
            print(f"❌ Erreur chargement agenda: {e}")
    
    def save_agenda_data(self):
        """Sauvegarde les données d'agenda"""
        try:
            data = {
                'patterns_comportementaux': self.patterns_comportementaux,
                'evenements': self.evenements,
                'historique_activites': self.historique_activites,
                'last_update': datetime.now().isoformat()
            }
            
            with open('jarvis_agenda_predictif.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde agenda: {e}")
    
    def _determiner_periode(self) -> str:
        """Détermine la période de la journée"""
        heure = datetime.now().hour
        
        if 5 <= heure < 12:
            return "matin"
        elif 12 <= heure < 18:
            return "apres-midi"
        elif 18 <= heure < 22:
            return "soiree"
        else:
            return "nuit"
    
    def enregistrer_activite(self, nom_activite: str, duree_minutes: int = None, 
                           productivite: float = None, notes: str = ""):
        """Enregistre une activité pour apprentissage des patterns"""
        
        maintenant = datetime.now()
        
        activite = {
            'nom': nom_activite,
            'timestamp': maintenant.isoformat(),
            'jour_semaine': maintenant.weekday(),
            'heure': maintenant.hour,
            'periode': self._determiner_periode(),
            'duree_minutes': duree_minutes,
            'productivite': productivite,  # 0.0 à 1.0
            'notes': notes
        }
        
        self.historique_activites.append(activite)
        
        # Garder seulement les 1000 dernières activités
        if len(self.historique_activites) > 1000:
            self.historique_activites = self.historique_activites[-1000:]
        
        # Mettre à jour les patterns
        self._analyser_patterns()
        
        # Sauvegarder
        self.save_agenda_data()
        
        print(f"📝 Activité enregistrée: {nom_activite}")
        return activite
    
    def _analyser_patterns(self):
        """Analyse les patterns comportementaux à partir de l'historique"""
        
        if len(self.historique_activites) < 5:
            return
        
        # Analyser les horaires de travail par jour
        horaires_par_jour = {}
        for activite in self.historique_activites[-100:]:  # 100 dernières activités
            jour = activite['jour_semaine']
            heure = activite['heure']
            
            if jour not in horaires_par_jour:
                horaires_par_jour[jour] = []
            horaires_par_jour[jour].append(heure)
        
        # Calculer les plages horaires actives
        for jour, heures in horaires_par_jour.items():
            if len(heures) >= 3:
                heures_uniques = sorted(set(heures))
                plage_debut = min(heures_uniques)
                plage_fin = max(heures_uniques)
                self.patterns_comportementaux['horaires_travail'][str(jour)] = [plage_debut, plage_fin]
        
        # Détecter les activités récurrentes
        activites_comptees = {}
        for activite in self.historique_activites[-50:]:
            nom = activite['nom']
            activites_comptees[nom] = activites_comptees.get(nom, 0) + 1
        
        # Activités récurrentes (apparaissent au moins 3 fois)
        self.patterns_comportementaux['activites_recurrentes'] = [
            {'nom': nom, 'frequence': count, 'derniere_fois': self._derniere_occurrence(nom)}
            for nom, count in activites_comptees.items() if count >= 3
        ]
        
        # Analyser les préférences temporelles
        preferences = {}
        for activite in self.historique_activites[-100:]:
            nom = activite['nom']
            heure = activite['heure']
            
            if nom not in preferences:
                preferences[nom] = []
            preferences[nom].append(heure)
        
        # Calculer l'heure moyenne préférée pour chaque activité
        for nom, heures in preferences.items():
            if len(heures) >= 2:
                heure_moyenne = sum(heures) / len(heures)
                self.patterns_comportementaux['preferences_temporelles'][nom] = heure_moyenne
    
    def _derniere_occurrence(self, nom_activite: str) -> str:
        """Trouve la dernière occurrence d'une activité"""
        for activite in reversed(self.historique_activites):
            if activite['nom'] == nom_activite:
                return activite['timestamp']
        return ""
    
    def proposer_evenements(self) -> List[Dict[str, Any]]:
        """Propose des événements basés sur l'analyse des patterns"""
        
        suggestions = []
        maintenant = datetime.now()
        jour_actuel = maintenant.weekday()
        heure_actuelle = maintenant.hour
        
        # Suggestions basées sur les activités récurrentes
        for activite_rec in self.patterns_comportementaux.get('activites_recurrentes', []):
            nom = activite_rec['nom']
            derniere_fois = activite_rec.get('derniere_fois', '')
            
            if derniere_fois:
                derniere_date = datetime.fromisoformat(derniere_fois)
                jours_depuis = (maintenant - derniere_date).days
                
                # Suggérer si ça fait plus de 2 jours
                if jours_depuis >= 2:
                    heure_preferee = self.patterns_comportementaux.get('preferences_temporelles', {}).get(nom, heure_actuelle + 1)
                    
                    suggestion = {
                        'type': 'activite_recurrente',
                        'nom': f"Temps pour: {nom}",
                        'description': f"Activité récurrente détectée (dernière fois: il y a {jours_depuis} jours)",
                        'heure_suggeree': int(heure_preferee),
                        'priorite': min(10, jours_depuis),
                        'raison': f"Pattern détecté: {activite_rec['frequence']} occurrences récentes"
                    }
                    suggestions.append(suggestion)
        
        # Suggestions basées sur les horaires de travail
        horaires_jour = self.patterns_comportementaux.get('horaires_travail', {}).get(str(jour_actuel))
        if horaires_jour and len(horaires_jour) == 2:
            debut, fin = horaires_jour
            
            # Suggérer une pause si on est en pleine période de travail
            if debut <= heure_actuelle <= fin and heure_actuelle % 2 == 0:
                suggestion = {
                    'type': 'pause_recommandee',
                    'nom': "Pause recommandée",
                    'description': f"Pause suggérée pendant votre période de travail habituelle ({debut}h-{fin}h)",
                    'heure_suggeree': heure_actuelle,
                    'priorite': 5,
                    'raison': "Optimisation de la productivité"
                }
                suggestions.append(suggestion)
        
        # Suggestions de planification pour demain
        demain = maintenant + timedelta(days=1)
        jour_demain = demain.weekday()
        
        # Suggérer de planifier les tâches importantes le matin
        if heure_actuelle >= 18:  # Après 18h, suggérer pour demain
            suggestion = {
                'type': 'planification_demain',
                'nom': "Planifier la journée de demain",
                'description': f"Organiser les tâches importantes pour {calendar.day_name[jour_demain]}",
                'heure_suggeree': 9,  # 9h du matin
                'priorite': 7,
                'raison': "Optimisation de la productivité matinale"
            }
            suggestions.append(suggestion)
        
        # Trier par priorité
        suggestions.sort(key=lambda x: x['priorite'], reverse=True)
        
        # Mettre à jour les suggestions dans les événements
        self.evenements['suggestions'] = suggestions[:5]  # Top 5
        
        return suggestions
    
    def alerter_si_oubli(self) -> List[str]:
        """Détecte les oublis potentiels et génère des alertes"""
        
        alertes = []
        maintenant = datetime.now()
        
        # Vérifier les activités récurrentes en retard
        for activite_rec in self.patterns_comportementaux.get('activites_recurrentes', []):
            nom = activite_rec['nom']
            derniere_fois = activite_rec.get('derniere_fois', '')
            
            if derniere_fois:
                derniere_date = datetime.fromisoformat(derniere_fois)
                jours_depuis = (maintenant - derniere_date).days
                
                # Alerte si ça fait plus de 5 jours
                if jours_depuis >= 5:
                    alertes.append(f"⚠️ Activité '{nom}' non effectuée depuis {jours_depuis} jours")
        
        # Vérifier les événements planifiés en retard
        for evenement in self.evenements.get('planifies', []):
            if 'date_prevue' in evenement:
                date_prevue = datetime.fromisoformat(evenement['date_prevue'])
                if date_prevue < maintenant and not evenement.get('termine', False):
                    retard_heures = (maintenant - date_prevue).total_seconds() / 3600
                    alertes.append(f"🚨 Événement en retard: '{evenement['nom']}' (retard: {retard_heures:.1f}h)")
        
        # Vérifier les incohérences dans la planification
        heure_actuelle = maintenant.hour
        jour_actuel = maintenant.weekday()
        
        horaires_habituels = self.patterns_comportementaux.get('horaires_travail', {}).get(str(jour_actuel))
        if horaires_habituels:
            debut, fin = horaires_habituels
            
            # Alerte si on est en dehors des horaires habituels un jour de semaine
            if jour_actuel < 5 and (heure_actuelle < debut - 1 or heure_actuelle > fin + 2):
                alertes.append(f"📅 Horaire inhabituel détecté (habituellement actif entre {debut}h-{fin}h)")
        
        return alertes
    
    def planifier_evenement(self, nom: str, date_prevue: str, description: str = "", 
                          priorite: int = 5, recurrent: bool = False) -> Dict[str, Any]:
        """Planifie un nouvel événement"""
        
        evenement = {
            'id': f"evt_{int(time.time())}_{len(self.evenements['planifies'])}",
            'nom': nom,
            'description': description,
            'date_prevue': date_prevue,
            'date_creation': datetime.now().isoformat(),
            'priorite': priorite,
            'recurrent': recurrent,
            'termine': False,
            'notes': ""
        }
        
        self.evenements['planifies'].append(evenement)
        self.save_agenda_data()
        
        print(f"📅 Événement planifié: {nom} pour {date_prevue}")
        return evenement
    
    def get_agenda_jour(self, date: datetime = None) -> Dict[str, Any]:
        """Retourne l'agenda pour un jour donné"""
        
        if date is None:
            date = datetime.now()
        
        date_str = date.date().isoformat()
        
        # Événements planifiés pour ce jour
        evenements_jour = [
            evt for evt in self.evenements['planifies']
            if evt['date_prevue'].startswith(date_str)
        ]
        
        # Suggestions pour ce jour
        suggestions_jour = self.proposer_evenements()
        
        # Patterns pour ce jour
        jour_semaine = date.weekday()
        horaires_habituels = self.patterns_comportementaux.get('horaires_travail', {}).get(str(jour_semaine))
        
        agenda = {
            'date': date_str,
            'jour_semaine': calendar.day_name[jour_semaine],
            'evenements_planifies': evenements_jour,
            'suggestions': suggestions_jour[:3],  # Top 3 suggestions
            'horaires_habituels': horaires_habituels,
            'alertes': self.alerter_si_oubli(),
            'contexte': self.contexte_actuel
        }
        
        return agenda
    
    def generer_rapport_hebdomadaire(self) -> str:
        """Génère un rapport hebdomadaire de planification"""
        
        maintenant = datetime.now()
        debut_semaine = maintenant - timedelta(days=maintenant.weekday())
        
        rapport = f"""
📅 RAPPORT HEBDOMADAIRE AGENDA PRÉDICTIF
=======================================
📅 Semaine du {debut_semaine.strftime('%Y-%m-%d')}
👤 Jean-Luc Passave - JARVIS

🎯 PATTERNS DÉTECTÉS:
   📊 Activités récurrentes: {len(self.patterns_comportementaux.get('activites_recurrentes', []))}
   ⏰ Horaires de travail: {len(self.patterns_comportementaux.get('horaires_travail', {}))} jours analysés
   🎨 Préférences temporelles: {len(self.patterns_comportementaux.get('preferences_temporelles', {}))} activités

📋 ÉVÉNEMENTS:
   📅 Planifiés: {len(self.evenements['planifies'])}
   🔮 Prédits: {len(self.evenements['predits'])}
   💡 Suggestions actives: {len(self.evenements['suggestions'])}

🚨 ALERTES:
"""
        
        alertes = self.alerter_si_oubli()
        if alertes:
            for alerte in alertes[:5]:
                rapport += f"   {alerte}\n"
        else:
            rapport += "   ✅ Aucune alerte\n"
        
        rapport += f"""
📊 ACTIVITÉ:
   📝 Activités enregistrées: {len(self.historique_activites)}
   🎯 Dernière activité: {self.historique_activites[-1]['nom'] if self.historique_activites else 'Aucune'}

🚀 OPTIMISATIONS SUGGÉRÉES:
   💡 Planifier les tâches importantes le matin
   ⏰ Respecter les horaires de travail détectés
   🔄 Maintenir la régularité des activités récurrentes

🎉 AGENDA PRÉDICTIF ACTIF!
"""
        
        return rapport

def test_agenda_predictif():
    """Test du système d'agenda prédictif"""
    
    print("📅 TEST AGENDA PRÉDICTIF INTELLIGENT")
    print("=" * 50)
    print("😂 Le grand frère va adorer cette fonctionnalité!")
    print()
    
    # Créer l'agenda
    agenda = AgendaPredictifIntelligent()
    
    # Simuler quelques activités
    print("📝 SIMULATION ACTIVITÉS:")
    agenda.enregistrer_activite("Développement JARVIS", 120, 0.9, "Travail sur l'IA")
    agenda.enregistrer_activite("Réunion équipe", 60, 0.7, "Discussion projet")
    agenda.enregistrer_activite("Pause café", 15, 0.8, "Détente")
    agenda.enregistrer_activite("Développement JARVIS", 90, 0.85, "Amélioration ICT")
    
    # Planifier un événement
    print("\n📅 PLANIFICATION ÉVÉNEMENT:")
    demain = (datetime.now() + timedelta(days=1)).isoformat()
    agenda.planifier_evenement("Présentation JARVIS", demain, "Démonstration du système", 9)
    
    # Proposer des événements
    print("\n💡 SUGGESTIONS INTELLIGENTES:")
    suggestions = agenda.proposer_evenements()
    for i, suggestion in enumerate(suggestions[:3], 1):
        print(f"   {i}. {suggestion['nom']} - {suggestion['description']}")
        print(f"      Priorité: {suggestion['priorite']}/10 - {suggestion['raison']}")
    
    # Vérifier les alertes
    print("\n🚨 VÉRIFICATION ALERTES:")
    alertes = agenda.alerter_si_oubli()
    if alertes:
        for alerte in alertes:
            print(f"   {alerte}")
    else:
        print("   ✅ Aucune alerte détectée")
    
    # Agenda du jour
    print("\n📋 AGENDA DU JOUR:")
    agenda_jour = agenda.get_agenda_jour()
    print(f"   📅 Date: {agenda_jour['date']} ({agenda_jour['jour_semaine']})")
    print(f"   📅 Événements planifiés: {len(agenda_jour['evenements_planifies'])}")
    print(f"   💡 Suggestions: {len(agenda_jour['suggestions'])}")
    
    # Rapport hebdomadaire
    print("\n📊 RAPPORT HEBDOMADAIRE:")
    rapport = agenda.generer_rapport_hebdomadaire()
    # Afficher les premières lignes
    lignes = rapport.split('\n')[:20]
    for ligne in lignes:
        print(ligne)
    
    print("\n✅ AGENDA PRÉDICTIF TESTÉ!")
    print("😂 Le grand frère va être impressionné par l'anticipation!")

if __name__ == "__main__":
    test_agenda_predictif()
