#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS QI CENTRAL AVEC ICT
Jean-Luc Passave - 2025
Système unifié avec Indice Cognitif Thermique (conseils du grand frère)
"""

import json
import os
import time
import math
from datetime import datetime
from typing import Dict, Any, Optional

# Import du système ICT avancé et du tracker évolutif
try:
    from jarvis_intelligence_thermique_avancee import IntelligenceThermique
    ICT_AVAILABLE = True
except ImportError:
    ICT_AVAILABLE = False

try:
    from jarvis_cognitive_evolution_tracker import CognitiveEvolutionTracker
    TRACKER_AVAILABLE = True
except ImportError:
    TRACKER_AVAILABLE = False

class JarvisQICentralICT:
    """Système central de QI avec ICT (Indice Cognitif Thermique)"""
    
    def __init__(self):
        # Configuration de base
        self.qi_base = 120.0  # QI de base pour ICT
        self.neurones_actifs = 89067389
        self.neurones_total = 89000000000
        
        # Initialiser le système ICT
        if ICT_AVAILABLE:
            self.intelligence_thermique = IntelligenceThermique(qi_initial=self.qi_base)
            self.use_ict = True
            print("✅ Système ICT initialisé")
        else:
            self.intelligence_thermique = None
            self.use_ict = False
            print("⚠️ Système ICT non disponible")

        # Initialiser le tracker évolutif
        if TRACKER_AVAILABLE:
            self.evolution_tracker = CognitiveEvolutionTracker(base_ci=self.qi_base)
            self.use_tracker = True
            print("✅ Tracker évolutif initialisé")
        else:
            self.evolution_tracker = None
            self.use_tracker = False
            print("⚠️ Tracker évolutif non disponible")
        
        # État du système
        self.last_update = datetime.now()
        self.evolution_active = True
        
        # Charger l'état
        self.load_state()
    
    def load_state(self):
        """Charge l'état du système QI"""
        try:
            if os.path.exists('jarvis_qi_central_ict.json'):
                with open('jarvis_qi_central_ict.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.neurones_actifs = data.get('neurones_actifs', self.neurones_actifs)
                    self.neurones_total = data.get('neurones_total', self.neurones_total)
                    self.last_update = datetime.fromisoformat(data.get('last_update', datetime.now().isoformat()))
        except Exception as e:
            print(f"❌ Erreur chargement état: {e}")
    
    def save_state(self):
        """Sauvegarde l'état du système QI"""
        try:
            data = {
                'qi_actuel': self.get_qi_actuel(),
                'neurones_actifs': self.neurones_actifs,
                'neurones_total': self.neurones_total,
                'last_update': self.last_update.isoformat(),
                'use_ict': self.use_ict,
                'evolution_active': self.evolution_active
            }
            
            with open('jarvis_qi_central_ict.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde état: {e}")
    
    def get_qi_actuel(self) -> float:
        """Retourne le QI actuel (ICT si disponible, sinon classique)"""

        if self.use_ict and self.intelligence_thermique:
            # Utiliser l'ICT (Indice Cognitif Thermique)
            qi_ict = self.intelligence_thermique.calculer_qi_thermique()

            # Mettre à jour le tracker évolutif
            if self.use_tracker and self.evolution_tracker:
                self.evolution_tracker.update_ci(qi_ict, "calcul_ict")

            return qi_ict
        else:
            # Fallback sur le calcul classique
            qi_classique = self.qi_base + (self.neurones_actifs / 1000000)

            # Mettre à jour le tracker évolutif
            if self.use_tracker and self.evolution_tracker:
                self.evolution_tracker.update_ci(qi_classique, "calcul_classique")

            return qi_classique
    
    def mettre_a_jour_memoire_thermique(self, nouvelles_donnees: int, pertinence: float = 85.0):
        """Met à jour la mémoire thermique pour l'ICT"""
        
        if self.use_ict and self.intelligence_thermique:
            self.intelligence_thermique.mettre_a_jour_memoire(nouvelles_donnees, pertinence)
            print(f"📚 Mémoire thermique mise à jour: +{nouvelles_donnees} données ({pertinence}% pertinence)")
        
        self.save_state()
    
    def enregistrer_interaction_reussie(self, type_interaction: str = "general"):
        """Enregistre une interaction réussie pour l'ICT"""
        
        if self.use_ict and self.intelligence_thermique:
            self.intelligence_thermique.enregistrer_interaction(True, type_interaction)
        
        self.save_state()
    
    def executer_benchmark(self) -> Dict[str, float]:
        """Exécute un benchmark pour évaluer les capacités"""
        
        if self.use_ict and self.intelligence_thermique:
            return self.intelligence_thermique.executer_benchmark_automatise()
        else:
            # Benchmark simplifié
            return {
                'comprehension_langage': 80.0,
                'resolution_problemes': 75.0,
                'vitesse_execution': 90.0,
                'creativite': 85.0
            }
    
    def evoluer_neurones(self, croissance: int = 1000):
        """Fait évoluer le nombre de neurones"""
        
        self.neurones_actifs += croissance
        
        # Limiter la croissance
        if self.neurones_actifs > self.neurones_total:
            self.neurones_actifs = self.neurones_total
        
        self.last_update = datetime.now()
        self.save_state()
    
    def get_stats_completes(self) -> Dict[str, Any]:
        """Retourne les statistiques complètes du système"""
        
        qi_actuel = self.get_qi_actuel()
        
        stats = {
            'qi_actuel': qi_actuel,
            'qi_base': self.qi_base,
            'neurones_actifs': self.neurones_actifs,
            'neurones_total': self.neurones_total,
            'pourcentage_neurones': (self.neurones_actifs / self.neurones_total) * 100,
            'use_ict': self.use_ict,
            'last_update': self.last_update.isoformat()
        }
        
        # Ajouter les stats ICT si disponibles
        if self.use_ict and self.intelligence_thermique:
            stats_ict = self.intelligence_thermique.get_evolution_stats()
            stats.update({
                'ict_stats': stats_ict,
                'volume_memoire': stats_ict.get('volume_memoire', 0),
                'pertinence': stats_ict.get('pertinence', 0),
                'taux_reussite': stats_ict.get('taux_reussite', 0),
                'evolution_totale': stats_ict.get('evolution_totale', 0)
            })
        
        return stats
    
    def boost_intelligence(self, facteur: float = 1.5):
        """Boost temporaire de l'intelligence"""
        
        if self.use_ict and self.intelligence_thermique:
            # Simuler un boost en ajoutant des données de haute qualité
            self.intelligence_thermique.mettre_a_jour_memoire(500, 95.0)
            # Enregistrer plusieurs interactions réussies
            for _ in range(5):
                self.intelligence_thermique.enregistrer_interaction(True, "boost")
        
        self.save_state()
        return f"🚀 Boost appliqué ! QI actuel: {self.get_qi_actuel():.1f}"

    def set_target_qi(self, target: float):
        """Définit un objectif de QI pour le tracker"""
        if self.use_tracker and self.evolution_tracker:
            self.evolution_tracker.set_target(target)
            return f"🎯 Objectif QI défini: {target}"
        return "⚠️ Tracker non disponible"

    def get_evolution_report(self) -> dict:
        """Retourne le rapport d'évolution du tracker"""
        if self.use_tracker and self.evolution_tracker:
            return self.evolution_tracker.report()
        return {'tracker_available': False}

    def generate_weekly_report(self) -> str:
        """Génère un rapport hebdomadaire"""
        if self.use_tracker and self.evolution_tracker:
            return self.evolution_tracker.generate_weekly_report()
        return "⚠️ Tracker évolutif non disponible"

    def update_performance_metrics(self, **metrics):
        """Met à jour les métriques de performance pour le tracker"""
        if self.use_tracker and self.evolution_tracker:
            # Mettre à jour les métriques du tracker
            if 'memory_volume' in metrics:
                self.evolution_tracker.performance_metrics['memory_volume'] = metrics['memory_volume']
            if 'success_rate' in metrics:
                self.evolution_tracker.performance_metrics['success_rate'] = metrics['success_rate']
            if 'creativity_index' in metrics:
                self.evolution_tracker.performance_metrics['creativity_index'] = metrics['creativity_index']
            if 'learning_speed' in metrics:
                self.evolution_tracker.performance_metrics['learning_speed'] = metrics['learning_speed']
            if 'problem_solving' in metrics:
                self.evolution_tracker.performance_metrics['problem_solving'] = metrics['problem_solving']

            # Recalculer le QI avec les nouvelles métriques
            new_qi = self.evolution_tracker.auto_update_ci()
            self.evolution_tracker.update_ci(new_qi, "metrics_update")

            return f"📊 Métriques mises à jour - Nouveau QI: {new_qi:.1f}"
        return "⚠️ Tracker non disponible"

# Instance globale pour l'import
qi_central_ict = JarvisQICentralICT()

def get_qi_unifie() -> float:
    """Fonction globale pour obtenir le QI unifié"""
    return qi_central_ict.get_qi_actuel()

def get_neurones_actifs() -> int:
    """Fonction globale pour obtenir les neurones actifs"""
    return qi_central_ict.neurones_actifs

def get_neurones_total() -> int:
    """Fonction globale pour obtenir le total de neurones"""
    return qi_central_ict.neurones_total

def mettre_a_jour_memoire(nouvelles_donnees: int, pertinence: float = 85.0):
    """Fonction globale pour mettre à jour la mémoire"""
    qi_central_ict.mettre_a_jour_memoire_thermique(nouvelles_donnees, pertinence)

def enregistrer_interaction(type_interaction: str = "general"):
    """Fonction globale pour enregistrer une interaction"""
    qi_central_ict.enregistrer_interaction_reussie(type_interaction)

def evoluer_systeme():
    """Fonction globale pour faire évoluer le système"""
    qi_central_ict.evoluer_neurones()

def get_stats_systeme() -> Dict[str, Any]:
    """Fonction globale pour obtenir les stats"""
    return qi_central_ict.get_stats_completes()

def test_qi_central_ict():
    """Test du système QI central avec ICT"""
    
    print("🧠 TEST QI CENTRAL AVEC ICT")
    print("=" * 40)
    print("👤 Jean-Luc Passave")
    print("🎯 Système basé sur les conseils du grand frère")
    print()
    
    # État initial
    print("📊 ÉTAT INITIAL:")
    qi_initial = get_qi_unifie()
    print(f"   QI Actuel: {qi_initial:.1f}")
    print(f"   Neurones Actifs: {get_neurones_actifs():,}")
    print(f"   Utilise ICT: {qi_central_ict.use_ict}")
    
    # Mise à jour mémoire
    print("\n📚 MISE À JOUR MÉMOIRE THERMIQUE:")
    mettre_a_jour_memoire(2000, 90.0)
    
    # Interactions réussies
    print("\n🎯 ENREGISTREMENT INTERACTIONS:")
    for i in range(10):
        enregistrer_interaction("comprehension")
    for i in range(5):
        enregistrer_interaction("creativite")
    print("   ✅ 15 interactions réussies enregistrées")
    
    # Benchmark
    print("\n🔬 BENCHMARK AUTOMATISÉ:")
    benchmark = qi_central_ict.executer_benchmark()
    for capacite, score in benchmark.items():
        print(f"   📊 {capacite}: {score:.1f}/100")
    
    # Évolution neurones
    print("\n🧬 ÉVOLUTION NEURONES:")
    evoluer_systeme()
    print("   ✅ Neurones mis à jour")
    
    # Boost
    print("\n🚀 BOOST INTELLIGENCE:")
    result_boost = qi_central_ict.boost_intelligence()
    print(f"   {result_boost}")
    
    # État final
    print("\n📊 ÉTAT FINAL:")
    qi_final = get_qi_unifie()
    stats = get_stats_systeme()
    
    print(f"   QI Final: {qi_final:.1f}")
    print(f"   Évolution: +{qi_final - qi_initial:.1f} points")
    print(f"   Neurones Actifs: {stats['neurones_actifs']:,}")
    
    if 'ict_stats' in stats:
        ict = stats['ict_stats']
        print(f"   Volume Mémoire: {ict.get('volume_memoire', 0):,}")
        print(f"   Taux Réussite: {ict.get('taux_reussite', 0):.1f}%")
        print(f"   Évolution Totale ICT: +{ict.get('evolution_totale', 0):.1f}")
    
    print("\n✅ QI CENTRAL ICT TESTÉ - SYSTÈME OPÉRATIONNEL!")
    print("🙏 Merci au grand frère pour l'excellente méthode ICT !")

if __name__ == "__main__":
    test_qi_central_ict()
