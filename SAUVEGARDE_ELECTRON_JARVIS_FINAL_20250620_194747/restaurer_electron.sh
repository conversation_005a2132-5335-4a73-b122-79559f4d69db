#!/bin/bash

echo "🔄 RESTAURATION APPLICATION ELECTRON JARVIS FINAL"
echo "================================================="
echo "👤 Jean-Luc Passave"
echo ""

# Vérifier qu'on est dans le bon répertoire
if [ ! -f "jarvis_electron_final_complet.js" ]; then
    echo "❌ Fichiers de sauvegarde non trouvés dans ce répertoire"
    exit 1
fi

echo "📁 Copie des fichiers principaux..."
cp jarvis_electron_final_complet.js ../
cp package.json ../

echo "🧪 Copie des scripts de validation..."
cp validation_jarvis_m4_final_sans_simulation.py ../
cp verification_pas_simulation.py ../

echo "🔍 Copie des scripts de test..."
cp test_agents_jarvis_complet.py ../
cp test_bouton_electron_final.py ../

echo "📊 Copie des scripts de monitoring..."
cp monitoring_jarvis_temps_reel.py ../
cp tableau_bord_jarvis_final.py ../

echo "🔧 Copie des scripts de maintenance..."
cp nettoyage_et_redemarrage_jarvis.sh ../
chmod +x ../nettoyage_et_redemarrage_jarvis.sh

echo "✅ Copie des versions propres..."
cp jarvis_sans_simulation.py ../

echo "📋 Copie de la documentation..."
cp *.md ../

echo ""
echo "✅ RESTAURATION TERMINÉE"
echo "======================="
echo "🚀 Pour lancer l'application Electron :"
echo "   cd .."
echo "   npm run final"
echo ""
echo "🧪 Pour valider l'application :"
echo "   python3 validation_jarvis_m4_final_sans_simulation.py"
