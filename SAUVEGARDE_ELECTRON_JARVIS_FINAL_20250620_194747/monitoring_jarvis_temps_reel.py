#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Monitoring JARVIS Temps Réel
<PERSON> - 2025
Surveillance en temps réel de tous les systèmes JARVIS
"""

import gradio as gr
import requests
import psutil
import time
import json
from datetime import datetime
import threading

# État global du monitoring
monitoring_data = {
    'last_update': None,
    'services_status': {},
    'system_metrics': {},
    'jarvis_activity': []
}

def check_service_status(url, name, timeout=3):
    """Vérifie le statut d'un service"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            return {
                'name': name,
                'status': '✅ ACTIF',
                'url': url,
                'response_time': f"{response.elapsed.total_seconds():.2f}s",
                'last_check': datetime.now().strftime("%H:%M:%S")
            }
        else:
            return {
                'name': name,
                'status': f'⚠️ STATUS {response.status_code}',
                'url': url,
                'response_time': 'N/A',
                'last_check': datetime.now().strftime("%H:%M:%S")
            }
    except Exception as e:
        return {
            'name': name,
            'status': '❌ INACTIF',
            'url': url,
            'response_time': 'N/A',
            'last_check': datetime.now().strftime("%H:%M:%S"),
            'error': str(e)
        }

def get_system_metrics():
    """Récupère les métriques système"""
    try:
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Mémoire
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used = round(memory.used / (1024**3), 1)
        memory_total = round(memory.total / (1024**3), 1)
        
        # Disque
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        disk_used = round(disk.used / (1024**3), 1)
        disk_total = round(disk.total / (1024**3), 1)
        
        # Processus JARVIS
        jarvis_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                if 'python' in proc.info['name'].lower() or 'electron' in proc.info['name'].lower():
                    jarvis_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu': proc.info['cpu_percent'],
                        'memory': proc.info['memory_percent']
                    })
            except:
                continue
        
        return {
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count,
                'status': '🟢' if cpu_percent < 70 else '🟡' if cpu_percent < 90 else '🔴'
            },
            'memory': {
                'percent': memory_percent,
                'used': memory_used,
                'total': memory_total,
                'status': '🟢' if memory_percent < 70 else '🟡' if memory_percent < 90 else '🔴'
            },
            'disk': {
                'percent': disk_percent,
                'used': disk_used,
                'total': disk_total,
                'status': '🟢' if disk_percent < 70 else '🟡' if disk_percent < 90 else '🔴'
            },
            'processes': jarvis_processes[:10],  # Top 10
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }
    except Exception as e:
        return {'error': str(e)}

def update_monitoring_data():
    """Met à jour les données de monitoring"""
    global monitoring_data
    
    # Services JARVIS à surveiller
    services = [
        ('http://localhost:7866', 'Communication Principale'),
        ('http://localhost:7867', 'Dashboard Principal'),
        ('http://localhost:7868', 'Éditeur Code'),
        ('http://localhost:7869', 'Pensées JARVIS'),
        ('http://localhost:7870', 'Configuration'),
        ('http://localhost:7879', 'Interface Vocale'),
        ('http://localhost:7880', 'Multi-Agents'),
        ('http://localhost:8000', 'DeepSeek R1 Server')
    ]
    
    # Vérifier les services
    services_status = {}
    for url, name in services:
        status = check_service_status(url, name)
        services_status[name] = status
    
    # Métriques système
    system_metrics = get_system_metrics()
    
    # Mettre à jour les données globales
    monitoring_data.update({
        'last_update': datetime.now().strftime("%H:%M:%S"),
        'services_status': services_status,
        'system_metrics': system_metrics
    })
    
    return monitoring_data

def generate_services_html():
    """Génère le HTML pour l'état des services"""
    data = monitoring_data.get('services_status', {})
    
    if not data:
        return "<p>Aucune donnée disponible</p>"
    
    html = """
    <div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>
        <h3 style='margin: 0 0 15px 0; color: #333;'>🌐 État des Services JARVIS</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;'>
    """
    
    for service_name, service_data in data.items():
        status_color = '#4CAF50' if '✅' in service_data['status'] else '#FF9800' if '⚠️' in service_data['status'] else '#F44336'
        
        html += f"""
        <div style='background: white; padding: 15px; border-radius: 8px; border-left: 4px solid {status_color};'>
            <h4 style='margin: 0 0 10px 0; color: #333;'>{service_name}</h4>
            <p style='margin: 5px 0; font-weight: bold; color: {status_color};'>{service_data['status']}</p>
            <p style='margin: 5px 0; font-size: 0.9em; color: #666;'>Temps: {service_data['response_time']}</p>
            <p style='margin: 5px 0; font-size: 0.9em; color: #666;'>Vérifié: {service_data['last_check']}</p>
        </div>
        """
    
    html += """
        </div>
        <p style='margin: 15px 0 0 0; text-align: center; color: #666; font-size: 0.9em;'>
            Dernière mise à jour: """ + monitoring_data.get('last_update', 'N/A') + """
        </p>
    </div>
    """
    
    return html

def generate_system_html():
    """Génère le HTML pour les métriques système"""
    data = monitoring_data.get('system_metrics', {})
    
    if not data or 'error' in data:
        return "<p>Erreur récupération métriques système</p>"
    
    html = f"""
    <div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>
        <h3 style='margin: 0 0 15px 0; color: #333;'>🍎 Métriques Système Apple M4</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>
            
            <div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>⚡ CPU</h4>
                <div style='font-size: 2em; margin: 10px 0;'>{data['cpu']['status']}</div>
                <p style='margin: 5px 0; font-weight: bold;'>{data['cpu']['percent']:.1f}%</p>
                <p style='margin: 5px 0; color: #666;'>{data['cpu']['count']} cœurs</p>
            </div>
            
            <div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>💾 RAM</h4>
                <div style='font-size: 2em; margin: 10px 0;'>{data['memory']['status']}</div>
                <p style='margin: 5px 0; font-weight: bold;'>{data['memory']['percent']:.1f}%</p>
                <p style='margin: 5px 0; color: #666;'>{data['memory']['used']:.1f}/{data['memory']['total']:.1f} GB</p>
            </div>
            
            <div style='background: white; padding: 15px; border-radius: 8px; text-align: center;'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>💿 Disque</h4>
                <div style='font-size: 2em; margin: 10px 0;'>{data['disk']['status']}</div>
                <p style='margin: 5px 0; font-weight: bold;'>{data['disk']['percent']:.1f}%</p>
                <p style='margin: 5px 0; color: #666;'>{data['disk']['used']:.1f}/{data['disk']['total']:.1f} GB</p>
            </div>
        </div>
        
        <h4 style='margin: 20px 0 10px 0; color: #333;'>🔄 Processus JARVIS Actifs</h4>
        <div style='background: white; padding: 15px; border-radius: 8px;'>
    """
    
    if data['processes']:
        html += "<table style='width: 100%; border-collapse: collapse;'>"
        html += "<tr style='background: #f0f0f0;'><th style='padding: 8px; text-align: left;'>PID</th><th style='padding: 8px; text-align: left;'>Nom</th><th style='padding: 8px; text-align: left;'>CPU%</th><th style='padding: 8px; text-align: left;'>RAM%</th></tr>"
        
        for proc in data['processes']:
            html += f"<tr><td style='padding: 8px;'>{proc['pid']}</td><td style='padding: 8px;'>{proc['name']}</td><td style='padding: 8px;'>{proc['cpu']:.1f}%</td><td style='padding: 8px;'>{proc['memory']:.1f}%</td></tr>"
        
        html += "</table>"
    else:
        html += "<p style='color: #666; text-align: center;'>Aucun processus JARVIS détecté</p>"
    
    html += f"""
        </div>
        <p style='margin: 15px 0 0 0; text-align: center; color: #666; font-size: 0.9em;'>
            Mise à jour: {data['timestamp']}
        </p>
    </div>
    """
    
    return html

def create_monitoring_interface():
    """Interface de monitoring temps réel"""
    
    with gr.Blocks(
        title="📊 Monitoring JARVIS Temps Réel",
        theme=gr.themes.Soft()
    ) as monitoring_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #2E7D32, #4CAF50); color: white; padding: 25px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.2em;">📊 MONITORING JARVIS TEMPS RÉEL</h1>
            <p style="margin: 10px 0; font-size: 1.1em;">Surveillance complète de tous les systèmes</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 1em;">👤 Jean-Luc Passave | 🌐 8 Services | 🍎 Apple M4 | ⏱️ Temps Réel</p>
            </div>
        </div>
        """)

        # Boutons de contrôle
        with gr.Row():
            refresh_btn = gr.Button("🔄 ACTUALISER", variant="primary")
            auto_refresh_btn = gr.Button("⚡ AUTO-REFRESH", variant="secondary")
            
        # État des services
        services_html = gr.HTML(
            value="<p>Cliquez sur 'ACTUALISER' pour charger les données</p>",
            label="État des Services"
        )
        
        # Métriques système
        system_html = gr.HTML(
            value="<p>Cliquez sur 'ACTUALISER' pour charger les métriques</p>",
            label="Métriques Système"
        )

        # Fonctions de mise à jour
        def refresh_all():
            update_monitoring_data()
            services = generate_services_html()
            system = generate_system_html()
            return services, system

        def start_auto_refresh():
            # Cette fonction sera appelée pour démarrer le refresh automatique
            return refresh_all()

        # Connexions
        refresh_btn.click(
            fn=refresh_all,
            outputs=[services_html, system_html]
        )
        
        auto_refresh_btn.click(
            fn=start_auto_refresh,
            outputs=[services_html, system_html]
        )

        # Instructions
        gr.HTML("""
        <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>
            <h3>📋 Instructions d'utilisation:</h3>
            <ul style='text-align: left; margin: 10px 0; line-height: 1.8;'>
                <li><strong>🔄 ACTUALISER</strong> - Met à jour toutes les données une fois</li>
                <li><strong>⚡ AUTO-REFRESH</strong> - Active la mise à jour automatique</li>
                <li><strong>🟢 Vert</strong> - Service/Métrique OK</li>
                <li><strong>🟡 Jaune</strong> - Attention requise</li>
                <li><strong>🔴 Rouge</strong> - Problème détecté</li>
            </ul>
        </div>
        """)

    return monitoring_interface

if __name__ == "__main__":
    print("📊 DÉMARRAGE MONITORING JARVIS TEMPS RÉEL")
    print("=========================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Surveillance complète de tous les systèmes")
    print("")
    
    # Première mise à jour des données
    update_monitoring_data()
    
    # Créer et lancer l'interface
    monitoring_app = create_monitoring_interface()
    
    print("✅ Interface de monitoring créée")
    print("🌐 Lancement sur http://localhost:7894")
    print("📊 Surveillance temps réel disponible")
    
    monitoring_app.launch(
        server_name="127.0.0.1",
        server_port=7894,
        share=False,
        show_error=True,
        quiet=False
    )
