#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Bouton Application Electron Finale
Jean-Luc Passave - 2025
Test du bouton pour lancer l'application Electron finale
"""

import gradio as gr
import subprocess
import os

def launch_electron_final_app():
    """Lance l'application Electron finale avec micro natif - JEAN-LUC PASSAVE"""
    try:
        print("🚀 Lancement Application Electron Finale...")
        
        # Chemin vers le répertoire de l'application
        app_dir = os.getcwd()
        
        # Commande pour lancer l'application Electron finale
        cmd = ["npm", "run", "final"]
        
        # Lancer l'application en arrière-plan
        process = subprocess.Popen(
            cmd,
            cwd=app_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"✅ Application Electron Finale lancée (PID: {process.pid})")
        print("🎤 Interface avec micro natif disponible")
        print("📹 Support webcam intégré")
        print("🍎 Optimisations Apple Silicon M4 actives")
        
        return "✅ Application Electron Finale lancée avec succès ! Vérifiez qu'une nouvelle fenêtre Electron s'est ouverte."
        
    except Exception as e:
        print(f"❌ Erreur lancement Electron Final: {str(e)}")
        return f"❌ Erreur: {str(e)}"

def create_test_interface():
    """Interface de test pour le bouton Electron Final"""
    
    with gr.Blocks(
        title="🧪 Test Bouton Electron Final",
        theme=gr.themes.Soft()
    ) as test_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1); color: white; padding: 20px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2em;">🧪 TEST BOUTON ELECTRON FINAL</h1>
            <p style="margin: 10px 0; font-size: 1.1em;">Test du bouton pour lancer l'application Electron finale</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 1em;">🎤 Micro Natif | 📹 Webcam | 🗣️ Synthèse Vocale | 🍎 Optimisé M4</p>
            </div>
        </div>
        """)

        # Section principale
        with gr.Row():
            with gr.Column():
                gr.HTML("""
                <div style='background: linear-gradient(45deg, #FF6B6B, #4ECDC4); padding: 25px; border-radius: 15px; text-align: center; color: white; margin: 20px 0;'>
                    <div style='background: rgba(255,255,255,0.2); padding: 20px; border-radius: 50%; width: 100px; height: 100px; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 3em;'>
                        🖥️
                    </div>
                    <h3 style='margin: 15px 0; color: white;'>Application Electron Finale</h3>
                    <p style='color: rgba(255,255,255,0.9);'>Interface native avec toutes les fonctionnalités avancées</p>
                </div>
                """)
                
                # Bouton principal
                launch_electron_btn = gr.Button(
                    "🚀 LANCER APPLICATION ELECTRON FINALE",
                    variant="primary",
                    size="lg"
                )
                
                # Résultat
                result_output = gr.Textbox(
                    label="Résultat du lancement",
                    interactive=False,
                    lines=3
                )
            
            with gr.Column():
                gr.HTML("""
                <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h4>✨ Fonctionnalités de l'Application Finale:</h4>
                    <ul style='text-align: left; margin: 10px 0; line-height: 1.8;'>
                        <li>🎤 <strong>Micro natif intégré</strong> - Reconnaissance vocale Web API</li>
                        <li>📹 <strong>Webcam native</strong> - Accès caméra pour vision IA</li>
                        <li>🗣️ <strong>Synthèse vocale</strong> - Réponses audio automatiques</li>
                        <li>💬 <strong>Chat moderne</strong> - Interface avec avatars</li>
                        <li>📊 <strong>Statut temps réel</strong> - Monitoring M4</li>
                        <li>🌐 <strong>Accès complet</strong> - Toutes les interfaces JARVIS</li>
                        <li>🍎 <strong>Optimisé M4</strong> - Performance Apple Silicon</li>
                        <li>🔒 <strong>Sécurisé</strong> - Application native Electron</li>
                    </ul>
                </div>
                """)
                
                gr.HTML("""
                <div style='background: linear-gradient(45deg, #4CAF50, #8BC34A); color: white; padding: 15px; border-radius: 10px; margin: 15px 0; text-align: center;'>
                    <h4 style='margin: 0 0 10px 0;'>🎯 Avantages Electron</h4>
                    <p style='margin: 5px 0; font-size: 0.9em;'>✅ Micro fonctionne (pas de problème web)</p>
                    <p style='margin: 5px 0; font-size: 0.9em;'>✅ Webcam accessible nativement</p>
                    <p style='margin: 5px 0; font-size: 0.9em;'>✅ Performance optimale M4</p>
                    <p style='margin: 5px 0; font-size: 0.9em;'>✅ Interface native macOS</p>
                </div>
                """)

        # Instructions
        gr.HTML("""
        <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #2196F3;'>
            <h4 style='margin: 0 0 10px 0; color: #1976d2;'>📋 Instructions:</h4>
            <ol style='margin: 0; padding-left: 20px; color: #333;'>
                <li>Cliquez sur le bouton "🚀 LANCER APPLICATION ELECTRON FINALE"</li>
                <li>Une nouvelle fenêtre Electron devrait s'ouvrir</li>
                <li>Testez le micro en cliquant sur le bouton 🎤 dans l'interface</li>
                <li>Vérifiez que la reconnaissance vocale fonctionne</li>
                <li>Testez la synthèse vocale des réponses JARVIS</li>
            </ol>
        </div>
        """)

        # Connexion du bouton
        launch_electron_btn.click(
            fn=launch_electron_final_app,
            outputs=[result_output]
        )

    return test_interface

if __name__ == "__main__":
    print("🧪 Démarrage test bouton Electron Final...")
    
    # Créer et lancer l'interface de test
    test_app = create_test_interface()
    
    print("✅ Interface de test créée")
    print("🌐 Lancement sur http://localhost:7891")
    
    test_app.launch(
        server_name="127.0.0.1",
        server_port=7891,
        share=False,
        show_error=True,
        quiet=False
    )
