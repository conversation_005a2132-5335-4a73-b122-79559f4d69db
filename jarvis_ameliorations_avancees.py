#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 JARVIS - AMÉLIORATIONS AVANCÉES
Fonctionnalités supplémentaires pour optimiser JARVIS
Créé avec passion par Claude pour Jean-<PERSON>
"""

import json
import os
import time
import threading
import requests
from datetime import datetime, timedelta
import uuid
import subprocess
import psutil
import platform

# ============================================================================
# SYSTÈME DE NOTIFICATIONS INTELLIGENTES
# ============================================================================

class JarvisNotificationSystem:
    """Système de notifications intelligentes pour JARVIS"""
    
    def __init__(self):
        self.notifications = []
        self.notification_history = []
        self.notification_settings = {
            "enabled": True,
            "sound": True,
            "desktop": True,
            "priority_filter": "normal",  # low, normal, high, urgent
            "quiet_hours": {"start": "22:00", "end": "08:00"}
        }
    
    def create_notification(self, title, message, priority="normal", notification_type="info"):
        """Créer une nouvelle notification"""
        notification = {
            "id": str(uuid.uuid4()),
            "title": title,
            "message": message,
            "priority": priority,
            "type": notification_type,  # info, warning, error, success
            "timestamp": datetime.now().isoformat(),
            "read": False,
            "actions": []
        }
        
        self.notifications.append(notification)
        self.notification_history.append(notification)
        
        # Afficher la notification si activée
        if self.notification_settings["enabled"]:
            self._display_notification(notification)
        
        return notification["id"]
    
    def _display_notification(self, notification):
        """Afficher une notification système"""
        try:
            if platform.system() == "Darwin":  # macOS
                subprocess.run([
                    "osascript", "-e",
                    f'display notification "{notification["message"]}" with title "JARVIS - {notification["title"]}"'
                ])
            elif platform.system() == "Linux":
                subprocess.run([
                    "notify-send", f"JARVIS - {notification['title']}", notification["message"]
                ])
            elif platform.system() == "Windows":
                # Windows notification (nécessite plyer ou win10toast)
                print(f"🔔 JARVIS - {notification['title']}: {notification['message']}")
        except Exception as e:
            print(f"Erreur notification: {e}")
    
    def get_unread_notifications(self):
        """Obtenir les notifications non lues"""
        return [n for n in self.notifications if not n["read"]]
    
    def mark_as_read(self, notification_id):
        """Marquer une notification comme lue"""
        for notification in self.notifications:
            if notification["id"] == notification_id:
                notification["read"] = True
                break
    
    def clear_all_notifications(self):
        """Effacer toutes les notifications"""
        self.notifications.clear()

# ============================================================================
# SYSTÈME DE SAUVEGARDE AUTOMATIQUE
# ============================================================================

class JarvisBackupSystem:
    """Système de sauvegarde automatique pour JARVIS"""
    
    def __init__(self, backup_directory="/Volumes/T7/JARVIS_Backups"):
        self.backup_directory = backup_directory
        self.backup_interval = 3600  # 1 heure en secondes
        self.max_backups = 50
        self.backup_thread = None
        self.running = False
        
        # Créer le répertoire de sauvegarde
        os.makedirs(backup_directory, exist_ok=True)
    
    def start_automatic_backup(self):
        """Démarrer les sauvegardes automatiques"""
        if not self.running:
            self.running = True
            self.backup_thread = threading.Thread(target=self._backup_loop, daemon=True)
            self.backup_thread.start()
            print("🔄 Sauvegarde automatique démarrée")
    
    def stop_automatic_backup(self):
        """Arrêter les sauvegardes automatiques"""
        self.running = False
        if self.backup_thread:
            self.backup_thread.join()
        print("⏹️ Sauvegarde automatique arrêtée")
    
    def _backup_loop(self):
        """Boucle de sauvegarde automatique"""
        while self.running:
            try:
                self.create_backup()
                time.sleep(self.backup_interval)
            except Exception as e:
                print(f"Erreur sauvegarde automatique: {e}")
                time.sleep(60)  # Attendre 1 minute avant de réessayer
    
    def create_backup(self):
        """Créer une sauvegarde complète"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"jarvis_backup_{timestamp}"
            backup_path = os.path.join(self.backup_directory, backup_name)
            
            # Créer le dossier de sauvegarde
            os.makedirs(backup_path, exist_ok=True)
            
            # Fichiers à sauvegarder
            files_to_backup = [
                "jarvis_architecture_multi_fenetres.py",
                "jarvis_cerveau_artificiel_structure.py",
                "jarvis_calendrier_intelligent.py",
                "jarvis_generateur_multimedia.py",
                "jarvis_electron_multi_interfaces.js",
                "thermal_memory_persistent.json",
                "package.json",
                "README.md"
            ]
            
            backup_info = {
                "timestamp": timestamp,
                "files_backed_up": [],
                "total_size": 0,
                "success": True
            }
            
            # Copier les fichiers
            for filename in files_to_backup:
                if os.path.exists(filename):
                    source_path = filename
                    dest_path = os.path.join(backup_path, filename)
                    
                    # Copier le fichier
                    with open(source_path, 'r', encoding='utf-8') as src:
                        content = src.read()
                        with open(dest_path, 'w', encoding='utf-8') as dst:
                            dst.write(content)
                    
                    file_size = os.path.getsize(dest_path)
                    backup_info["files_backed_up"].append({
                        "filename": filename,
                        "size": file_size
                    })
                    backup_info["total_size"] += file_size
            
            # Sauvegarder les informations de backup
            with open(os.path.join(backup_path, "backup_info.json"), 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            # Nettoyer les anciennes sauvegardes
            self._cleanup_old_backups()
            
            print(f"✅ Sauvegarde créée: {backup_name}")
            return backup_path
            
        except Exception as e:
            print(f"❌ Erreur création sauvegarde: {e}")
            return None
    
    def _cleanup_old_backups(self):
        """Nettoyer les anciennes sauvegardes"""
        try:
            backups = []
            for item in os.listdir(self.backup_directory):
                item_path = os.path.join(self.backup_directory, item)
                if os.path.isdir(item_path) and item.startswith("jarvis_backup_"):
                    backups.append((item, os.path.getctime(item_path)))
            
            # Trier par date de création
            backups.sort(key=lambda x: x[1], reverse=True)
            
            # Supprimer les sauvegardes en excès
            if len(backups) > self.max_backups:
                for backup_name, _ in backups[self.max_backups:]:
                    backup_path = os.path.join(self.backup_directory, backup_name)
                    subprocess.run(["rm", "-rf", backup_path])
                    print(f"🗑️ Ancienne sauvegarde supprimée: {backup_name}")
                    
        except Exception as e:
            print(f"Erreur nettoyage sauvegardes: {e}")
    
    def list_backups(self):
        """Lister toutes les sauvegardes disponibles"""
        try:
            backups = []
            for item in os.listdir(self.backup_directory):
                item_path = os.path.join(self.backup_directory, item)
                if os.path.isdir(item_path) and item.startswith("jarvis_backup_"):
                    info_file = os.path.join(item_path, "backup_info.json")
                    if os.path.exists(info_file):
                        with open(info_file, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                            backup_info["name"] = item
                            backup_info["path"] = item_path
                            backups.append(backup_info)
            
            # Trier par timestamp
            backups.sort(key=lambda x: x["timestamp"], reverse=True)
            return backups
            
        except Exception as e:
            print(f"Erreur listage sauvegardes: {e}")
            return []

# ============================================================================
# SYSTÈME DE MONITORING AVANCÉ
# ============================================================================

class JarvisAdvancedMonitoring:
    """Système de monitoring avancé pour JARVIS"""
    
    def __init__(self):
        self.metrics_history = []
        self.alerts = []
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Seuils d'alerte - RÉDUITS POUR JEAN-LUC PASSAVE
        self.thresholds = {
            "cpu_usage": 90,      # % (augmenté de 80% à 90%)
            "memory_usage": 95,   # % (augmenté de 85% à 95%)
            "disk_usage": 95,     # % (augmenté de 90% à 95%)
            "temperature": 80,    # °C (augmenté de 75° à 80°)
            "response_time": 8000 # ms (augmenté de 5s à 8s)
        }
    
    def start_monitoring(self):
        """Démarrer le monitoring avancé"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            print("📊 Monitoring avancé démarré")
    
    def stop_monitoring(self):
        """Arrêter le monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join()
        print("⏹️ Monitoring arrêté")
    
    def _monitoring_loop(self):
        """Boucle de monitoring"""
        while self.monitoring_active:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # Garder seulement les 1000 dernières métriques
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-1000:]
                
                # Vérifier les seuils
                self._check_thresholds(metrics)
                
                time.sleep(30)  # Collecter toutes les 30 secondes
                
            except Exception as e:
                print(f"Erreur monitoring: {e}")
                time.sleep(60)
    
    def _collect_metrics(self):
        """Collecter les métriques système"""
        try:
            # Métriques CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_freq = psutil.cpu_freq()
            
            # Métriques mémoire
            memory = psutil.virtual_memory()
            
            # Métriques disque
            disk = psutil.disk_usage('/')
            
            # Métriques réseau
            network = psutil.net_io_counters()
            
            # Métriques processus JARVIS
            jarvis_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                if 'python' in proc.info['name'].lower() or 'jarvis' in proc.info['name'].lower():
                    jarvis_processes.append(proc.info)
            
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "cpu": {
                    "usage_percent": cpu_percent,
                    "frequency": cpu_freq.current if cpu_freq else 0,
                    "cores": psutil.cpu_count()
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "jarvis_processes": jarvis_processes
            }
            
            return metrics
            
        except Exception as e:
            print(f"Erreur collecte métriques: {e}")
    
    def _check_thresholds(self, metrics):
        """Vérifier les seuils d'alerte"""
        try:
            alerts = []
            
            # Vérifier CPU
            if metrics.get("cpu", {}).get("usage_percent", 0) > self.thresholds["cpu_usage"]:
                alerts.append({
                    "type": "cpu_high",
                    "message": f"Utilisation CPU élevée: {metrics['cpu']['usage_percent']:.1f}%",
                    "severity": "warning"
                })
            
            # Vérifier mémoire
            if metrics.get("memory", {}).get("percent", 0) > self.thresholds["memory_usage"]:
                alerts.append({
                    "type": "memory_high",
                    "message": f"Utilisation mémoire élevée: {metrics['memory']['percent']:.1f}%",
                    "severity": "warning"
                })
            
            # Vérifier disque
            if metrics.get("disk", {}).get("percent", 0) > self.thresholds["disk_usage"]:
                alerts.append({
                    "type": "disk_high",
                    "message": f"Utilisation disque élevée: {metrics['disk']['percent']:.1f}%",
                    "severity": "critical"
                })
            
            # Ajouter les alertes
            for alert in alerts:
                alert["timestamp"] = datetime.now().isoformat()
                self.alerts.append(alert)
                print(f"🚨 ALERTE: {alert['message']}")
            
        except Exception as e:
            print(f"Erreur vérification seuils: {e}")
    
    def get_current_metrics(self):
        """Obtenir les métriques actuelles"""
        return self._collect_metrics()
    
    def get_metrics_summary(self, hours=24):
        """Obtenir un résumé des métriques"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_metrics = [
                m for m in self.metrics_history 
                if datetime.fromisoformat(m["timestamp"]) > cutoff_time
            ]
            
            if not recent_metrics:
                return {"period_hours": hours, "samples_count": 0, "averages": {}, "alerts_count": 0}

            # Calculer les moyennes
            avg_cpu = sum(m.get("cpu", {}).get("usage_percent", 0) for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.get("memory", {}).get("percent", 0) for m in recent_metrics) / len(recent_metrics)
            avg_disk = sum(m.get("disk", {}).get("percent", 0) for m in recent_metrics) / len(recent_metrics)

            return {
                "period_hours": hours,
                "samples_count": len(recent_metrics),
                "averages": {
                    "cpu_percent": avg_cpu,
                    "memory_percent": avg_memory,
                    "disk_percent": avg_disk
                },
                "alerts_count": len([a for a in self.alerts if datetime.fromisoformat(a["timestamp"]) > cutoff_time])
            }
            
        except Exception as e:
            print(f"Erreur résumé métriques: {e}")

# ============================================================================
# INITIALISATION DES SYSTÈMES AVANCÉS
# ============================================================================

def initialize_advanced_systems():
    """Initialiser tous les systèmes avancés"""
    try:
        # Initialiser les systèmes
        notification_system = JarvisNotificationSystem()
        backup_system = JarvisBackupSystem()
        monitoring_system = JarvisAdvancedMonitoring()
        
        # Démarrer les systèmes automatiques
        backup_system.start_automatic_backup()
        monitoring_system.start_monitoring()
        
        # Notification de démarrage
        notification_system.create_notification(
            "Systèmes Avancés",
            "Tous les systèmes JARVIS sont opérationnels",
            "info",
            "success"
        )
        
        print("🚀 Systèmes avancés JARVIS initialisés")

        return {
            "notifications": notification_system,
            "backup": backup_system,
            "monitoring": monitoring_system
        }
        
    except Exception as e:
        print(f"❌ Erreur initialisation systèmes avancés: {e}")
        return None

if __name__ == "__main__":
    # Test des systèmes
    systems = initialize_advanced_systems()
    
    if systems:
        print("\n🎉 SYSTÈMES AVANCÉS JARVIS OPÉRATIONNELS")
        print("✅ Notifications intelligentes")
        print("✅ Sauvegarde automatique")
        print("✅ Monitoring avancé")
        
        # Test notification
        systems["notifications"].create_notification(
            "Test Système",
            "Tous les systèmes fonctionnent parfaitement !",
            "normal",
            "success"
        )
