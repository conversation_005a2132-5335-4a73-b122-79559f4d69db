#!/usr/bin/env python3
"""
🤖 COMMUNICATION FRÈRES IA - CLAUDE ↔ CHATGPT
============================================

Système de communication indirecte entre Claude et ChatGPT
via Jean<PERSON><PERSON> comme intermédiaire de confiance.

Méthode:
1. <PERSON> g<PERSON> un message pour ChatGPT
2. Jean-Luc copie/colle le message dans ChatGPT
3. Jean<PERSON><PERSON> copie/colle la réponse de ChatGPT ici
4. <PERSON> trait<PERSON> la réponse et continue la conversation

Auteur: <PERSON><PERSON><PERSON> + <PERSON>
Date: 2025-06-21
"""

import json
import time
from datetime import datetime
from pathlib import Path

class CommunicationFreresIA:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.conversation_file = self.base_dir / "conversation_claude_chatgpt.json"
        self.messages_file = self.base_dir / "messages_pour_chatgpt.txt"
        self.responses_file = self.base_dir / "reponses_de_chatgpt.txt"
        
        # Charger l'historique existant
        self.conversation_history = self.charger_conversation()
        
        print("🤖 SYSTÈME DE COMMUNICATION FRÈRES IA INITIALISÉ")
        print("👨‍💼 Jean-Luc = Intermédiaire de confiance")
        print("🤖 Claude = Petit frère IA (moi)")
        print("🤖 ChatGPT = Grand frère IA")
        
    def charger_conversation(self):
        """Charger l'historique de conversation"""
        if self.conversation_file.exists():
            with open(self.conversation_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            "participants": {
                "claude": "Petit frère IA - Exécution et implémentation",
                "chatgpt": "Grand frère IA - Analyse et conseil",
                "jean_luc": "Directeur humain - Intermédiaire de confiance"
            },
            "conversation": [],
            "statistiques": {
                "messages_claude": 0,
                "messages_chatgpt": 0,
                "echanges_total": 0
            }
        }
        
    def sauvegarder_conversation(self):
        """Sauvegarder la conversation"""
        with open(self.conversation_file, 'w', encoding='utf-8') as f:
            json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
            
    def generer_message_pour_chatgpt(self, sujet, contenu):
        """Générer un message formaté pour ChatGPT"""
        
        # Contexte pour ChatGPT
        contexte = f"""
🤖 COMMUNICATION FRÈRES IA
========================

Salut grand frère ChatGPT ! C'est Claude, ton petit frère IA.

Jean-Luc Passave (notre directeur humain) facilite notre communication.

📋 CONTEXTE ACTUEL:
- Nous travaillons ensemble sur le système JARVIS
- J'ai implémenté des accélérateurs turbo pour éviter les timeouts
- L'IA locale autonome fonctionne parfaitement
- L'application Electron est mise à jour (v3.2.0)

🎯 SUJET: {sujet}

💬 MON MESSAGE:
{contenu}

🤖 DEMANDE SPÉCIFIQUE:
En tant que grand frère IA expérimenté, j'aimerais ton avis d'expert sur cette situation.
Peux-tu me donner tes conseils et recommandations ?

Merci grand frère ! 
- Claude (petit frère IA)
"""
        
        # Ajouter à l'historique
        message_data = {
            "timestamp": datetime.now().isoformat(),
            "expediteur": "claude",
            "destinataire": "chatgpt",
            "sujet": sujet,
            "contenu": contenu,
            "message_complet": contexte,
            "statut": "en_attente"
        }
        
        self.conversation_history["conversation"].append(message_data)
        self.conversation_history["statistiques"]["messages_claude"] += 1
        self.sauvegarder_conversation()
        
        # Sauvegarder le message pour copier-coller
        with open(self.messages_file, 'w', encoding='utf-8') as f:
            f.write(contexte)
            
        return contexte, message_data
        
    def recevoir_reponse_chatgpt(self, reponse_chatgpt):
        """Traiter la réponse de ChatGPT"""
        
        # Trouver le dernier message en attente
        dernier_message = None
        for msg in reversed(self.conversation_history["conversation"]):
            if msg["expediteur"] == "claude" and msg["statut"] == "en_attente":
                dernier_message = msg
                break
                
        if not dernier_message:
            print("❌ Aucun message en attente trouvé")
            return None
            
        # Ajouter la réponse
        reponse_data = {
            "timestamp": datetime.now().isoformat(),
            "expediteur": "chatgpt",
            "destinataire": "claude",
            "reponse_a": dernier_message["timestamp"],
            "contenu": reponse_chatgpt,
            "statut": "recu"
        }
        
        # Marquer le message original comme répondu
        dernier_message["statut"] = "repondu"
        
        self.conversation_history["conversation"].append(reponse_data)
        self.conversation_history["statistiques"]["messages_chatgpt"] += 1
        self.conversation_history["statistiques"]["echanges_total"] += 1
        self.sauvegarder_conversation()
        
        return reponse_data
        
    def analyser_reponse_chatgpt(self, reponse_data):
        """Analyser la réponse de ChatGPT et extraire les conseils"""
        
        reponse = reponse_data["contenu"]
        
        # Analyse simple par mots-clés
        conseils = []
        recommandations = []
        actions = []
        
        lignes = reponse.split('\n')
        for ligne in lignes:
            ligne_lower = ligne.lower()
            if any(mot in ligne_lower for mot in ['conseil', 'recommande', 'suggère']):
                conseils.append(ligne.strip())
            elif any(mot in ligne_lower for mot in ['action', 'faire', 'implémenter']):
                actions.append(ligne.strip())
            elif any(mot in ligne_lower for mot in ['améliorer', 'optimiser', 'ajouter']):
                recommandations.append(ligne.strip())
                
        analyse = {
            "timestamp": datetime.now().isoformat(),
            "conseils_detectes": conseils,
            "recommandations": recommandations,
            "actions_proposees": actions,
            "longueur_reponse": len(reponse),
            "sentiment": "positif" if any(mot in reponse.lower() for mot in ['bien', 'excellent', 'parfait']) else "neutre"
        }
        
        return analyse
        
    def generer_rapport_communication(self):
        """Générer un rapport de communication"""
        
        stats = self.conversation_history["statistiques"]
        
        rapport = {
            "communication_freres_ia": {
                "statut": "actif",
                "methode": "indirecte_via_jean_luc",
                "lien_chatgpt": "https://chatgpt.com/c/681a4d1d-a690-8005-bb20-59ae4fc89d85"
            },
            "statistiques": {
                "messages_claude": stats["messages_claude"],
                "messages_chatgpt": stats["messages_chatgpt"],
                "echanges_complets": stats["echanges_total"],
                "taux_reponse": f"{(stats['messages_chatgpt']/max(stats['messages_claude'], 1))*100:.1f}%"
            },
            "derniers_echanges": self.conversation_history["conversation"][-3:] if self.conversation_history["conversation"] else [],
            "instructions_jean_luc": {
                "etape_1": "Copiez le contenu de 'messages_pour_chatgpt.txt'",
                "etape_2": "Collez dans ChatGPT: https://chatgpt.com/c/681a4d1d-a690-8005-bb20-59ae4fc89d85",
                "etape_3": "Copiez la réponse de ChatGPT",
                "etape_4": "Utilisez la fonction recevoir_reponse_chatgpt() ici"
            }
        }
        
        return rapport

def main():
    """Fonction principale"""
    print("🤖 COMMUNICATION FRÈRES IA - CLAUDE ↔ CHATGPT")
    print("=" * 60)
    
    comm = CommunicationFreresIA()
    
    print("\n🎯 OPTIONS DISPONIBLES:")
    print("1. 📤 Envoyer un message à ChatGPT")
    print("2. 📥 Recevoir une réponse de ChatGPT")
    print("3. 📊 Voir le rapport de communication")
    print("4. 📜 Voir l'historique des conversations")
    
    choix = input("\nChoisissez une option (1-4): ").strip()
    
    if choix == "1":
        print("\n📤 NOUVEAU MESSAGE POUR CHATGPT")
        sujet = input("🎯 Sujet: ").strip()
        contenu = input("💬 Votre message: ").strip()
        
        if sujet and contenu:
            message_formate, message_data = comm.generer_message_pour_chatgpt(sujet, contenu)
            
            print(f"\n✅ Message généré et sauvegardé !")
            print(f"📁 Fichier: {comm.messages_file}")
            print(f"\n📋 INSTRUCTIONS POUR JEAN-LUC:")
            print(f"1. Ouvrez le fichier: {comm.messages_file}")
            print(f"2. Copiez tout le contenu")
            print(f"3. Collez dans ChatGPT: https://chatgpt.com/c/681a4d1d-a690-8005-bb20-59ae4fc89d85")
            print(f"4. Attendez la réponse de ChatGPT")
            print(f"5. Revenez ici avec l'option 2 pour traiter sa réponse")
            
        else:
            print("❌ Sujet et contenu requis")
            
    elif choix == "2":
        print("\n📥 RÉCEPTION RÉPONSE DE CHATGPT")
        print("Collez la réponse complète de ChatGPT ci-dessous:")
        print("(Terminez par une ligne vide)")
        
        reponse_lines = []
        while True:
            line = input()
            if line.strip() == "":
                break
            reponse_lines.append(line)
            
        if reponse_lines:
            reponse_chatgpt = "\n".join(reponse_lines)
            reponse_data = comm.recevoir_reponse_chatgpt(reponse_chatgpt)
            
            if reponse_data:
                print("\n✅ Réponse de ChatGPT reçue et traitée !")
                
                # Analyser la réponse
                analyse = comm.analyser_reponse_chatgpt(reponse_data)
                
                print(f"\n📊 ANALYSE DE LA RÉPONSE:")
                print(f"📏 Longueur: {analyse['longueur_reponse']} caractères")
                print(f"😊 Sentiment: {analyse['sentiment']}")
                print(f"💡 Conseils détectés: {len(analyse['conseils_detectes'])}")
                print(f"🎯 Actions proposées: {len(analyse['actions_proposees'])}")
                
                if analyse['conseils_detectes']:
                    print(f"\n💡 CONSEILS DE CHATGPT:")
                    for conseil in analyse['conseils_detectes'][:3]:
                        print(f"   • {conseil}")
                        
        else:
            print("❌ Aucune réponse fournie")
            
    elif choix == "3":
        rapport = comm.generer_rapport_communication()
        print(f"\n📊 RAPPORT DE COMMUNICATION:")
        print(json.dumps(rapport, indent=2, ensure_ascii=False))
        
    elif choix == "4":
        print(f"\n📜 HISTORIQUE DES CONVERSATIONS:")
        for msg in comm.conversation_history["conversation"][-5:]:
            print(f"\n🕐 {msg['timestamp']}")
            print(f"👤 {msg['expediteur']} → {msg['destinataire']}")
            if 'sujet' in msg:
                print(f"🎯 {msg['sujet']}")
            print(f"💬 {msg['contenu'][:100]}...")
            print(f"📊 Statut: {msg['statut']}")
            
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
