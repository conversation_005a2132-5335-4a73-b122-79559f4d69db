#!/bin/bash
# 🚀 DÉMARRAGE JARVIS TURBO COMPLET
# ================================
# 
# Script de démarrage automatique pour JARVIS avec accélérateurs turbo
# Évite tous les timeouts et problèmes de performance
#
# Auteur: <PERSON><PERSON><PERSON> + Claude
# Date: 2025-06-21

echo "🚀 DÉMARRAGE JARVIS TURBO COMPLET"
echo "================================="
echo "⚡ ZÉRO TIMEOUT - PERFORMANCE MAXIMALE"
echo ""

# Fonction de vérification des ports
check_port() {
    local port=$1
    local name=$2
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port | grep -q "200\|302"; then
        echo "✅ $name (port $port) - Actif"
        return 0
    else
        echo "❌ $name (port $port) - Inactif"
        return 1
    fi
}

# Fonction de démarrage Ollama avec optimisations
start_ollama_turbo() {
    echo "⚡ Démarrage Ollama avec optimisations turbo..."
    
    # Vérifier si Ollama fonctionne
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        echo "✅ Ollama déjà actif"
        return 0
    fi
    
    # Démarrer Ollama avec optimisations
    export OLLAMA_NUM_PARALLEL=4
    export OLLAMA_MAX_LOADED_MODELS=2
    export OLLAMA_FLASH_ATTENTION=1
    
    nohup ollama serve > /dev/null 2>&1 &
    
    # Attendre démarrage
    echo "⏳ Attente démarrage Ollama..."
    for i in {1..15}; do
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            echo "✅ Ollama turbo actif"
            return 0
        fi
        sleep 1
    done
    
    echo "❌ Timeout démarrage Ollama"
    return 1
}

# Fonction de démarrage JARVIS principal
start_jarvis_principal() {
    echo "🏠 Démarrage JARVIS principal..."
    
    if check_port 7867 "JARVIS Principal"; then
        return 0
    fi
    
    # Démarrer en arrière-plan
    nohup python3 jarvis_architecture_multi_fenetres.py > /dev/null 2>&1 &
    
    # Attendre démarrage
    sleep 3
    check_port 7867 "JARVIS Principal"
}

# Fonction de démarrage JARVIS V2 PRO
start_jarvis_v2_pro() {
    echo "🚀 Démarrage JARVIS V2 PRO..."
    
    if check_port 8000 "JARVIS V2 PRO"; then
        return 0
    fi
    
    # Démarrer V2 PRO
    cd JARVIS_V2_PRO
    nohup python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 > /dev/null 2>&1 &
    cd ..
    
    # Attendre démarrage
    sleep 5
    check_port 8000 "JARVIS V2 PRO"
}

# Fonction de test des accélérateurs
test_accelerateurs() {
    echo "⚡ Test des accélérateurs turbo..."
    
    # Test simple ultra-rapide
    result=$(python3 -c "
import sys
sys.path.append('.')
from accelerateurs_turbo_simple import AccelerateursTurboSimple
acc = AccelerateursTurboSimple()
result = acc.test_simple('Test turbo')
print(f'Durée: {result[\"duree\"]:.2f}s')
" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        echo "✅ Accélérateurs turbo opérationnels"
        echo "   $result"
    else
        echo "⚠️ Accélérateurs en mode fallback"
    fi
}

# Fonction de démarrage Electron
start_electron() {
    echo "📱 Démarrage application Electron..."
    
    # Vérifier si Electron est déjà lancé
    if pgrep -f "jarvis_electron_final_complet.js" > /dev/null; then
        echo "✅ Electron déjà actif"
        return 0
    fi
    
    # Démarrer Electron
    nohup npm run final > /dev/null 2>&1 &
    
    echo "✅ Electron démarré"
}

# Fonction de rapport final
rapport_final() {
    echo ""
    echo "📊 RAPPORT FINAL JARVIS TURBO"
    echo "============================="
    
    # Vérifier tous les services
    services=(
        "7867:JARVIS Principal"
        "8000:JARVIS V2 PRO" 
        "11434:Ollama"
    )
    
    actifs=0
    total=${#services[@]}
    
    for service in "${services[@]}"; do
        port=$(echo $service | cut -d: -f1)
        name=$(echo $service | cut -d: -f2)
        
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port | grep -q "200\|302"; then
            echo "✅ $name (port $port)"
            ((actifs++))
        else
            echo "❌ $name (port $port)"
        fi
    done
    
    echo ""
    echo "📈 STATUT GLOBAL: $actifs/$total services actifs"
    
    if [ $actifs -eq $total ]; then
        echo "🎉 JARVIS TURBO COMPLET OPÉRATIONNEL !"
        echo ""
        echo "🌐 INTERFACES DISPONIBLES:"
        echo "   • JARVIS Principal: http://localhost:7867"
        echo "   • JARVIS V2 PRO: http://localhost:8000"
        echo "   • Dashboard V2: http://localhost:8000/dashboard"
        echo "   • Accélérateurs: Intégrés partout"
        echo ""
        echo "⚡ ACCÉLÉRATEURS TURBO ACTIFS:"
        echo "   • Timeout ultra-rapide: 2-3 secondes max"
        echo "   • Fallback automatique: Zéro blocage"
        echo "   • Optimisation M4: Apple Silicon"
        echo "   • Cache intelligent: Performance maximale"
    else
        echo "⚠️ Certains services nécessitent une vérification"
    fi
}

# Fonction principale
main() {
    echo "🎯 SÉQUENCE DE DÉMARRAGE TURBO:"
    echo "1. Ollama avec optimisations"
    echo "2. JARVIS V2 PRO (API)"
    echo "3. JARVIS Principal (Multi-fenêtres)"
    echo "4. Test accélérateurs"
    echo "5. Application Electron"
    echo ""
    
    read -p "Démarrer JARVIS TURBO COMPLET ? (Y/n): " confirm
    if [[ $confirm =~ ^[Nn]$ ]]; then
        echo "❌ Démarrage annulé"
        exit 1
    fi
    
    # Étape 1: Ollama
    start_ollama_turbo
    
    # Étape 2: JARVIS V2 PRO
    start_jarvis_v2_pro
    
    # Étape 3: JARVIS Principal
    start_jarvis_principal
    
    # Étape 4: Test accélérateurs
    test_accelerateurs
    
    # Étape 5: Electron (optionnel)
    read -p "Démarrer l'interface Electron ? (Y/n): " electron_confirm
    if [[ ! $electron_confirm =~ ^[Nn]$ ]]; then
        start_electron
    fi
    
    # Rapport final
    rapport_final
    
    echo ""
    echo "🚀 JARVIS TURBO PRÊT À L'UTILISATION !"
    echo "⚡ ZÉRO TIMEOUT GARANTI !"
}

# Gestion des signaux
trap 'echo -e "\n👋 Arrêt JARVIS TURBO"; exit 0' INT TERM

# Exécution
main "$@"
