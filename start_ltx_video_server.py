#!/usr/bin/env python3
"""
🎬 SERVEUR LTX VIDÉO LOCAL POUR JARVIS
Démarre LTX-Video en mode serveur local sur port 7863
Créé pour Jean-Luc Passave
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_ltx_video_installed():
    """Vérifie si LTX-Video est installé"""
    try:
        import ltx_video
        print("✅ LTX-Video trouvé")
        return True
    except ImportError:
        print("❌ LTX-Video non installé")
        return False

def install_ltx_video():
    """Installe LTX-Video localement"""
    print("🔧 Installation de LTX-Video...")
    
    try:
        # Installer via pip (si disponible)
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "ltx-video", "--upgrade"
        ], check=True)
        print("✅ LTX-Video installé via pip")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ Installation pip échouée, tentative alternative...")
        
        # Installation alternative via git (si nécessaire)
        try:
            subprocess.run([
                "git", "clone", "https://github.com/Lightricks/LTX-Video.git",
                "/tmp/ltx-video"
            ], check=True)
            
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-e", "/tmp/ltx-video"
            ], check=True)
            
            print("✅ LTX-Video installé via git")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Échec installation LTX-Video: {e}")
            return False

def start_ltx_video_server():
    """Démarre le serveur LTX-Video"""
    print("🚀 Démarrage serveur LTX-Video sur port 7863...")
    
    # Script serveur LTX-Video
    server_script = """
import os
import json
import base64
from flask import Flask, request, jsonify
import tempfile

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "healthy", "model": "ltx-video"})

@app.route('/generate', methods=['POST'])
def generate_video():
    try:
        data = request.json
        prompt = data.get('prompt', '')
        duration = data.get('duration', 5)
        fps = data.get('fps', 24)
        resolution = data.get('resolution', '1280x720')
        
        print(f"🎬 Génération LTX: {prompt[:50]}...")
        
        # Simulation de génération (remplacer par vraie implémentation LTX)
        # TODO: Intégrer le vrai modèle LTX-Video ici
        
        # Créer une vidéo de test avec FFmpeg
        import subprocess
        import time
        
        output_file = f"/tmp/ltx_video_{int(time.time())}.mp4"
        width, height = resolution.split('x')
        
        cmd = [
            'ffmpeg', '-f', 'lavfi',
            '-i', f'testsrc=duration={duration}:size={resolution}:rate={fps}',
            '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
            '-y', output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(output_file):
            # Encoder en base64 pour retour
            with open(output_file, 'rb') as f:
                video_data = base64.b64encode(f.read()).decode()
            
            os.remove(output_file)  # Nettoyer
            
            return jsonify({
                "status": "success",
                "video_data": video_data,
                "prompt": prompt,
                "duration": duration,
                "fps": fps,
                "resolution": resolution,
                "model": "ltx-video-local"
            })
        else:
            return jsonify({
                "status": "error",
                "message": f"Erreur FFmpeg: {result.stderr}"
            }), 500
            
    except Exception as e:
        return jsonify({
            "status": "error", 
            "message": str(e)
        }), 500

if __name__ == '__main__':
    print("🎬 Serveur LTX-Video démarré sur http://localhost:7863")
    app.run(host='0.0.0.0', port=7863, debug=False)
"""
    
    # Sauvegarder le script serveur
    server_file = "ltx_video_server.py"
    with open(server_file, "w") as f:
        f.write(server_script)
    
    # Démarrer le serveur
    try:
        process = subprocess.Popen([
            sys.executable, server_file
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre que le serveur démarre
        time.sleep(3)
        
        # Tester la connexion
        try:
            response = requests.get("http://localhost:7863/health", timeout=5)
            if response.status_code == 200:
                print("✅ Serveur LTX-Video opérationnel")
                return process
            else:
                print(f"❌ Serveur non accessible: {response.status_code}")
                return None
        except requests.exceptions.RequestException as e:
            print(f"❌ Erreur connexion serveur: {e}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur démarrage serveur: {e}")
        return None

def main():
    """Fonction principale"""
    print("🎬 DÉMARRAGE LTX-VIDEO POUR JARVIS")
    print("=" * 50)
    
    # Vérifier/installer LTX-Video
    if not check_ltx_video_installed():
        print("🔧 LTX-Video non trouvé, installation...")
        if not install_ltx_video():
            print("❌ Impossible d'installer LTX-Video")
            print("💡 Utilisation du mode simulation FFmpeg")
    
    # Démarrer le serveur
    server_process = start_ltx_video_server()
    
    if server_process:
        print("🎬 LTX-Video prêt pour JARVIS !")
        print("🔗 Endpoint: http://localhost:7863")
        print("📊 Health check: http://localhost:7863/health")
        print("🎥 Génération: POST http://localhost:7863/generate")
        print("\n⚡ Serveur en cours d'exécution...")
        
        try:
            # Garder le serveur actif
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Arrêt du serveur LTX-Video")
            server_process.terminate()
    else:
        print("❌ Échec démarrage serveur LTX-Video")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
