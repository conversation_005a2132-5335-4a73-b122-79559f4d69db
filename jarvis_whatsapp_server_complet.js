/**
 * 📱 JARVIS WHATSAPP SERVER COMPLET
 * Serveur WhatsApp 100% fonctionnel avec API REST
 * Créé pour Jean<PERSON>Luc <PERSON> - Interface complète
 */

const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

console.log('🤖 JARVIS WhatsApp Server COMPLET - Démarrage...');
console.log('📱 Version: 3.0 - Interface complète avec API REST');
console.log('👤 Développé pour: Jean-Luc Passave');
console.log('🌐 Serveur API: http://localhost:3001');
console.log('');

class JarvisWhatsAppServer {
    constructor() {
        this.client = null;
        this.isReady = false;
        this.jeanLucNumber = null;
        this.messages = [];
        this.contacts = [];
        this.app = express();
        this.port = 3001;
        
        this.setupExpress();
        this.initializeWhatsApp();
        this.startServer();
    }

    setupExpress() {
        // Configuration Express
        this.app.use(cors());
        this.app.use(express.json());
        
        // Routes API
        this.app.get('/status', (req, res) => {
            res.json({
                ready: this.isReady,
                connected: this.client ? true : false,
                messages_count: this.messages.length,
                contacts_count: this.contacts.length,
                timestamp: new Date().toISOString()
            });
        });
        
        this.app.get('/messages', (req, res) => {
            res.json({
                messages: this.messages.slice(-50), // 50 derniers messages
                count: this.messages.length
            });
        });
        
        this.app.post('/send-message', async (req, res) => {
            try {
                const { number, message } = req.body;
                
                if (!this.isReady) {
                    return res.status(400).json({
                        error: 'WhatsApp non connecté',
                        ready: false
                    });
                }
                
                if (!number || !message) {
                    return res.status(400).json({
                        error: 'Numéro et message requis'
                    });
                }
                
                // Envoyer le message
                const chatId = number.includes('@c.us') ? number : `${number}@c.us`;
                await this.client.sendMessage(chatId, message);
                
                // Enregistrer le message envoyé
                this.messages.push({
                    from: 'JARVIS',
                    to: number,
                    body: message,
                    timestamp: new Date().toISOString(),
                    type: 'sent'
                });
                
                console.log(`📤 Message envoyé à ${number}: ${message}`);
                
                res.json({
                    success: true,
                    message: 'Message envoyé avec succès',
                    timestamp: new Date().toISOString()
                });
                
            } catch (error) {
                console.error('❌ Erreur envoi message:', error);
                res.status(500).json({
                    error: 'Erreur envoi message',
                    details: error.message
                });
            }
        });
        
        this.app.get('/contacts', (req, res) => {
            res.json({
                contacts: this.contacts,
                count: this.contacts.length
            });
        });
        
        this.app.post('/send-to-jarvis', async (req, res) => {
            try {
                const { message } = req.body;
                
                // Envoyer à JARVIS via DeepSeek
                const response = await axios.post('http://localhost:8000/v1/chat/completions', {
                    model: "deepseek-r1-8b",
                    messages: [
                        {
                            role: "system",
                            content: "Tu es JARVIS, l'assistant de Jean-Luc Passave. Réponds de manière naturelle et concise."
                        },
                        {
                            role: "user",
                            content: message
                        }
                    ],
                    max_tokens: 200,
                    temperature: 0.7
                });
                
                const jarvisResponse = response.data.choices[0].message.content;
                
                res.json({
                    success: true,
                    response: jarvisResponse,
                    timestamp: new Date().toISOString()
                });
                
            } catch (error) {
                console.error('❌ Erreur communication JARVIS:', error);
                res.json({
                    success: false,
                    response: '🤖 JARVIS temporairement indisponible',
                    error: error.message
                });
            }
        });
    }

    initializeWhatsApp() {
        console.log('🤖 Initialisation WhatsApp Client...');
        
        this.client = new Client({
            authStrategy: new LocalAuth({
                clientId: "jarvis-whatsapp-complet"
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            }
        });

        // Événements WhatsApp
        this.client.on('qr', (qr) => {
            console.log('📱 QR Code WhatsApp:');
            console.log('👆 Scannez ce QR code avec votre téléphone WhatsApp:');
            qrcode.generate(qr, { small: true });
            console.log('');
            console.log('🔄 En attente de la connexion...');
        });

        this.client.on('ready', async () => {
            console.log('✅ JARVIS WhatsApp connecté et prêt !');
            this.isReady = true;
            
            // Charger les contacts
            try {
                const contacts = await this.client.getContacts();
                this.contacts = contacts.map(contact => ({
                    id: contact.id._serialized,
                    name: contact.name || contact.pushname || 'Inconnu',
                    number: contact.number,
                    isGroup: contact.isGroup
                }));
                
                console.log(`📞 ${this.contacts.length} contacts chargés`);
            } catch (error) {
                console.error('❌ Erreur chargement contacts:', error);
            }
        });

        this.client.on('message', async (message) => {
            try {
                // Enregistrer le message reçu
                this.messages.push({
                    from: message.from,
                    body: message.body,
                    timestamp: new Date().toISOString(),
                    type: 'received',
                    author: message.author || message.from
                });
                
                console.log(`📨 Message reçu de ${message.from}: ${message.body}`);
                
                // Répondre automatiquement si JARVIS est mentionné
                if (message.body.toLowerCase().includes('jarvis')) {
                    try {
                        // Obtenir réponse de JARVIS
                        const response = await axios.post('http://localhost:8000/v1/chat/completions', {
                            model: "deepseek-r1-8b",
                            messages: [
                                {
                                    role: "system",
                                    content: "Tu es JARVIS, l'assistant IA de Jean-Luc Passave. Réponds via WhatsApp de manière naturelle et concise (max 100 mots)."
                                },
                                {
                                    role: "user",
                                    content: message.body
                                }
                            ],
                            max_tokens: 150,
                            temperature: 0.8
                        });
                        
                        const jarvisResponse = response.data.choices[0].message.content;
                        await message.reply(`🤖 ${jarvisResponse}`);
                        
                        console.log(`🤖 JARVIS a répondu: ${jarvisResponse}`);
                        
                    } catch (error) {
                        console.error('❌ Erreur réponse JARVIS:', error);
                        await message.reply('🤖 JARVIS temporairement indisponible. Réessayez dans quelques instants.');
                    }
                }
                
            } catch (error) {
                console.error('❌ Erreur traitement message:', error);
            }
        });

        this.client.on('disconnected', (reason) => {
            console.log('❌ WhatsApp déconnecté:', reason);
            this.isReady = false;
        });

        this.client.on('auth_failure', (msg) => {
            console.error('❌ Échec authentification WhatsApp:', msg);
        });

        // Initialiser le client
        this.client.initialize();
    }

    startServer() {
        this.app.listen(this.port, () => {
            console.log(`🌐 Serveur API JARVIS WhatsApp démarré sur port ${this.port}`);
            console.log(`📡 Endpoints disponibles:`);
            console.log(`   GET  /status - Statut de la connexion`);
            console.log(`   GET  /messages - Messages récents`);
            console.log(`   POST /send-message - Envoyer un message`);
            console.log(`   GET  /contacts - Liste des contacts`);
            console.log(`   POST /send-to-jarvis - Communiquer avec JARVIS`);
            console.log('');
            console.log('🔄 En attente de la connexion WhatsApp...');
        });
    }
}

// Démarrer le serveur
const jarvisWhatsApp = new JarvisWhatsAppServer();

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur JARVIS WhatsApp...');
    if (jarvisWhatsApp.client) {
        jarvisWhatsApp.client.destroy();
    }
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Arrêt du serveur JARVIS WhatsApp...');
    if (jarvisWhatsApp.client) {
        jarvisWhatsApp.client.destroy();
    }
    process.exit(0);
});
