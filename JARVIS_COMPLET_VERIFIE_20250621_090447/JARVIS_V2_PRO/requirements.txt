# JARVIS V2 PRO - REQUIREMENTS PRODUCTION
# <PERSON><PERSON><PERSON> - 2025
# Architecture ChatGPT + <PERSON> + <PERSON><PERSON><PERSON>

# === [ API CORE ] ===
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# === [ WEBSOCKET ] ===
websockets==12.0
python-socketio==5.10.0

# === [ ASYNC & WORKERS ] ===
celery==5.3.4
redis==5.0.1
aioredis==2.0.1

# === [ DATABASE ] ===
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# === [ AUDIO ] ===
pyttsx3==2.90
SpeechRecognition==3.10.0
pyaudio==0.2.11
soundfile==0.12.1

# === [ MULTIMEDIA ] ===
moviepy==1.0.3
Pillow==10.1.0
opencv-python==********
numpy==1.24.3

# === [ NLP & AI ] ===
transformers==4.36.0
torch==2.1.1
sentence-transformers==2.2.2
spacy==3.7.2

# === [ UTILITIES ] ===
python-dotenv==1.0.0
requests==2.31.0
aiohttp==3.9.1
httpx==0.25.2

# === [ MONITORING & LOGS ] ===
prometheus-client==0.19.0
structlog==23.2.0
colorama==0.4.6

# === [ SECURITY ] ===
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# === [ TESTING ] ===
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# === [ DEVELOPMENT ] ===
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# === [ CLOUD & DEPLOYMENT ] ===
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0

# === [ DATA PROCESSING ] ===
pandas==2.1.4
scipy==1.11.4
scikit-learn==1.3.2

# === [ CONFIGURATION ] ===
pyyaml==6.0.1
toml==0.10.2
configparser==6.0.0
