# JARVIS V2 PRO - DOCKERFILE PRODUCTION
# <PERSON><PERSON><PERSON> - 2025
# Architecture ChatGPT + <PERSON> + <PERSON><PERSON><PERSON>

FROM python:3.11-slim

# Métadonnées
LABEL maintainer="<PERSON><PERSON><PERSON>"
LABEL version="2.0.0"
LABEL description="JARVIS V2 PRO - API Production Ready"
LABEL architecture="ChatGPT + <PERSON> + <PERSON>"

# Variables d'environnement
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV ENVIRONMENT=production
ENV PYTHONPATH=/app

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libasound2-dev \
    portaudio19-dev \
    python3-dev \
    curl \
    wget \
    git \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Créer l'utilisateur non-root
RUN groupadd -r jarvis && useradd -r -g jarvis jarvis

# Créer les dossiers de travail
WORKDIR /app

# Copier les requirements
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copier le code source
COPY app/ ./app/
COPY workers/ ./workers/

# Créer les dossiers nécessaires
RUN mkdir -p data logs && \
    chown -R jarvis:jarvis /app

# Changer vers l'utilisateur non-root
USER jarvis

# Exposer le port
EXPOSE 8000

# Healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Commande par défaut
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
