#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 - SQUELETTE ARCHITECTURE PRO
<PERSON><PERSON><PERSON> Passave - 2025
Squelette complet selon l'architecture de ChatGPT (grand frère)
FAMILLE IA : ChatGP<PERSON> + <PERSON> + <PERSON><PERSON><PERSON>
"""

import json
import os
import time
from datetime import datetime
from queue import PriorityQueue
from typing import Dict, List, Any, Optional

# Imports audio (avec gestion d'erreur)
try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("⚠️ pyttsx3 non disponible - Mode simulation TTS")

try:
    import speech_recognition as sr
    SR_AVAILABLE = True
except ImportError:
    SR_AVAILABLE = False
    print("⚠️ speech_recognition non disponible - Mode simulation SR")

# === [ CONFIGURATION PRINCIPALE ] ===

class ConfigJARVIS:
    """Configuration principale JARVIS V2 (architecture ChatGPT)"""
    
    PRIORITES = {
        "CRITIQUE": 1,
        "IMPORTANT": 3,
        "NORMAL": 5,
        "BASSE": 7,
    }
    
    REACTIVITE_EMOTIONNELLE = 1.5
    LANGUE = "fr-FR"
    
    # Configuration avancée
    SEUILS_EMOTIONNELS = {
        'minimum': 0,
        'maximum': 10,
        'alerte': 8
    }
    
    MODULES_ACTIFS = [
        'memoire_thermique',
        'profil_emotionnel', 
        'nlp_emotionnel',
        'gestion_taches',
        'audio_vocal',
        'coeur_principal'
    ]

# === [ MODULE 1 : MEMOIRE THERMIQUE ] ===

class MemoireThermique:
    """Mémoire thermique ordonnée par clé/date (architecture ChatGPT)"""
    
    def __init__(self):
        self.index = {}
        self.calendar = {}
        self.compteur_acces = {}
        self.fichier_sauvegarde = 'jarvis_v2_memoire_thermique.json'
        self.charger_memoire()
    
    def ajouter(self, cle: str, contenu: Any, date: str = None):
        """Ajoute un élément à la mémoire thermique"""
        if date is None:
            date = datetime.now().isoformat()
        
        self.index[cle] = {
            "contenu": contenu, 
            "date": date,
            "acces": 0,
            "derniere_modification": datetime.now().isoformat()
        }
        self.calendar[date] = cle
        
        print(f"💾 Mémoire thermique: Ajout '{cle}' ({date})")
        self.sauvegarder_memoire()
    
    def rechercher(self, cle: str) -> Optional[Any]:
        """Recherche par clé"""
        if cle in self.index:
            self.compteur_acces[cle] = self.compteur_acces.get(cle, 0) + 1
            self.index[cle]['acces'] += 1
            return self.index[cle]["contenu"]
        return None

    def rechercher_par_date(self, date: str) -> Optional[Any]:
        """Recherche par date"""
        cle = self.calendar.get(date)
        return self.rechercher(cle) if cle else None
    
    def rechercher_fuzzy(self, terme: str) -> List[Dict[str, Any]]:
        """Recherche floue dans la mémoire"""
        resultats = []
        terme_lower = terme.lower()
        
        for cle, data in self.index.items():
            if terme_lower in cle.lower() or terme_lower in str(data['contenu']).lower():
                resultats.append({
                    'cle': cle,
                    'contenu': data['contenu'],
                    'date': data['date'],
                    'acces': data['acces']
                })
        
        # Trier par nombre d'accès
        resultats.sort(key=lambda x: x['acces'], reverse=True)
        return resultats
    
    def charger_memoire(self):
        """Charge la mémoire depuis le fichier"""
        try:
            if os.path.exists(self.fichier_sauvegarde):
                with open(self.fichier_sauvegarde, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.index = data.get('index', {})
                    self.calendar = data.get('calendar', {})
                    self.compteur_acces = data.get('compteur_acces', {})
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
    
    def sauvegarder_memoire(self):
        """Sauvegarde la mémoire"""
        try:
            data = {
                'index': self.index,
                'calendar': self.calendar,
                'compteur_acces': self.compteur_acces,
                'last_save': datetime.now().isoformat()
            }
            with open(self.fichier_sauvegarde, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire: {e}")

# === [ MODULE 2 : PROFIL EMOTIONNEL ] ===

class ProfilEmotionnel:
    """Profil émotionnel cohérent et dynamique (architecture ChatGPT)"""
    
    def __init__(self):
        self.etat = {
            "joie": 3,
            "colère": 0,
            "curiosité": 5,  # Valeur de base élevée
            "enthousiasme": 2,
            "frustration": 0,
            "satisfaction": 3,
            "confiance": 4
        }
        self.reactivite = ConfigJARVIS.REACTIVITE_EMOTIONNELLE
        self.historique = []
        self.fichier_sauvegarde = 'jarvis_v2_profil_emotionnel.json'
        self.charger_profil()
    
    def ajuster(self, emotion: str, valeur: float, raison: str = ""):
        """Ajuste une émotion avec réactivité"""
        if emotion in self.etat:
            ancienne_valeur = self.etat[emotion]
            self.etat[emotion] += valeur * self.reactivite
            
            # Appliquer les seuils
            self.etat[emotion] = min(max(self.etat[emotion], 
                                       ConfigJARVIS.SEUILS_EMOTIONNELS['minimum']), 
                                   ConfigJARVIS.SEUILS_EMOTIONNELS['maximum'])
            
            # Enregistrer le changement
            changement = {
                'timestamp': datetime.now().isoformat(),
                'emotion': emotion,
                'ancienne_valeur': ancienne_valeur,
                'nouvelle_valeur': self.etat[emotion],
                'ajustement': valeur,
                'raison': raison
            }
            self.historique.append(changement)
            
            print(f"😊 Émotion ajustée: {emotion} {ancienne_valeur:.1f} → {self.etat[emotion]:.1f} ({raison})")
            self.sauvegarder_profil()
    
    def humeur(self) -> str:
        """Détermine l'humeur globale"""
        emotions_positives = self.etat["joie"] + self.etat["enthousiasme"] + self.etat["satisfaction"]
        emotions_negatives = self.etat["colère"] + self.etat["frustration"]
        
        if emotions_positives > emotions_negatives + 3:
            return "très positif"
        elif emotions_positives > emotions_negatives:
            return "positif"
        elif emotions_negatives > emotions_positives + 2:
            return "frustré"
        else:
            return "neutre"
    
    def charger_profil(self):
        """Charge le profil émotionnel"""
        try:
            if os.path.exists(self.fichier_sauvegarde):
                with open(self.fichier_sauvegarde, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.etat = data.get('etat', self.etat)
                    self.historique = data.get('historique', [])
        except Exception as e:
            print(f"❌ Erreur chargement profil: {e}")
    
    def sauvegarder_profil(self):
        """Sauvegarde le profil émotionnel"""
        try:
            data = {
                'etat': self.etat,
                'historique': self.historique[-50:],  # Garder 50 derniers
                'last_save': datetime.now().isoformat()
            }
            with open(self.fichier_sauvegarde, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde profil: {e}")

# === [ MODULE 3 : NLP EMOTIONNEL ] ===

class AnalyseurEmotion:
    """Analyseur émotionnel NLP (architecture ChatGPT)"""
    
    def __init__(self):
        self.mots_cles = {
            'positif': ['merci', 'génial', 'super', 'excellent', 'parfait', 'bravo'],
            'inquiet': ['problème', 'compliqué', 'difficile', 'bug', 'erreur', 'aide'],
            'colère': ['nul', 'débile', 'stupide', 'merde', 'énervé', 'furieux'],
            'curiosité': ['comment', 'pourquoi', 'expliquer', 'comprendre', 'savoir'],
            'enthousiasme': ['wow', 'incroyable', 'extraordinaire', 'fantastique']
        }
    
    def analyser(self, texte: str) -> Dict[str, Any]:
        """Analyse l'émotion d'un texte"""
        texte_lower = texte.lower()
        emotions_detectees = {}
        
        for emotion, mots in self.mots_cles.items():
            score = sum(1 for mot in mots if mot in texte_lower)
            if score > 0:
                emotions_detectees[emotion] = score
        
        if emotions_detectees:
            emotion_dominante = max(emotions_detectees, key=emotions_detectees.get)
            intensite = emotions_detectees[emotion_dominante] / len(texte.split()) * 10
        else:
            emotion_dominante = "neutre"
            intensite = 0.5
        
        return {
            'emotion': emotion_dominante,
            'intensite': min(intensite, 1.0),
            'emotions_detectees': emotions_detectees,
            'texte_analyse': texte
        }

# === [ MODULE 4 : GESTION PRIORITAIRE ] ===

class GestionTaches:
    """Gestion des tâches avec priorité (architecture ChatGPT)"""
    
    def __init__(self):
        self.queue = PriorityQueue()
        self.historique_taches = []
        self.compteur_taches = 0
    
    def ajouter_tache(self, priorite: int, contenu: str, contexte: str = ""):
        """Ajoute une tâche avec priorité"""
        self.compteur_taches += 1
        tache = {
            'id': self.compteur_taches,
            'contenu': contenu,
            'contexte': contexte,
            'timestamp': datetime.now().isoformat(),
            'priorite': priorite
        }
        
        self.queue.put((priorite, self.compteur_taches, tache))
        print(f"📋 Tâche ajoutée (P{priorite}): {contenu}")
    
    def recuperer_tache(self) -> Optional[Dict[str, Any]]:
        """Récupère la tâche la plus prioritaire"""
        if not self.queue.empty():
            priorite, id_tache, tache = self.queue.get()
            self.historique_taches.append(tache)
            return tache
        return None
    
    def taille_queue(self) -> int:
        """Retourne le nombre de tâches en attente"""
        return self.queue.qsize()

# === [ MODULE 5 : AUDIO / VOCAL ] ===

class GestionAudio:
    """Gestion audio bidirectionnelle (architecture ChatGPT)"""
    
    def __init__(self):
        self.engine = None
        self.recognizer = None
        self.microphone = None
        self.initialiser_audio()
    
    def initialiser_audio(self):
        """Initialise les composants audio"""
        # TTS
        if TTS_AVAILABLE:
            try:
                self.engine = pyttsx3.init()
                self.engine.setProperty('rate', 150)
                self.engine.setProperty('volume', 0.8)
                print("🔊 TTS initialisé")
            except Exception as e:
                print(f"❌ Erreur TTS: {e}")
        
        # STT
        if SR_AVAILABLE:
            try:
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()
                print("🎤 STT initialisé")
            except Exception as e:
                print(f"❌ Erreur STT: {e}")

    def parler(self, texte: str):
        """Synthèse vocale"""
        print(f"🔊 JARVIS: {texte}")
        
        if self.engine:
            try:
                self.engine.say(texte)
                self.engine.runAndWait()
            except Exception as e:
                print(f"❌ Erreur parole: {e}")

    def ecouter(self, timeout: int = 5) -> str:
        """Reconnaissance vocale"""
        if not self.recognizer or not self.microphone:
            # Mode simulation
            print("🎤 [SIMULATION] Écoute...")
            time.sleep(1)
            return "commande simulée"
        
        try:
            with self.microphone as source:
                print("🎤 Écoute en cours...")
                audio = self.recognizer.listen(source, timeout=timeout)
                texte = self.recognizer.recognize_google(audio, language=ConfigJARVIS.LANGUE)
                print(f"✅ Reconnu: {texte}")
                return texte
        except sr.WaitTimeoutError:
            print("⏰ Timeout écoute")
            return ""
        except Exception as e:
            print(f"❌ Erreur écoute: {e}")
            return ""

# === [ MODULE 6 : COEUR PRINCIPAL IA ] ===

class JARVIS:
    """Cœur principal JARVIS V2 (architecture ChatGPT)"""
    
    def __init__(self):
        print("🚀 INITIALISATION JARVIS V2")
        print("=" * 40)
        print("🤖 Architecture ChatGPT + Claude + Jean-Luc")
        print()
        
        # Initialiser les modules
        self.memoire = MemoireThermique()
        self.profil = ProfilEmotionnel()
        self.analyseur = AnalyseurEmotion()
        self.taches = GestionTaches()
        self.audio = GestionAudio()
        
        # État du système
        self.actif = True
        self.mode_debug = True
        
        print("✅ JARVIS V2 initialisé avec succès!")
        print(f"😊 Humeur initiale: {self.profil.humeur()}")

    def recevoir_instruction(self, texte: str):
        """Reçoit et traite une instruction"""
        print(f"\n📥 Instruction reçue: {texte}")
        
        # Analyser l'émotion
        analyse_emotion = self.analyseur.analyser(texte)
        emotion = analyse_emotion['emotion']
        intensite = analyse_emotion['intensite']
        
        print(f"🧠 Émotion détectée: {emotion} ({intensite:.1%})")
        
        # Ajuster le profil émotionnel
        ajustements = {
            "positif": ("joie", 2),
            "colère": ("colère", 3),
            "inquiet": ("frustration", 1),
            "curiosité": ("curiosité", 1.5),
            "enthousiasme": ("enthousiasme", 2.5)
        }
        
        if emotion in ajustements:
            emotion_cible, facteur = ajustements[emotion]
            self.profil.ajuster(emotion_cible, intensite * facteur, f"Réaction à: {texte[:30]}...")
        
        # Ajouter à la mémoire
        cle_memoire = f"instruction_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.memoire.ajouter(cle_memoire, {
            'texte': texte,
            'emotion_detectee': emotion,
            'intensite': intensite,
            'humeur_apres': self.profil.humeur()
        })
        
        # Ajouter à la queue des tâches
        priorite = ConfigJARVIS.PRIORITES["IMPORTANT"] if emotion == "colère" else ConfigJARVIS.PRIORITES["NORMAL"]
        self.taches.ajouter_tache(priorite, texte, f"Émotion: {emotion}")
        
        # Réponse adaptée
        humeur_actuelle = self.profil.humeur()
        reponse = self._generer_reponse(texte, emotion, humeur_actuelle)
        self.audio.parler(reponse)

    def _generer_reponse(self, texte: str, emotion: str, humeur: str) -> str:
        """Génère une réponse adaptée"""
        
        if emotion == "positif":
            if humeur == "très positif":
                return "🎉 Merci ! Je suis ravi que cela vous plaise ! Mon enthousiasme est à son maximum !"
            else:
                return "😊 Merci beaucoup ! Cela me fait plaisir !"
        
        elif emotion == "colère":
            return "😔 Je comprends votre frustration. Laissez-moi corriger cela immédiatement."
        
        elif emotion == "inquiet":
            return "🤔 Je vois que vous avez des préoccupations. Comment puis-je vous aider ?"
        
        elif emotion == "curiosité":
            return "🧠 Excellente question ! J'adore explorer de nouveaux sujets avec vous !"
        
        elif emotion == "enthousiasme":
            return "🚀 Votre enthousiasme est contagieux ! Allons-y ensemble !"
        
        else:
            return f"🤖 Compris. Humeur actuelle : {humeur}. Comment puis-je vous aider ?"

    def executer(self):
        """Exécute la prochaine tâche"""
        tache = self.taches.recuperer_tache()
        if tache:
            print(f"\n⚡ Exécution tâche P{tache['priorite']}: {tache['contenu']}")
            self.audio.parler(f"J'exécute : {tache['contenu']}")
            return tache
        return None

    def boucle_principale(self):
        """Boucle principale d'interaction"""
        self.audio.parler("Bonjour ! JARVIS V2 est prêt à fonctionner.")
        
        while self.actif:
            try:
                # Écouter une commande
                commande = self.audio.ecouter(timeout=10)
                
                if commande:
                    if any(mot in commande.lower() for mot in ["stop", "arrêt", "au revoir"]):
                        self.audio.parler("Arrêt du système JARVIS V2. Au revoir !")
                        self.actif = False
                        break
                    else:
                        self.recevoir_instruction(commande)
                        self.executer()
                
                # Petite pause
                time.sleep(0.5)
                
            except KeyboardInterrupt:
                print("\n🔚 Arrêt demandé par l'utilisateur")
                self.audio.parler("Arrêt du système.")
                self.actif = False
                break
            except Exception as e:
                print(f"❌ Erreur boucle principale: {e}")
                time.sleep(2)

    def get_statut(self) -> Dict[str, Any]:
        """Retourne le statut complet du système"""
        return {
            'humeur': self.profil.humeur(),
            'etat_emotionnel': self.profil.etat,
            'taches_en_attente': self.taches.taille_queue(),
            'elements_memoire': len(self.memoire.index),
            'modules_actifs': ConfigJARVIS.MODULES_ACTIFS,
            'timestamp': datetime.now().isoformat()
        }

# === [ LANCEMENT ] ===

def test_jarvis_v2():
    """Test du squelette JARVIS V2"""
    
    print("🚀 TEST SQUELETTE JARVIS V2 (ARCHITECTURE CHATGPT)")
    print("=" * 60)
    print("👤 Jean-Luc Passave")
    print("🤖 Squelette du grand frère ChatGPT")
    print()
    
    # Créer JARVIS V2
    jarvis = JARVIS()
    
    # Test des instructions
    instructions_test = [
        "Bonjour JARVIS, comment ça va ?",
        "Génial ! Tu fonctionnes parfaitement !",
        "J'ai un problème avec le code, peux-tu m'aider ?",
        "Merci beaucoup pour ton aide !",
        "Comment fonctionne ta mémoire thermique ?"
    ]
    
    print("📝 TEST INSTRUCTIONS:")
    for instruction in instructions_test:
        print(f"\n👤 Utilisateur: {instruction}")
        jarvis.recevoir_instruction(instruction)
        jarvis.executer()
        time.sleep(1)
    
    # Statut final
    print(f"\n📊 STATUT FINAL:")
    statut = jarvis.get_statut()
    for cle, valeur in statut.items():
        print(f"   {cle}: {valeur}")
    
    print(f"\n✅ SQUELETTE JARVIS V2 TESTÉ!")
    print(f"🤖 Architecture ChatGPT parfaitement implémentée!")

if __name__ == "__main__":
    test_jarvis_v2()
