#!/usr/bin/env python3
"""
🚀 SERVEUR DIRECT DEEPSEEK R1 8B
Connexion directe sans Ollama - JEAN-LUC PASSAVE
"""

from flask import Flask, request, jsonify
import threading
import time
import subprocess
import os
from datetime import datetime

app = Flask(__name__)

class ServeurDirectDeepSeek:
    """Serveur direct pour DeepSeek R1 8B sans Ollama"""
    
    def __init__(self):
        self.model_loaded = False
        self.model_process = None
        self.port = 8000
        
        print("🚀 SERVEUR DIRECT DEEPSEEK initialisé")
        
    def demarrer_modele_direct(self):
        """Démarre DeepSeek R1 8B en direct"""
        try:
            # Utiliser transformers directement
            print("🤖 Chargement DeepSeek R1 8B en direct...")
            
            # Import des librairies nécessaires
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            model_name = "deepseek-ai/DeepSeek-R1-Distill-Llama-8B"
            
            print(f"📥 Téléchargement {model_name}...")
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            
            self.model_loaded = True
            print("✅ DeepSeek R1 8B chargé en direct !")
            
        except Exception as e:
            print(f"❌ Erreur chargement DeepSeek: {e}")
            self.model_loaded = False
    
    def generer_reponse(self, prompt, temperature=0.7, max_tokens=150):
        """Génère une réponse directe avec DeepSeek"""
        if not self.model_loaded:
            return "❌ Modèle non chargé"
        
        try:
            # Encoder le prompt
            inputs = self.tokenizer.encode(prompt, return_tensors="pt")
            
            # Générer la réponse
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + max_tokens,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Décoder la réponse
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extraire seulement la nouvelle partie
            new_response = response[len(prompt):].strip()
            
            return new_response
            
        except Exception as e:
            print(f"❌ Erreur génération: {e}")
            return f"❌ Erreur génération: {e}"

# Instance globale
serveur_deepseek = ServeurDirectDeepSeek()

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint compatible OpenAI pour DeepSeek direct"""
    try:
        data = request.json
        
        if not serveur_deepseek.model_loaded:
            return jsonify({
                "error": {
                    "message": "Modèle DeepSeek en cours de chargement",
                    "type": "model_loading"
                }
            }), 503
        
        # Extraire le prompt des messages
        messages = data.get('messages', [])
        prompt = ""
        
        for message in messages:
            role = message.get('role', '')
            content = message.get('content', '')
            
            if role == 'system':
                prompt += f"System: {content}\n"
            elif role == 'user':
                prompt += f"User: {content}\n"
            elif role == 'assistant':
                prompt += f"Assistant: {content}\n"
        
        prompt += "Assistant: "
        
        # Paramètres
        temperature = data.get('temperature', 0.7)
        max_tokens = data.get('max_tokens', 150)
        
        # Générer la réponse
        response_text = serveur_deepseek.generer_reponse(prompt, temperature, max_tokens)
        
        # Format de réponse compatible OpenAI
        response = {
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(prompt.split()) + len(response_text.split())
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            "error": {
                "message": str(e),
                "type": "server_error"
            }
        }), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    return jsonify({
        "status": "ok" if serveur_deepseek.model_loaded else "loading",
        "model": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/status', methods=['GET'])
def status():
    """Endpoint de statut détaillé"""
    return jsonify({
        "model_loaded": serveur_deepseek.model_loaded,
        "port": serveur_deepseek.port,
        "timestamp": datetime.now().isoformat(),
        "endpoints": [
            "/v1/chat/completions",
            "/health",
            "/status"
        ]
    })

def demarrer_serveur():
    """Démarre le serveur direct DeepSeek"""
    print("🚀 Démarrage serveur direct DeepSeek...")
    
    # Charger le modèle en arrière-plan
    model_thread = threading.Thread(target=serveur_deepseek.demarrer_modele_direct)
    model_thread.daemon = True
    model_thread.start()
    
    # Démarrer le serveur Flask
    app.run(host='0.0.0.0', port=8000, debug=False)

if __name__ == "__main__":
    print("🚀 SERVEUR DIRECT DEEPSEEK R1 8B")
    print("=" * 50)
    print("🚫 SANS OLLAMA - CONNEXION DIRECTE")
    print("🌐 Port: 8000")
    print("📡 Endpoint: /v1/chat/completions")
    print("=" * 50)
    
    demarrer_serveur()
