#!/bin/bash

echo "🚀 DÉMARRAGE ELECTRON JARVIS CORRIGÉ"
echo "===================================="

# Aller dans le bon répertoire
cd /Volumes/seagate/Louna_Electron_Latest

# Vérifier que le fichier principal existe
if [ ! -f "jarvis_electron_final_complet.js" ]; then
    echo "❌ Fichier jarvis_electron_final_complet.js non trouvé"
    exit 1
fi

# Vérifier qu'Electron est installé
if [ ! -f "node_modules/.bin/electron" ]; then
    echo "🔧 Installation d'Electron..."
    npm install electron
fi

echo "✅ Fichiers vérifiés"
echo "🚀 Démarrage de l'application Electron JARVIS..."

# Démarrer Electron avec le bon fichier
./node_modules/.bin/electron jarvis_electron_final_complet.js

echo "🔄 Application Electron fermée"
