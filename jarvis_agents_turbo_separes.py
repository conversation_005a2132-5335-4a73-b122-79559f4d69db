#!/usr/bin/env python3
"""
🚀 JARVIS AGENTS TURBO SÉPARÉS
Système d'agents turbo sur ports différents - PLUS DE SIMULATION
Créé pour Jean-<PERSON>
"""

import threading
import time
import requests
import json
from datetime import datetime

class AgentsTurboSepares:
    """Gestionnaire d'agents turbo sur ports séparés"""
    
    def __init__(self):
        self.agents = {
            "agent1_principal": {
                "port": 11434,
                "endpoint": "http://localhost:11434/v1/chat/completions",
                "model": "mistral:latest",  # Plus rapide que DeepSeek
                "status": "disconnected",
                "role": "Agent principal JARVIS"
            },
            "agent2_traducteur": {
                "port": 1234,
                "endpoint": "http://localhost:1234/v1/chat/completions", 
                "model": "mistral:latest",
                "status": "disconnected",
                "role": "Agent traducteur turbo"
            },
            "agent3_analyseur": {
                "port": 8001,
                "endpoint": "http://localhost:8001/v1/chat/completions",
                "model": "mistral:latest", 
                "status": "disconnected",
                "role": "Agent analyseur"
            }
        }
        
        self.active = True
        print("🚀 AGENTS TURBO SÉPARÉS initialisés")
        
    def verifier_agents(self):
        """Vérifie le statut de tous les agents"""
        for agent_id, agent_info in self.agents.items():
            try:
                response = requests.get(f"http://localhost:{agent_info['port']}/health", timeout=2)
                if response.status_code == 200:
                    self.agents[agent_id]["status"] = "connected"
                    print(f"✅ {agent_id} connecté sur port {agent_info['port']}")
                else:
                    self.agents[agent_id]["status"] = "error"
            except:
                self.agents[agent_id]["status"] = "disconnected"
                print(f"❌ {agent_id} déconnecté sur port {agent_info['port']}")
    
    def envoyer_a_agent(self, agent_id, message, temperature=0.7):
        """Envoie un message à un agent spécifique"""
        if agent_id not in self.agents:
            return f"❌ Agent {agent_id} inconnu"
        
        agent = self.agents[agent_id]
        
        try:
            payload = {
                "model": agent["model"],
                "messages": [
                    {
                        "role": "system",
                        "content": f"Tu es {agent['role']} de JARVIS. Réponds de manière concise et utile."
                    },
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                "stream": False,
                "temperature": temperature,
                "max_tokens": 200
            }
            
            response = requests.post(agent["endpoint"], json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                reponse = result["choices"][0]["message"]["content"]
                self.agents[agent_id]["status"] = "connected"
                print(f"✅ {agent_id} répond: {reponse[:50]}...")
                return reponse
            else:
                self.agents[agent_id]["status"] = "error"
                return f"❌ Erreur {agent_id}: {response.status_code}"
                
        except Exception as e:
            self.agents[agent_id]["status"] = "disconnected"
            print(f"❌ Erreur connexion {agent_id}: {e}")
            return f"🔄 {agent_id} en reconnexion..."
    
    def agent1_principal(self, message):
        """Agent 1 - Principal JARVIS"""
        return self.envoyer_a_agent("agent1_principal", message, 0.7)
    
    def agent2_traducteur(self, message, langue_cible="français"):
        """Agent 2 - Traducteur Turbo"""
        prompt = f"Traduis en {langue_cible}: {message}"
        return self.envoyer_a_agent("agent2_traducteur", prompt, 0.3)
    
    def agent3_analyseur(self, message):
        """Agent 3 - Analyseur"""
        prompt = f"Analyse et résume: {message}"
        return self.envoyer_a_agent("agent3_analyseur", prompt, 0.5)
    
    def dialogue_multi_agents(self, sujet):
        """Dialogue entre tous les agents"""
        resultats = {}
        
        # Agent 1 commence
        reponse1 = self.agent1_principal(f"Initie une discussion sur: {sujet}")
        resultats["agent1"] = reponse1
        
        # Agent 2 traduit et enrichit
        reponse2 = self.agent2_traducteur(f"Enrichis cette discussion: {reponse1}")
        resultats["agent2"] = reponse2
        
        # Agent 3 analyse
        reponse3 = self.agent3_analyseur(f"Analyse cette discussion: {reponse1} | {reponse2}")
        resultats["agent3"] = reponse3
        
        return resultats
    
    def get_stats_agents(self):
        """Statistiques des agents"""
        stats = {
            "timestamp": datetime.now().isoformat(),
            "agents_connectes": sum(1 for a in self.agents.values() if a["status"] == "connected"),
            "agents_total": len(self.agents),
            "details": self.agents
        }
        return stats
    
    def demarrer_surveillance(self):
        """Démarre la surveillance des agents"""
        def surveillance_worker():
            while self.active:
                try:
                    self.verifier_agents()
                    time.sleep(30)  # Vérification toutes les 30 secondes
                except Exception as e:
                    print(f"❌ Erreur surveillance: {e}")
                    time.sleep(60)
        
        surveillance_thread = threading.Thread(target=surveillance_worker)
        surveillance_thread.daemon = True
        surveillance_thread.start()
        print("🔍 Surveillance agents démarrée")
        
        return surveillance_thread
    
    def arreter_agents(self):
        """Arrête tous les agents"""
        self.active = False
        print("🛑 Agents turbo arrêtés")

# Instance globale
agents_turbo = AgentsTurboSepares()

def demarrer_agents_turbo():
    """Démarre le système d'agents turbo"""
    agents_turbo.demarrer_surveillance()
    return agents_turbo

def agent1_principal(message):
    """Interface Agent 1"""
    return agents_turbo.agent1_principal(message)

def agent2_traducteur(message, langue="français"):
    """Interface Agent 2"""
    return agents_turbo.agent2_traducteur(message, langue)

def agent3_analyseur(message):
    """Interface Agent 3"""
    return agents_turbo.agent3_analyseur(message)

def dialogue_multi_agents(sujet):
    """Interface dialogue multi-agents"""
    return agents_turbo.dialogue_multi_agents(sujet)

def get_stats_agents():
    """Interface stats agents"""
    return agents_turbo.get_stats_agents()

if __name__ == "__main__":
    print("🚀 JARVIS AGENTS TURBO SÉPARÉS")
    print("=" * 50)
    
    # Démarrer le système
    system = demarrer_agents_turbo()
    
    # Test des agents
    print("\n🧪 TEST DES AGENTS:")
    
    test_message = "Bonjour JARVIS, comment ça va ?"
    
    print(f"\n🤖 Agent 1: {agent1_principal(test_message)}")
    print(f"\n🌍 Agent 2: {agent2_traducteur('Hello JARVIS', 'français')}")
    print(f"\n🔍 Agent 3: {agent3_analyseur(test_message)}")
    
    # Stats
    stats = get_stats_agents()
    print(f"\n📊 Stats: {stats}")
    
    try:
        while True:
            time.sleep(10)
            stats = get_stats_agents()
            print(f"📊 Agents connectés: {stats['agents_connectes']}/{stats['agents_total']}")
    except KeyboardInterrupt:
        system.arreter_agents()
        print("\n🚀 Agents turbo arrêtés")
