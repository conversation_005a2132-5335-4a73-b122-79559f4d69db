#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - ROUTES SÉCURITÉ
Jean-<PERSON> - 2025
Routes pour reconnaissance vocale/faciale et sécurité
AUCUN BLOCAGE - VERSION PERSONNELLE
"""

from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from datetime import datetime
import base64
import hashlib

from app.services.security_service import SecurityService
from app.utils.logger import jarvis_logger

# Créer le router
router = APIRouter()

# Service sécurité
security_service = SecurityService()

# === [ MODÈLES PYDANTIC ] ===

class VoiceAuthRequest(BaseModel):
    """Modèle pour l'authentification vocale"""
    audio_data: str  # Base64 encoded
    user_identifier: str
    action: str = "authenticate"  # authenticate, register, verify

class FaceAuthRequest(BaseModel):
    """Mod<PERSON><PERSON> pour l'authentification faciale"""
    image_data: str  # Base64 encoded
    user_identifier: str
    action: str = "authenticate"  # authenticate, register, verify

class SecurityConfig(BaseModel):
    """Configuration sécurité"""
    voice_auth_enabled: bool = True
    face_auth_enabled: bool = True
    require_both: bool = False
    max_attempts: int = 3
    lockout_duration: int = 300  # secondes

# === [ ROUTES AUTHENTIFICATION VOCALE ] ===

@router.post("/voice/auth")
async def voice_authentication(request: VoiceAuthRequest):
    """Authentification par reconnaissance vocale"""
    try:
        jarvis_logger.info(f"Authentification vocale: {request.user_identifier}")
        
        # Décoder l'audio
        try:
            audio_bytes = base64.b64decode(request.audio_data)
        except Exception as e:
            raise HTTPException(status_code=400, detail="Données audio invalides")
        
        # Traitement selon l'action
        if request.action == "register":
            # Enregistrer la voix de l'utilisateur
            result = security_service.enregistrer_voix(
                user_identifier=request.user_identifier,
                audio_data=audio_bytes
            )
            
            return {
                "success": True,
                "action": "register",
                "message": f"Voix enregistrée pour {request.user_identifier}",
                "voice_id": result.get("voice_id"),
                "timestamp": datetime.now().isoformat()
            }
        
        elif request.action == "authenticate":
            # Authentifier par la voix
            auth_result = security_service.authentifier_voix(
                user_identifier=request.user_identifier,
                audio_data=audio_bytes
            )
            
            return {
                "success": auth_result["authenticated"],
                "action": "authenticate",
                "confidence": auth_result["confidence"],
                "message": auth_result["message"],
                "user_identifier": request.user_identifier,
                "timestamp": datetime.now().isoformat()
            }
        
        elif request.action == "verify":
            # Vérifier la qualité de l'audio
            verification = security_service.verifier_qualite_audio(audio_bytes)
            
            return {
                "success": True,
                "action": "verify",
                "audio_quality": verification,
                "timestamp": datetime.now().isoformat()
            }
        
        else:
            raise HTTPException(status_code=400, detail="Action non supportée")
            
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur authentification vocale: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/voice/users")
async def list_voice_users():
    """Liste les utilisateurs avec authentification vocale"""
    try:
        users = security_service.lister_utilisateurs_voix()
        
        return {
            "voice_users": users,
            "total": len(users),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur liste utilisateurs voix: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/voice/{user_identifier}")
async def delete_voice_data(user_identifier: str):
    """Supprime les données vocales d'un utilisateur"""
    try:
        result = security_service.supprimer_donnees_voix(user_identifier)
        
        if not result:
            raise HTTPException(status_code=404, detail="Données vocales non trouvées")
        
        jarvis_logger.info(f"Données vocales supprimées: {user_identifier}")
        
        return {
            "success": True,
            "message": f"Données vocales supprimées pour {user_identifier}",
            "user_identifier": user_identifier,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur suppression données vocales: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# === [ ROUTES AUTHENTIFICATION FACIALE ] ===

@router.post("/face/auth")
async def face_authentication(request: FaceAuthRequest):
    """Authentification par reconnaissance faciale"""
    try:
        jarvis_logger.info(f"Authentification faciale: {request.user_identifier}")
        
        # Décoder l'image
        try:
            image_bytes = base64.b64decode(request.image_data)
        except Exception as e:
            raise HTTPException(status_code=400, detail="Données image invalides")
        
        # Traitement selon l'action
        if request.action == "register":
            # Enregistrer le visage de l'utilisateur
            result = security_service.enregistrer_visage(
                user_identifier=request.user_identifier,
                image_data=image_bytes
            )
            
            return {
                "success": True,
                "action": "register",
                "message": f"Visage enregistré pour {request.user_identifier}",
                "face_id": result.get("face_id"),
                "timestamp": datetime.now().isoformat()
            }
        
        elif request.action == "authenticate":
            # Authentifier par le visage
            auth_result = security_service.authentifier_visage(
                user_identifier=request.user_identifier,
                image_data=image_bytes
            )
            
            return {
                "success": auth_result["authenticated"],
                "action": "authenticate",
                "confidence": auth_result["confidence"],
                "message": auth_result["message"],
                "user_identifier": request.user_identifier,
                "timestamp": datetime.now().isoformat()
            }
        
        elif request.action == "verify":
            # Vérifier la qualité de l'image
            verification = security_service.verifier_qualite_image(image_bytes)
            
            return {
                "success": True,
                "action": "verify",
                "image_quality": verification,
                "timestamp": datetime.now().isoformat()
            }
        
        else:
            raise HTTPException(status_code=400, detail="Action non supportée")
            
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur authentification faciale: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/face/upload")
async def upload_face_image(
    user_identifier: str,
    file: UploadFile = File(...)
):
    """Upload d'image pour reconnaissance faciale"""
    try:
        # Vérifier le type de fichier
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="Fichier doit être une image")
        
        # Lire les données
        image_data = await file.read()
        
        # Enregistrer le visage
        result = security_service.enregistrer_visage(
            user_identifier=user_identifier,
            image_data=image_data
        )
        
        return {
            "success": True,
            "message": f"Image uploadée et visage enregistré pour {user_identifier}",
            "filename": file.filename,
            "face_id": result.get("face_id"),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur upload image: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/face/{user_identifier}")
async def delete_face_data(user_identifier: str):
    """Supprime les données faciales d'un utilisateur"""
    try:
        result = security_service.supprimer_donnees_visage(user_identifier)
        
        if not result:
            raise HTTPException(status_code=404, detail="Données faciales non trouvées")
        
        jarvis_logger.info(f"Données faciales supprimées: {user_identifier}")
        
        return {
            "success": True,
            "message": f"Données faciales supprimées pour {user_identifier}",
            "user_identifier": user_identifier,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur suppression données faciales: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# === [ ROUTES CONFIGURATION SÉCURITÉ ] ===

@router.get("/config")
async def get_security_config():
    """Récupère la configuration sécurité"""
    try:
        config = security_service.get_configuration()
        
        return {
            "security_config": config,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur récupération config sécurité: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config")
async def update_security_config(config: SecurityConfig):
    """Met à jour la configuration sécurité"""
    try:
        result = security_service.mettre_a_jour_configuration(config.dict())
        
        jarvis_logger.info("Configuration sécurité mise à jour")
        
        return {
            "success": True,
            "message": "Configuration sécurité mise à jour",
            "new_config": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur mise à jour config sécurité: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
async def get_security_status():
    """Statut du système de sécurité"""
    try:
        status = security_service.get_statut_securite()
        
        return {
            "security_status": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur statut sécurité: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# === [ ROUTES AUTHENTIFICATION COMBINÉE ] ===

@router.post("/multi-auth")
async def multi_factor_authentication(
    user_identifier: str,
    voice_data: Optional[str] = None,
    face_data: Optional[str] = None
):
    """Authentification multi-facteurs (voix + visage)"""
    try:
        if not voice_data and not face_data:
            raise HTTPException(
                status_code=400, 
                detail="Au moins une méthode d'authentification requise"
            )
        
        results = {}
        overall_success = True
        
        # Authentification vocale
        if voice_data:
            try:
                audio_bytes = base64.b64decode(voice_data)
                voice_result = security_service.authentifier_voix(
                    user_identifier, audio_bytes
                )
                results["voice"] = voice_result
                if not voice_result["authenticated"]:
                    overall_success = False
            except Exception as e:
                results["voice"] = {"authenticated": False, "error": str(e)}
                overall_success = False
        
        # Authentification faciale
        if face_data:
            try:
                image_bytes = base64.b64decode(face_data)
                face_result = security_service.authentifier_visage(
                    user_identifier, image_bytes
                )
                results["face"] = face_result
                if not face_result["authenticated"]:
                    overall_success = False
            except Exception as e:
                results["face"] = {"authenticated": False, "error": str(e)}
                overall_success = False
        
        # Calculer la confiance globale
        confidences = []
        if "voice" in results and results["voice"].get("authenticated"):
            confidences.append(results["voice"].get("confidence", 0))
        if "face" in results and results["face"].get("authenticated"):
            confidences.append(results["face"].get("confidence", 0))
        
        overall_confidence = sum(confidences) / len(confidences) if confidences else 0
        
        jarvis_logger.info(
            f"Authentification multi-facteurs: {user_identifier} - {overall_success}",
            user_id=user_identifier,
            success=overall_success,
            confidence=overall_confidence
        )
        
        return {
            "success": overall_success,
            "user_identifier": user_identifier,
            "authentication_results": results,
            "overall_confidence": overall_confidence,
            "methods_used": list(results.keys()),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur authentification multi-facteurs: {e}")
        raise HTTPException(status_code=500, detail=str(e))
