#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - LOGGER INTELLIGENT
Jean-<PERSON> - 2025
Système de logs avancé pour production
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
import json
from typing import Dict, Any

def setup_logger(name: str, level: str = "INFO") -> logging.Logger:
    """Configure un logger intelligent pour JARVIS V2 PRO"""
    
    # Créer le dossier de logs
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Créer le logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # Éviter la duplication des handlers
    if logger.handlers:
        return logger
    
    # Format personnalisé
    formatter = logging.Formatter(
        '%(asctime)s | %(name)s | %(levelname)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Handler console avec couleurs
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(ColoredFormatter())
    logger.addHandler(console_handler)
    
    # Handler fichier
    file_handler = logging.FileHandler(
        log_dir / f"jarvis_v2_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Handler JSON pour analyse
    json_handler = logging.FileHandler(
        log_dir / f"jarvis_v2_{datetime.now().strftime('%Y%m%d')}.json",
        encoding='utf-8'
    )
    json_handler.setFormatter(JSONFormatter())
    logger.addHandler(json_handler)
    
    return logger

class ColoredFormatter(logging.Formatter):
    """Formatter avec couleurs pour la console"""
    
    # Codes couleurs ANSI
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Vert
        'WARNING': '\033[33m',  # Jaune
        'ERROR': '\033[31m',    # Rouge
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Ajouter les couleurs
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # Format avec couleurs
        formatted = f"{color}%(asctime)s | %(name)s | %(levelname)s{reset} | %(message)s"
        formatter = logging.Formatter(formatted, datefmt='%Y-%m-%d %H:%M:%S')
        
        return formatter.format(record)

class JSONFormatter(logging.Formatter):
    """Formatter JSON pour analyse automatisée"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'logger': record.name,
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Ajouter des infos supplémentaires si disponibles
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'jarvis_state'):
            log_entry['jarvis_state'] = record.jarvis_state
        
        return json.dumps(log_entry, ensure_ascii=False)

class JarvisLogger:
    """Logger spécialisé pour JARVIS avec contexte"""
    
    def __init__(self, name: str):
        self.logger = setup_logger(name)
        self.context = {}
    
    def set_context(self, **kwargs):
        """Définit le contexte pour les logs suivants"""
        self.context.update(kwargs)
    
    def clear_context(self):
        """Efface le contexte"""
        self.context.clear()
    
    def _log_with_context(self, level: str, message: str, **kwargs):
        """Log avec contexte"""
        # Fusionner le contexte et les kwargs
        extra = {**self.context, **kwargs}
        
        # Créer un LogRecord avec les infos supplémentaires
        getattr(self.logger, level.lower())(message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        self._log_with_context('DEBUG', message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self._log_with_context('INFO', message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log_with_context('WARNING', message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log_with_context('ERROR', message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self._log_with_context('CRITICAL', message, **kwargs)
    
    def log_interaction(self, user_message: str, jarvis_response: str, 
                       emotion_detected: str = None, user_id: str = None):
        """Log spécialisé pour les interactions"""
        self.info(
            f"Interaction: {user_message} -> {jarvis_response}",
            user_id=user_id,
            emotion_detected=emotion_detected,
            interaction_type="chat"
        )
    
    def log_api_call(self, endpoint: str, method: str, status_code: int, 
                    response_time: float, user_id: str = None):
        """Log spécialisé pour les appels API"""
        self.info(
            f"API {method} {endpoint} -> {status_code} ({response_time:.3f}s)",
            endpoint=endpoint,
            method=method,
            status_code=status_code,
            response_time=response_time,
            user_id=user_id,
            log_type="api_call"
        )
    
    def log_websocket_event(self, event_type: str, message: str, 
                           connection_count: int = None):
        """Log spécialisé pour les événements WebSocket"""
        self.info(
            f"WebSocket {event_type}: {message}",
            event_type=event_type,
            connection_count=connection_count,
            log_type="websocket"
        )
    
    def log_jarvis_state_change(self, old_state: Dict[str, Any], 
                               new_state: Dict[str, Any], reason: str = None):
        """Log spécialisé pour les changements d'état de JARVIS"""
        self.info(
            f"État JARVIS changé: {reason or 'Non spécifié'}",
            old_state=old_state,
            new_state=new_state,
            reason=reason,
            log_type="state_change"
        )

# Instance globale pour faciliter l'utilisation
jarvis_logger = JarvisLogger("jarvis_v2_pro")
