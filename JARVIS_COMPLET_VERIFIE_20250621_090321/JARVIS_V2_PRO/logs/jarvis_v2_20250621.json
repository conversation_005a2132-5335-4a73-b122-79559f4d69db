{"timestamp": "2025-06-21T04:33:40.560047", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T04:33:40.560432", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T04:34:39.360352", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> <PERSON><PERSON><PERSON>, je suis <PERSON><PERSON> et je travaille sur JARV...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:34:39.382427", "logger": "jarvis_v2_pro", "level": "INFO", "message": "<PERSON><PERSON>ération réponse pour jean_luc_passave: Bonjour, je suis <PERSON>-Luc et je travaille sur JARV...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:34:39.447558", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_043439_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:34:39.447640", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:01.436913", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> Peux-tu me rappeler mon nom et sur quoi je travail...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:01.578174", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Peux-tu me rappeler mon nom et sur quoi je travail...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:01.609226", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_043501_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:01.609309", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:23.799569", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Mémoire ajoutée: presentation_jean_luc", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:44.396535", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> Rappelle-moi qui je suis et sur quoi je travaille...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:44.532085", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Rappelle-moi qui je suis et sur quoi je travaille...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:44.532710", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_043544_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:35:44.532758", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:26.594735", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Recherche mémoire: 'jean-luc' - 1 résultats", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:47.786768", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Bonjour, je m'appelle Jean-Luc et je travaille sur...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.109457", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_043648_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.109568", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.109607", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Peux-tu me rappeler mon nom ?...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.110177", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_043648_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.110208", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.110234", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Sur quoi est-ce que je travaille ?...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.110800", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_043648_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T04:36:48.110825", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:05:05.693851", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:05:05.708060", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:06:16.115978", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> Bonjour, je suis Jean-Luc Passave et je teste mon ...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:06:16.426913", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Bonjour, je suis Jean-Luc Passave et je teste mon ...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:06:16.485066", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_050616_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:06:16.485157", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:06:39.857581", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Mémoire ajoutée: test_jean_luc_final", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:07:12.584417", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Recherche mémoire: 'jean-luc' - 2 résultats", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:07:34.720185", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> Rappelle-moi qui je suis et sur quoi je travaille...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:07:34.979548", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Rappelle-moi qui je suis et sur quoi je travaille...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:07:35.016907", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_050734_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:07:35.016993", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 1 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:07:56.734127", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Commande d'oubli: 'test' pour jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:10:01.143762", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Statistiques mémoire consultées", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:16:20.141180", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:16:20.154035", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:18:34.436925", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> Bonjour JARVIS, je suis Jean-Luc et tout fonctionn...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:18:34.437395", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: Bonjour JARVIS, je suis Jean-Luc et tout fonctionn...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:18:34.449025", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_051834_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:18:34.449406", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:19:40.518468", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T05:19:44.838474", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:19:44.838598", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:20:12.851163", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T05:20:16.393828", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:20:16.393918", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:23:20.672318", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T05:23:23.850648", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:23:23.850723", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:23:44.138136", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T05:25:33.545088", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:25:33.552129", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:27:29.336320", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 💼 TRAVAIL ASSIGNÉ: Analyse les performances systèm...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:29.645205", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 💼 TRAVAIL ASSIGNÉ: Analyse les performances systèm...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:29.647550", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_052729_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:29.647611", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:52.702725", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 🚀 JARVIS, je veux que tu travailles ! Voici ta mis...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:52.750056", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 🚀 JARVIS, je veux que tu travailles ! Voici ta mis...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:52.750822", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_052752_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:27:52.750867", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:30:24.338942", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:30:24.358023", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T05:31:15.363817", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 🚀 JARVIS, mission urgente ! Analyse les performanc...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:15.671674", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 🚀 JARVIS, mission urgente ! Analyse les performanc...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:15.672618", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_053115_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:15.672681", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:37.346241", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 💡 JARVIS, nouvelle mission ! Propose-moi 3 amélior...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:37.433700", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 💡 JARVIS, nouvelle mission ! Propose-moi 3 amélior...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:37.434587", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_053137_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:31:37.434634", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:32:00.584509", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 🧠 JARVIS, analyse ma mémoire thermique et trouve d...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:32:00.885872", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 🧠 JARVIS, analyse ma mémoire thermique et trouve d...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:32:00.886955", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_053200_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:32:00.887019", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T05:33:33.572662", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T05:33:33.593824", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:26:44.622374", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:26:49.669927", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:26:49.670015", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:28:32.980002", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Commande d'oubli: 'test' pour jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:28:33.010267", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Oubli effectué: 2 éléments supprimés", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:30:05.778858", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:30:10.309064", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:30:10.309269", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:32:26.569337", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:32:29.773136", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:32:29.773223", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:33:14.929233", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:33:17.914116", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:33:17.914200", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:38:50.046737", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 🎯 JARVIS, mission créative ! Génère-moi un plan dé...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:38:50.047281", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 🎯 JARVIS, mission créative ! Génère-moi un plan dé...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:38:50.352579", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_083850_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:38:50.352666", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:14.483610", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 🔧 JARVIS, mission technique ! Analyse notre archit...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:14.483876", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 🔧 JARVIS, mission technique ! Analyse notre archit...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:14.484594", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_083914_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:14.484629", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:36.696454", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 🎓 JARVIS, je veux que tu apprennes ! Voici une nou...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:36.696543", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 🎓 JARVIS, je veux que tu apprennes ! Voici une nou...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:36.697197", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_083936_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:36.697231", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:59.817133", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Chat agent: jean_luc_passave -> 💡 JARVIS, maintenant propose-moi des améliorations...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:59.817516", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Génération réponse pour jean_luc_passave: 💡 JARVIS, maintenant propose-moi des améliorations...", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:59.819027", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Conversation stockée: conversation_20250621_083959_jean_luc_passave", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:39:59.819371", "logger": "jarvis_v2_pro", "level": "INFO", "message": "Réponse générée avec 0 souvenirs contextuels", "module": "logger", "function": "_log_with_context", "line": 132}
{"timestamp": "2025-06-21T08:41:54.916959", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:41:58.711254", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:41:58.711434", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:42:28.505003", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:42:31.049771", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:42:31.049870", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:43:56.412113", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:43:59.306266", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:43:59.306341", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:49:39.335912", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:49:43.159784", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:49:43.159881", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:50:03.934563", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:50:07.333102", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:50:07.333378", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
{"timestamp": "2025-06-21T08:50:28.374521", "logger": "jarvis_v2_api", "level": "INFO", "message": "🔚 JARVIS V2 PRO API - Arrêt", "module": "main", "function": "shutdown_event", "line": 74}
{"timestamp": "2025-06-21T08:50:32.376903", "logger": "jarvis_v2_api", "level": "INFO", "message": "🚀 JARVIS V2 PRO API - Démarrage", "module": "main", "function": "startup_event", "line": 64}
{"timestamp": "2025-06-21T08:50:32.377042", "logger": "jarvis_v2_api", "level": "INFO", "message": "✅ JARVIS V2 PRO API - Opérationnel", "module": "main", "function": "startup_event", "line": 69}
