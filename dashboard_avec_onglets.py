#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Dashboard JARVIS avec Onglets
Jean-Luc Passave - 2025
Dashboard principal avec organisation en onglets
"""

import gradio as gr
import subprocess
import webbrowser
import os

def launch_electron_final_app():
    """Lance l'application Electron finale"""
    try:
        cmd = ["npm", "run", "final"]
        process = subprocess.Popen(cmd, cwd=os.getcwd())
        return f"✅ Application Electron lancée (PID: {process.pid})"
    except Exception as e:
        return f"❌ Erreur: {str(e)}"

def open_window(window_type):
    """Ouvre une fenêtre spécialisée"""
    ports = {
        "communication": 7866,
        "presentation": 7890,
        "code": 7868,
        "thoughts": 7869,
        "config": 7870,
        "whatsapp": 7871,
        "security": 7872,
        "monitoring": 7873,
        "memory": 7874,
        "creative": 7875,
        "music": 7876,
        "system": 7877,
        "websearch": 7878,
        "voice": 7879,
        "multiagent": 7880,
        "workspace": 7881,
        "accelerators": 7882
    }
    
    if window_type in ports:
        url = f"http://localhost:{ports[window_type]}"
        webbrowser.open(url)
        return f"✅ Ouverture de {window_type} sur {url}"
    else:
        return f"❌ Fenêtre {window_type} non trouvée"

def create_dashboard_with_tabs():
    """Crée le dashboard avec onglets"""
    
    with gr.Blocks(
        title="🤖 JARVIS Dashboard avec Onglets",
        theme=gr.themes.Soft()
    ) as dashboard:

        # HEADER PRINCIPAL
        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🤖 JARVIS DASHBOARD</h1>
            <h2 style="margin: 10px 0; font-size: 1.5em;">Interface Multi-Fenêtres avec Onglets</h2>
            <p style="margin: 10px 0; font-size: 1.2em;"><strong>Jean-Luc Passave</strong> - Apple Silicon M4 Optimisé</p>
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <p style="margin: 0; font-size: 1.1em;">🧠 QI: 247+ | ⚡ 89B Neurones | 🔥 Dynamique | 🍎 M4 Optimisé</p>
            </div>
        </div>
        """)

        # ONGLETS PRINCIPAUX
        with gr.Tabs():
            
            # ONGLET 1: ACCUEIL
            with gr.Tab("🏠 Accueil"):
                gr.HTML("""
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 15px; margin: 20px 0;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h2 style="margin: 0; font-size: 2.2em;">🎯 Bienvenue dans JARVIS</h2>
                        <p style="margin: 15px 0; font-size: 1.2em;">Votre Assistant IA Personnel Nouvelle Génération</p>
                        <p style="margin: 10px 0; font-size: 1em; opacity: 0.8;">Développé spécialement pour Jean-Luc Passave</p>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 25px; margin: 25px 0;">
                        <div style="background: rgba(255,255,255,0.15); padding: 25px; border-radius: 12px; text-align: center;">
                            <h3 style="margin: 0 0 15px 0;">🧠 Intelligence</h3>
                            <p style="margin: 0; line-height: 1.4;">
                                <strong>DeepSeek R1 8B</strong><br>
                                Mémoire Thermique<br>
                                QI Adaptatif: 247+
                            </p>
                        </div>
                        <div style="background: rgba(255,255,255,0.15); padding: 25px; border-radius: 12px; text-align: center;">
                            <h3 style="margin: 0 0 15px 0;">⚡ Performance</h3>
                            <p style="margin: 0; line-height: 1.4;">
                                <strong>Réponses Instantanées</strong><br>
                                Multi-Fenêtres<br>
                                Optimisé M4
                            </p>
                        </div>
                        <div style="background: rgba(255,255,255,0.15); padding: 25px; border-radius: 12px; text-align: center;">
                            <h3 style="margin: 0 0 15px 0;">🔐 Sécurité</h3>
                            <p style="margin: 0; line-height: 1.4;">
                                <strong>100% Local</strong><br>
                                Chiffrement Avancé<br>
                                Contrôle Total
                            </p>
                        </div>
                    </div>
                </div>
                """)
            
            # ONGLET 2: INTERFACES PRINCIPALES
            with gr.Tab("🚀 Interfaces Principales"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>🚀 INTERFACES PRINCIPALES</h2>")
                
                # Communication Principale
                gr.HTML("""
                <div style="background: linear-gradient(45deg, #2c2c2c, #6a4c93, #9c27b0); color: white; padding: 20px; border-radius: 15px; margin: 20px 0; text-align: center;">
                    <h2 style="margin: 0 0 10px 0;">💬 COMMUNICATION PRINCIPALE</h2>
                    <p style="margin: 0;">Interface complète comme Claude/ChatGPT - Chat, Micro, Caméra, Web</p>
                </div>
                """)
                
                launch_communication_btn = gr.Button(
                    "🚀 OUVRIR COMMUNICATION PRINCIPALE",
                    variant="primary",
                    size="lg"
                )

                # Application Electron Finale
                gr.HTML("""
                <div style="background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1); color: white; padding: 20px; border-radius: 15px; margin: 20px 0; text-align: center;">
                    <h2 style="margin: 0 0 10px 0;">🖥️ APPLICATION ELECTRON FINALE</h2>
                    <p style="margin: 0;">Interface native avec micro, webcam et neurones dynamiques</p>
                    <p style="margin: 8px 0 0 0; opacity: 0.9;">🎤 Micro Natif | 📹 Webcam | 🧠 Neurones Dynamiques | 🍎 M4</p>
                </div>
                """)

                launch_electron_final_btn = gr.Button(
                    "🚀 OUVRIR APPLICATION ELECTRON FINALE",
                    variant="primary",
                    size="lg"
                )

                # Présentation Complète
                gr.HTML("""
                <div style="background: linear-gradient(45deg, #1a237e, #3f51b5, #9c27b0); color: white; padding: 15px; border-radius: 15px; margin: 20px 0; text-align: center;">
                    <h3 style="margin: 0 0 8px 0;">📋 PRÉSENTATION COMPLÈTE JARVIS</h3>
                    <p style="margin: 0;">Découvrez TOUTES les fonctions et capacités</p>
                </div>
                """)

                launch_presentation_btn = gr.Button(
                    "📋 VOIR PRÉSENTATION COMPLÈTE",
                    variant="secondary",
                    size="lg"
                )

                # Résultats
                main_result = gr.Textbox(
                    label="Résultats",
                    lines=3,
                    interactive=False
                )
            
            # ONGLET 3: FENÊTRES SPÉCIALISÉES
            with gr.Tab("🪟 Fenêtres Spécialisées"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>🪟 FENÊTRES SPÉCIALISÉES</h2>")

                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>💻 Développement</h3>")
                        launch_code_btn = gr.Button("💻 Éditeur Code", size="sm")
                        launch_thoughts_btn = gr.Button("🧠 Pensées JARVIS", size="sm")
                        launch_config_btn = gr.Button("⚙️ Configuration", size="sm")
                        
                    with gr.Column():
                        gr.HTML("<h3>🎯 Communication</h3>")
                        launch_whatsapp_btn = gr.Button("📱 WhatsApp", size="sm")
                        launch_voice_btn = gr.Button("🎤 Interface Vocale", size="sm")
                        launch_multiagent_btn = gr.Button("🤖 Multi-Agents", size="sm")
                        
                    with gr.Column():
                        gr.HTML("<h3>🔧 Système</h3>")
                        launch_security_btn = gr.Button("🔐 Sécurité", size="sm")
                        launch_monitoring_btn = gr.Button("📊 Monitoring", size="sm")
                        launch_system_btn = gr.Button("📊 Système", size="sm")

                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🧠 Intelligence</h3>")
                        launch_memory_btn = gr.Button("💾 Mémoire Thermique", size="sm")
                        launch_websearch_btn = gr.Button("🌐 Recherche Web", size="sm")
                        launch_accelerators_btn = gr.Button("⚡ Accélérateurs", size="sm")
                        
                    with gr.Column():
                        gr.HTML("<h3>🎨 Créativité</h3>")
                        launch_creative_btn = gr.Button("🎨 Créativité", size="sm")
                        launch_music_btn = gr.Button("🎵 Musique & Audio", size="sm")
                        launch_workspace_btn = gr.Button("📁 Workspace", size="sm")

                # Résultats spécialisés
                specialized_result = gr.Textbox(
                    label="Résultats Fenêtres Spécialisées",
                    lines=3,
                    interactive=False
                )
            
            # ONGLET 4: OUTILS ET TESTS
            with gr.Tab("🧪 Outils et Tests"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>🧪 OUTILS ET TESTS</h2>")

                with gr.Row():
                    with gr.Column():
                        gr.HTML("""
                        <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 10px 0;'>
                            <h3>🧪 Tests Disponibles:</h3>
                            <ul style='text-align: left; line-height: 1.8;'>
                                <li><strong>🧠 Test Neurones</strong> - Validation neurones dynamiques</li>
                                <li><strong>🤖 Test Agents</strong> - Validation tous les agents</li>
                                <li><strong>💾 Test Mémoire</strong> - Test mémoire thermique</li>
                                <li><strong>📊 Monitoring</strong> - Surveillance temps réel</li>
                            </ul>
                        </div>
                        """)
                        
                        test_neurons_btn = gr.Button("🧠 Test Neurones Dynamiques", variant="secondary")
                        test_agents_btn = gr.Button("🤖 Test Agents Complet", variant="secondary")
                        test_memory_btn = gr.Button("💾 Test Mémoire Thermique", variant="secondary")
                        monitoring_btn = gr.Button("📊 Monitoring Temps Réel", variant="secondary")
                        
                    with gr.Column():
                        gr.HTML("""
                        <div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 10px 0;'>
                            <h3>🔧 Outils Maintenance:</h3>
                            <ul style='text-align: left; line-height: 1.8;'>
                                <li><strong>🧹 Nettoyage</strong> - Redémarrage propre</li>
                                <li><strong>✅ Validation</strong> - Vérification système</li>
                                <li><strong>🎯 Centre Contrôle</strong> - Contrôle unifié</li>
                                <li><strong>💾 Sauvegarde</strong> - Protection données</li>
                            </ul>
                        </div>
                        """)
                        
                        cleanup_btn = gr.Button("🧹 Nettoyage Système", variant="secondary")
                        validation_btn = gr.Button("✅ Validation Complète", variant="secondary")
                        control_center_btn = gr.Button("🎯 Centre Contrôle", variant="secondary")

                # Résultats tests
                test_result = gr.Textbox(
                    label="Résultats Tests et Outils",
                    lines=5,
                    interactive=False
                )

            # ONGLET 5: NOUVEAUX SYSTÈMES - JEAN-LUC PASSAVE
            with gr.Tab("🌟 Nouveaux Systèmes"):
                gr.HTML("""
                <style>
                    .dashboard-new-system {
                        background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39) !important;
                        color: white !important;
                        border: none !important;
                        border-radius: 10px !important;
                        font-weight: bold !important;
                        font-size: 1.1em !important;
                        padding: 12px 24px !important;
                        transition: all 0.3s ease !important;
                        box-shadow: 0 5px 20px rgba(76, 175, 80, 0.4) !important;
                    }
                    .dashboard-new-system:hover {
                        background: linear-gradient(45deg, #8BC34A, #CDDC39, #4CAF50) !important;
                        transform: translateY(-3px) !important;
                        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.5) !important;
                    }
                </style>
                <h2 style='text-align: center; color: #333; margin: 20px 0;'>🌟 NOUVEAUX SYSTÈMES JARVIS</h2>
                """)

                with gr.Row():
                    with gr.Column():
                        gr.HTML("""
                        <div style='background: linear-gradient(45deg, #4CAF50, #8BC34A); color: white; padding: 20px; border-radius: 15px; margin: 10px 0;'>
                            <h3 style='margin: 0 0 10px 0; text-align: center;'>💾 SAUVEGARDE & PROTECTION</h3>
                            <ul style='text-align: left; line-height: 1.8; margin: 0; padding-left: 20px;'>
                                <li>💾 Sauvegarde avec choix du lieu</li>
                                <li>🛡️ Monitoring préservatif</li>
                                <li>✅ Validation continue</li>
                            </ul>
                        </div>
                        """)

                        new_backup_btn = gr.Button("💾 Sauvegarde Automatique", variant="primary", elem_classes=["dashboard-new-system"])
                        new_preserve_btn = gr.Button("🛡️ Monitoring Préservatif", variant="primary", elem_classes=["dashboard-new-system"])
                        new_validate_btn = gr.Button("✅ Validation Continue", variant="primary", elem_classes=["dashboard-new-system"])

                    with gr.Column():
                        gr.HTML("""
                        <div style='background: linear-gradient(45deg, #2196F3, #21CBF3); color: white; padding: 20px; border-radius: 15px; margin: 10px 0;'>
                            <h3 style='margin: 0 0 10px 0; text-align: center;'>🎯 CONTRÔLE & DIAGNOSTIC</h3>
                            <ul style='text-align: left; line-height: 1.8; margin: 0; padding-left: 20px;'>
                                <li>🎯 Centre commande unifié</li>
                                <li>🏥 Monitoring santé avancé</li>
                                <li>🤖 Diagnostic agents & mémoire</li>
                            </ul>
                        </div>
                        """)

                        new_command_btn = gr.Button("🎯 Centre Commande Unifié", variant="primary", elem_classes=["dashboard-new-system"])
                        new_health_btn = gr.Button("🏥 Monitoring Santé", variant="primary", elem_classes=["dashboard-new-system"])
                        new_diagnostic_btn = gr.Button("🤖 Diagnostic Agents", variant="primary", elem_classes=["dashboard-new-system"])

                    with gr.Column():
                        gr.HTML("""
                        <div style='background: linear-gradient(45deg, #FF6B6B, #4ECDC4); color: white; padding: 20px; border-radius: 15px; margin: 10px 0;'>
                            <h3 style='margin: 0 0 10px 0; text-align: center;'>🎨 INTERFACE MODERNE</h3>
                            <ul style='text-align: left; line-height: 1.8; margin: 0; padding-left: 20px;'>
                                <li>🎨 Démonstration couleurs</li>
                                <li>✨ Boutons avec effets 3D</li>
                                <li>🌈 Palette complète</li>
                            </ul>
                        </div>
                        """)

                        new_colors_btn = gr.Button("🎨 Démonstration Couleurs", variant="secondary", elem_classes=["dashboard-new-system"])

                        gr.HTML("""
                        <div style='background: linear-gradient(45d, #FF6B35, #F7931E); color: white; padding: 20px; border-radius: 15px; margin: 10px 0;'>
                            <h3 style='margin: 0 0 10px 0; text-align: center;'>🧠 CERVEAU TENSORFLOW</h3>
                            <ul style='text-align: left; line-height: 1.8; margin: 0; padding-left: 20px;'>
                                <li>🎓 QI dynamique évolutif</li>
                                <li>🧠 Réseau neuronal style TensorFlow</li>
                                <li>📊 Métriques d'apprentissage</li>
                            </ul>
                        </div>
                        """)

                        new_tensorflow_btn = gr.Button("🧠 Cerveau TensorFlow", variant="primary", elem_classes=["dashboard-new-system"])

                        gr.HTML("""
                        <div style='background: linear-gradient(45deg, #9C27B0, #E91E63); color: white; padding: 15px; border-radius: 10px; text-align: center; margin: 10px 0;'>
                            <h4 style='margin: 0 0 5px 0;'>🎉 JEAN-LUC PASSAVE</h4>
                            <p style='margin: 0; font-size: 0.9em; opacity: 0.9;'>7 nouveaux systèmes ajoutés !</p>
                            <p style='margin: 5px 0 0 0; font-size: 0.8em; opacity: 0.8;'>Interface colorée et moderne</p>
                        </div>
                        """)

                # Résultats nouveaux systèmes
                new_systems_result = gr.Textbox(
                    label="Résultats Nouveaux Systèmes",
                    lines=3,
                    interactive=False
                )

        # CONNEXIONS DES BOUTONS
        
        # Interfaces principales
        launch_communication_btn.click(
            fn=lambda: open_window("communication"),
            outputs=[main_result]
        )
        
        launch_electron_final_btn.click(
            fn=launch_electron_final_app,
            outputs=[main_result]
        )
        
        launch_presentation_btn.click(
            fn=lambda: open_window("presentation"),
            outputs=[main_result]
        )

        # Fenêtres spécialisées
        launch_code_btn.click(fn=lambda: open_window("code"), outputs=[specialized_result])
        launch_thoughts_btn.click(fn=lambda: open_window("thoughts"), outputs=[specialized_result])
        launch_config_btn.click(fn=lambda: open_window("config"), outputs=[specialized_result])
        launch_whatsapp_btn.click(fn=lambda: open_window("whatsapp"), outputs=[specialized_result])
        launch_voice_btn.click(fn=lambda: open_window("voice"), outputs=[specialized_result])
        launch_multiagent_btn.click(fn=lambda: open_window("multiagent"), outputs=[specialized_result])
        launch_security_btn.click(fn=lambda: open_window("security"), outputs=[specialized_result])
        launch_monitoring_btn.click(fn=lambda: open_window("monitoring"), outputs=[specialized_result])
        launch_system_btn.click(fn=lambda: open_window("system"), outputs=[specialized_result])
        launch_memory_btn.click(fn=lambda: open_window("memory"), outputs=[specialized_result])
        launch_websearch_btn.click(fn=lambda: open_window("websearch"), outputs=[specialized_result])
        launch_accelerators_btn.click(fn=lambda: open_window("accelerators"), outputs=[specialized_result])
        launch_creative_btn.click(fn=lambda: open_window("creative"), outputs=[specialized_result])
        launch_music_btn.click(fn=lambda: open_window("music"), outputs=[specialized_result])
        launch_workspace_btn.click(fn=lambda: open_window("workspace"), outputs=[specialized_result])

        # Tests et outils
        def open_test_url(port):
            url = f"http://localhost:{port}"
            webbrowser.open(url)
            return f"✅ Interface de test ouverte: {url}"

        test_neurons_btn.click(fn=lambda: open_test_url(7898), outputs=[test_result])
        test_agents_btn.click(fn=lambda: open_test_url(7893), outputs=[test_result])
        test_memory_btn.click(fn=lambda: open_test_url(7896), outputs=[test_result])
        monitoring_btn.click(fn=lambda: open_test_url(7894), outputs=[test_result])
        control_center_btn.click(fn=lambda: open_test_url(7897), outputs=[test_result])

        # NOUVEAUX SYSTÈMES - JEAN-LUC PASSAVE
        new_backup_btn.click(fn=lambda: open_test_url(7903), outputs=[new_systems_result])
        new_preserve_btn.click(fn=lambda: open_test_url(7908), outputs=[new_systems_result])
        new_validate_btn.click(fn=lambda: open_test_url(7909), outputs=[new_systems_result])
        new_command_btn.click(fn=lambda: open_test_url(7905), outputs=[new_systems_result])
        new_health_btn.click(fn=lambda: open_test_url(7904), outputs=[new_systems_result])
        new_diagnostic_btn.click(fn=lambda: open_test_url(7906), outputs=[new_systems_result])
        new_colors_btn.click(fn=lambda: open_test_url(7907), outputs=[new_systems_result])
        new_tensorflow_btn.click(fn=lambda: open_test_url(7912), outputs=[new_systems_result])

        # BOUTON RETOUR À LA PAGE PRINCIPALE
        gr.HTML("<hr style='margin: 30px 0;'>")
        with gr.Row():
            home_main_btn = gr.Button("🏠 Retour Page Principale", variant="primary", size="lg")

        def go_to_main_dashboard():
            """Redirige vers la VRAIE page principale"""
            webbrowser.open("http://localhost:7867")
            return "🏠 Redirection vers Page Principale (7867)..."

        home_main_btn.click(fn=go_to_main_dashboard, outputs=[new_systems_result])

        # Footer
        gr.HTML("""
        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>
            <h3 style='color: #333; margin: 0 0 10px 0;'>🎉 JEAN-LUC PASSAVE - JARVIS M4 AVEC ONGLETS</h3>
            <p style='color: #666; margin: 5px 0;'>✅ Dashboard organisé avec onglets pour une navigation claire</p>
            <p style='color: #666; margin: 5px 0;'>🧠 Neurones dynamiques | 🎤 Micro natif | 🍎 Optimisé M4</p>
        </div>
        """)

    return dashboard

if __name__ == "__main__":
    print("🤖 DÉMARRAGE DASHBOARD JARVIS AVEC ONGLETS")
    print("==========================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Dashboard organisé avec onglets")
    print("")
    
    # Créer et lancer le dashboard
    dashboard_app = create_dashboard_with_tabs()
    
    print("✅ Dashboard avec onglets créé")
    print("🌐 Lancement sur http://localhost:7899")
    print("📋 Navigation organisée par onglets")
    
    dashboard_app.launch(
        server_name="127.0.0.1",
        server_port=7899,
        share=False,
        show_error=True,
        quiet=False
    )
