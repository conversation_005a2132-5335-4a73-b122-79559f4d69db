#!/usr/bin/env python3
"""
TEST SYSTÈME PENSÉES ET CRÉATIVITÉ JARVIS - JEAN-LUC PASSAVE
"""

import requests
import json
import time

def test_thoughts_extraction():
    """TEST EXTRACTION DES PENSÉES"""
    print("🧠 TEST EXTRACTION DES PENSÉES")
    print("=" * 50)
    
    # Requête avec instruction explicite pour les pensées
    payload = {
        "model": "deepseek-r1",
        "messages": [
            {
                "role": "system",
                "content": """Tu es JARVIS de Jean-Luc Passave. 

IMPORTANT: Tu DOIS toujours montrer tes pensées avec les balises <think>...</think> avant de répondre.

Exemple:
<think>
Je réfléchis à la question de Jean-Luc...
</think>
Ma réponse finale ici.

Montre TOUJOURS tes pensées !"""
            },
            {
                "role": "user", 
                "content": "Salut JARVIS ! Comment optimiser ma mémoire thermique ? Montre-moi tes pensées !"
            }
        ],
        "max_tokens": 400,
        "temperature": 0.8
    }
    
    try:
        print("🚀 Envoi requête avec instruction pensées...")
        start = time.time()
        
        response = requests.post("http://localhost:8000/v1/chat/completions", 
                               json=payload, timeout=30)
        
        end = time.time()
        
        if response.status_code == 200:
            result = response.json()
            full_response = result['choices'][0]['message']['content']
            
            print(f"✅ Réponse reçue ({end-start:.2f}s)")
            print(f"📝 Réponse complète:")
            print(f"{full_response}")
            
            # Test extraction pensées
            thoughts = ""
            final_response = full_response
            
            if "<think>" in full_response and "</think>" in full_response:
                start_think = full_response.find("<think>") + 7
                end_think = full_response.find("</think>")
                thoughts = full_response[start_think:end_think].strip()
                final_response = full_response[end_think + 8:].strip()
                
                print(f"\n🎯 EXTRACTION RÉUSSIE:")
                print(f"🧠 Pensées: {thoughts}")
                print(f"💬 Réponse: {final_response}")
                
                return True, thoughts, final_response
            else:
                print(f"\n⚠️ AUCUNE PENSÉE DÉTECTÉE")
                print(f"   La réponse ne contient pas de balises <think>...</think>")
                return False, "", full_response
                
        else:
            print(f"❌ Erreur serveur: {response.status_code}")
            return False, "", ""
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False, "", ""

def test_creativity_generation():
    """TEST GÉNÉRATION CRÉATIVE"""
    print("\n🎨 TEST GÉNÉRATION CRÉATIVE")
    print("=" * 50)
    
    creative_prompts = [
        "Génère une idée créative pour améliorer JARVIS",
        "Propose une nouvelle fonctionnalité innovante pour Jean-Luc",
        "Crée un concept original d'interface utilisateur"
    ]
    
    import random
    prompt = random.choice(creative_prompts)
    
    payload = {
        "model": "deepseek-r1",
        "messages": [
            {
                "role": "system",
                "content": """Tu es JARVIS en mode CRÉATIVITÉ pour Jean-Luc Passave.

Tu dois être TRÈS créatif et innovant. Montre tes pensées créatives avec <think>...</think>.

Génère des idées originales et pratiques."""
            },
            {
                "role": "user",
                "content": f"🎨 MODE CRÉATIVITÉ: {prompt}"
            }
        ],
        "max_tokens": 300,
        "temperature": 1.0  # Créativité maximale
    }
    
    try:
        print(f"🎨 Prompt créatif: {prompt}")
        print("🚀 Génération créative...")
        
        start = time.time()
        response = requests.post("http://localhost:8000/v1/chat/completions", 
                               json=payload, timeout=25)
        end = time.time()
        
        if response.status_code == 200:
            result = response.json()
            creative_output = result['choices'][0]['message']['content']
            
            print(f"✅ Création générée ({end-start:.2f}s)")
            print(f"🎨 Contenu créatif:")
            print(f"{creative_output}")
            
            # Sauvegarder la création
            creation_data = {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "prompt": prompt,
                "output": creative_output,
                "temperature": 1.0
            }
            
            try:
                with open("test_creative_outputs.json", "w", encoding="utf-8") as f:
                    json.dump(creation_data, f, ensure_ascii=False, indent=2)
                print(f"💾 Création sauvegardée: test_creative_outputs.json")
            except:
                pass
            
            return True, creative_output
        else:
            print(f"❌ Erreur créativité: {response.status_code}")
            return False, ""
            
    except Exception as e:
        print(f"❌ Erreur génération créative: {e}")
        return False, ""

def main():
    """TEST COMPLET PENSÉES ET CRÉATIVITÉ"""
    print("🚀 TEST COMPLET SYSTÈME PENSÉES ET CRÉATIVITÉ JARVIS")
    print("=" * 70)
    
    # Test 1: Extraction des pensées
    thoughts_success, thoughts, response = test_thoughts_extraction()
    
    # Test 2: Génération créative
    creativity_success, creative_output = test_creativity_generation()
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 70)
    
    if thoughts_success:
        print("✅ PENSÉES: Extraction fonctionnelle")
        print(f"   🧠 Pensées détectées: {len(thoughts)} caractères")
    else:
        print("❌ PENSÉES: Problème d'extraction")
    
    if creativity_success:
        print("✅ CRÉATIVITÉ: Génération fonctionnelle")
        print(f"   🎨 Contenu créé: {len(creative_output)} caractères")
    else:
        print("❌ CRÉATIVITÉ: Problème de génération")
    
    if thoughts_success and creativity_success:
        print("\n🎉 SYSTÈME PENSÉES ET CRÉATIVITÉ: PARFAITEMENT FONCTIONNEL !")
    elif thoughts_success or creativity_success:
        print("\n⚠️ SYSTÈME PENSÉES ET CRÉATIVITÉ: PARTIELLEMENT FONCTIONNEL")
    else:
        print("\n❌ SYSTÈME PENSÉES ET CRÉATIVITÉ: NÉCESSITE CORRECTIONS")
    
    return thoughts_success and creativity_success

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 TOUS LES TESTS RÉUSSIS - SYSTÈME OPÉRATIONNEL !")
    else:
        print("\n🔧 CERTAINS TESTS ÉCHOUÉS - AJUSTEMENTS NÉCESSAIRES")
