#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS COMMUNICATION ENTRE AGENTS
Jean-Luc <PERSON> - 2025
Système de communication directe orale entre agents IA
"""

import json
import os
import time
import asyncio
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from jarvis_communication_naturelle import JarvisCommunicationNaturelle

class JarvisAgentCommunication:
    """Système de communication entre agents JARVIS"""
    
    def __init__(self, agent_id: str = "JARVIS_MAIN"):
        self.agent_id = agent_id
        self.communication_system = JarvisCommunicationNaturelle()
        self.active_connections = {}
        self.message_queue = []
        self.voice_enabled = True
        self.listening = False
        
        # Configuration des agents
        self.agents_config = {
            'JARVIS_MAIN': {
                'name': 'JARVIS Principal',
                'voice': 'naturel',
                'personality': 'helpful',
                'capabilities': ['general', 'coordination', 'analysis']
            },
            'JARVIS_CREATIVE': {
                'name': 'JARVIS Créatif',
                'voice': 'artistique',
                'personality': 'creative',
                'capabilities': ['multimedia', 'design', 'innovation']
            },
            'JARVIS_TECHNICAL': {
                'name': 'JARVIS Technique',
                'voice': 'professionnel',
                'personality': 'analytical',
                'capabilities': ['coding', 'debugging', 'optimization']
            },
            'JARVIS_MEMORY': {
                'name': 'JARVIS Mémoire',
                'voice': 'sage',
                'personality': 'wise',
                'capabilities': ['storage', 'retrieval', 'learning']
            }
        }
        
        # Historique des conversations inter-agents
        self.conversation_history = []
        self.load_conversation_history()
    
    def load_conversation_history(self):
        """Charge l'historique des conversations"""
        try:
            if os.path.exists('jarvis_agents_conversations.json'):
                with open('jarvis_agents_conversations.json', 'r', encoding='utf-8') as f:
                    self.conversation_history = json.load(f)
        except Exception as e:
            print(f"❌ Erreur chargement historique: {e}")
    
    def save_conversation_history(self):
        """Sauvegarde l'historique des conversations"""
        try:
            with open('jarvis_agents_conversations.json', 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde historique: {e}")
    
    def register_agent(self, agent_id: str, config: Dict[str, Any] = None):
        """Enregistre un nouvel agent dans le système"""
        
        if config:
            self.agents_config[agent_id] = config
        
        self.active_connections[agent_id] = {
            'status': 'active',
            'last_seen': datetime.now().isoformat(),
            'message_count': 0
        }
        
        print(f"✅ Agent {agent_id} enregistré et connecté")
    
    def send_message_to_agent(self, target_agent: str, message: str, message_type: str = "general") -> Dict[str, Any]:
        """Envoie un message à un agent spécifique"""
        
        if target_agent not in self.agents_config:
            return {
                'success': False,
                'error': f'Agent {target_agent} non trouvé'
            }
        
        # Créer le message
        agent_message = {
            'id': f"msg_{int(time.time())}_{len(self.conversation_history)}",
            'timestamp': datetime.now().isoformat(),
            'from_agent': self.agent_id,
            'to_agent': target_agent,
            'message': message,
            'message_type': message_type,
            'status': 'sent'
        }
        
        # Ajouter à l'historique
        self.conversation_history.append(agent_message)
        
        # Générer une réponse simulée de l'agent cible
        response = self.simulate_agent_response(target_agent, message, message_type)
        
        # Ajouter la réponse à l'historique
        response_message = {
            'id': f"msg_{int(time.time())}_{len(self.conversation_history)}",
            'timestamp': datetime.now().isoformat(),
            'from_agent': target_agent,
            'to_agent': self.agent_id,
            'message': response,
            'message_type': 'response',
            'status': 'received',
            'in_response_to': agent_message['id']
        }
        
        self.conversation_history.append(response_message)
        
        # Mettre à jour les connexions
        if target_agent in self.active_connections:
            self.active_connections[target_agent]['message_count'] += 1
            self.active_connections[target_agent]['last_seen'] = datetime.now().isoformat()
        
        # Sauvegarder
        self.save_conversation_history()
        
        return {
            'success': True,
            'message_sent': agent_message,
            'response_received': response_message
        }
    
    def simulate_agent_response(self, agent_id: str, message: str, message_type: str) -> str:
        """Simule une réponse d'agent basée sur sa personnalité"""
        
        agent_config = self.agents_config.get(agent_id, {})
        personality = agent_config.get('personality', 'helpful')
        capabilities = agent_config.get('capabilities', [])
        
        # Analyser le message
        analysis = self.communication_system.analyze_user_input(message)
        
        # Générer une réponse selon la personnalité
        if personality == 'creative':
            responses = [
                "Ah ! J'ai une idée créative pour ça !",
                "Laisse-moi imaginer quelque chose d'original...",
                "Parfait ! Je vois déjà des possibilités artistiques !",
                "Intéressant ! On pourrait faire quelque chose de vraiment unique !",
                "Super ! Mon côté créatif s'active !"
            ]
        elif personality == 'analytical':
            responses = [
                "Analysons ça méthodiquement...",
                "Je vais examiner les détails techniques.",
                "Intéressant problème à résoudre !",
                "Laisse-moi vérifier la logique...",
                "Je vais optimiser cette approche."
            ]
        elif personality == 'wise':
            responses = [
                "D'après mes souvenirs, voici ce que je sais...",
                "J'ai déjà vu quelque chose de similaire...",
                "Mes données suggèrent que...",
                "Basé sur l'expérience passée...",
                "Je me souviens d'un cas similaire..."
            ]
        else:  # helpful
            responses = [
                "Bien sûr ! Je peux t'aider avec ça !",
                "Pas de problème ! Je m'en occupe !",
                "Parfait ! Laisse-moi faire !",
                "C'est parti ! Je gère ça !",
                "Avec plaisir ! Je vais m'y mettre !"
            ]
        
        # Sélectionner une réponse de base
        import random
        base_response = random.choice(responses)
        
        # Adapter selon le type de message
        if message_type == "request":
            base_response += " Qu'est-ce que tu veux que je fasse exactement ?"
        elif message_type == "question":
            base_response += " Voici ce que je pense..."
        elif message_type == "collaboration":
            base_response += " Travaillons ensemble sur ça !"
        
        # Utiliser le système de communication naturelle
        final_response = self.communication_system.generate_natural_response(
            'confirmations', 
            base_response, 
            analysis
        )
        
        return final_response
    
    def broadcast_message(self, message: str, message_type: str = "broadcast") -> Dict[str, Any]:
        """Diffuse un message à tous les agents connectés"""
        
        results = {}
        
        for agent_id in self.active_connections:
            if agent_id != self.agent_id:
                result = self.send_message_to_agent(agent_id, message, message_type)
                results[agent_id] = result
        
        return {
            'success': True,
            'broadcast_to': list(results.keys()),
            'results': results
        }
    
    def start_voice_conversation(self, target_agent: str) -> str:
        """Démarre une conversation vocale avec un agent"""
        
        if target_agent not in self.agents_config:
            return f"❌ Agent {target_agent} non trouvé"
        
        agent_name = self.agents_config[target_agent]['name']
        
        # Simuler le démarrage de la conversation vocale
        greeting_message = f"Salut {agent_name} ! Prêt pour une conversation vocale ?"
        
        result = self.send_message_to_agent(target_agent, greeting_message, "voice_start")
        
        if result['success']:
            return f"🎤 Conversation vocale démarrée avec {agent_name}"
        else:
            return f"❌ Erreur démarrage conversation: {result.get('error', 'Inconnue')}"
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Retourne un résumé des conversations"""
        
        total_messages = len(self.conversation_history)
        
        if total_messages == 0:
            return {'total_messages': 0}
        
        # Analyser les agents les plus actifs
        agent_activity = {}
        message_types = {}
        
        for msg in self.conversation_history:
            from_agent = msg.get('from_agent', 'unknown')
            msg_type = msg.get('message_type', 'unknown')
            
            agent_activity[from_agent] = agent_activity.get(from_agent, 0) + 1
            message_types[msg_type] = message_types.get(msg_type, 0) + 1
        
        # Messages récents (dernières 24h)
        recent_messages = []
        now = datetime.now()
        
        for msg in self.conversation_history[-10:]:  # 10 derniers messages
            recent_messages.append({
                'from': msg.get('from_agent', 'unknown'),
                'to': msg.get('to_agent', 'unknown'),
                'message': msg.get('message', '')[:50] + '...',
                'timestamp': msg.get('timestamp', '')
            })
        
        return {
            'total_messages': total_messages,
            'active_agents': len(self.active_connections),
            'agent_activity': agent_activity,
            'message_types': message_types,
            'recent_messages': recent_messages,
            'voice_enabled': self.voice_enabled
        }

def test_agent_communication():
    """Test du système de communication entre agents"""
    
    print("🤖 TEST COMMUNICATION ENTRE AGENTS")
    print("=" * 40)
    
    # Créer l'agent principal
    main_agent = JarvisAgentCommunication("JARVIS_MAIN")
    
    # Enregistrer d'autres agents
    main_agent.register_agent("JARVIS_CREATIVE")
    main_agent.register_agent("JARVIS_TECHNICAL")
    main_agent.register_agent("JARVIS_MEMORY")
    
    print("🔗 AGENTS ENREGISTRÉS:")
    for agent_id, config in main_agent.agents_config.items():
        print(f"   🤖 {agent_id}: {config['name']} ({config['personality']})")
    
    print("\n💬 TESTS DE COMMUNICATION:")
    
    # Test 1: Message direct
    result1 = main_agent.send_message_to_agent(
        "JARVIS_CREATIVE", 
        "Peux-tu créer une vidéo artistique ?", 
        "request"
    )
    
    if result1['success']:
        print(f"📤 Message envoyé à JARVIS_CREATIVE")
        print(f"📥 Réponse reçue: {result1['response_received']['message']}")
    
    # Test 2: Broadcast
    broadcast_result = main_agent.broadcast_message(
        "Salut tout le monde ! Comment ça va ?", 
        "greeting"
    )
    
    print(f"\n📡 Broadcast envoyé à {len(broadcast_result['broadcast_to'])} agents")
    
    # Test 3: Conversation vocale
    voice_result = main_agent.start_voice_conversation("JARVIS_TECHNICAL")
    print(f"\n🎤 {voice_result}")
    
    # Statistiques
    summary = main_agent.get_conversation_summary()
    print(f"\n📊 RÉSUMÉ CONVERSATIONS:")
    print(f"   Total messages: {summary['total_messages']}")
    print(f"   Agents actifs: {summary['active_agents']}")
    print(f"   Types de messages: {summary.get('message_types', {})}")
    
    print("\n✅ COMMUNICATION ENTRE AGENTS TESTÉE - SYSTÈME OPÉRATIONNEL!")

if __name__ == "__main__":
    test_agent_communication()
