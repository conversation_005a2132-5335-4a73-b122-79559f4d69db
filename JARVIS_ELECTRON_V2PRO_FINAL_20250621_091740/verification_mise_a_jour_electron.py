#!/usr/bin/env python3
"""
🔧 VÉRIFICATION MISE À JOUR ELECTRON JARVIS V2 PRO
==================================================

Script de vérification post-mise à jour pour s'assurer que toutes les 
nouvelles fonctionnalités V2 PRO sont correctement intégrées dans 
l'application Electron.

Auteur: <PERSON> + <PERSON>-Luc Passave
Date: 2025-06-21
"""

import os
import sys
import json
import subprocess
import time
import requests
from pathlib import Path

class VerificationElectronV2Pro:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.errors = []
        self.warnings = []
        self.success = []
        
    def print_header(self):
        print("🔧 VÉRIFICATION MISE À JOUR ELECTRON JARVIS V2 PRO")
        print("=" * 60)
        print()
        
    def check_file_integrity(self):
        """Vérifier l'intégrité des fichiers critiques"""
        print("📝 VÉRIFICATION INTÉGRITÉ FICHIERS...")
        
        critical_files = {
            'jarvis_electron_final_complet.js': 'Application Electron principale',
            'jarvis_architecture_multi_fenetres.py': 'Interface JARVIS principale',
            'package.json': 'Configuration Node.js',
            'JARVIS_V2_PRO/app/main.py': 'API V2 PRO'
        }
        
        for file_path, description in critical_files.items():
            full_path = self.base_dir / file_path
            if full_path.exists():
                size = full_path.stat().st_size
                print(f"  ✅ {description}: {size:,} bytes")
                self.success.append(f"Fichier {file_path} présent ({size:,} bytes)")
            else:
                print(f"  ❌ {description}: MANQUANT")
                self.errors.append(f"Fichier critique manquant: {file_path}")
                
    def check_package_json_updates(self):
        """Vérifier les mises à jour du package.json"""
        print("\n📦 VÉRIFICATION PACKAGE.JSON...")
        
        package_path = self.base_dir / 'package.json'
        if not package_path.exists():
            self.errors.append("package.json manquant")
            return
            
        with open(package_path, 'r', encoding='utf-8') as f:
            package_data = json.load(f)
            
        # Vérifications
        checks = [
            ('version', '3.1.0', 'Version mise à jour'),
            ('main', 'jarvis_electron_final_complet.js', 'Point d\'entrée correct'),
        ]
        
        for key, expected, description in checks:
            if package_data.get(key) == expected:
                print(f"  ✅ {description}: {package_data[key]}")
                self.success.append(f"Package.json: {description}")
            else:
                print(f"  ❌ {description}: attendu '{expected}', trouvé '{package_data.get(key)}'")
                self.errors.append(f"Package.json: {description} incorrect")
                
        # Vérifier les nouveaux scripts
        scripts = package_data.get('scripts', {})
        if 'v2pro' in scripts:
            print(f"  ✅ Script V2 PRO ajouté: {scripts['v2pro']}")
            self.success.append("Script V2 PRO configuré")
        else:
            print("  ⚠️ Script V2 PRO manquant")
            self.warnings.append("Script V2 PRO non configuré")
            
    def check_electron_updates(self):
        """Vérifier les mises à jour dans le fichier Electron"""
        print("\n📱 VÉRIFICATION ELECTRON UPDATES...")
        
        electron_path = self.base_dir / 'jarvis_electron_final_complet.js'
        if not electron_path.exists():
            self.errors.append("Fichier Electron principal manquant")
            return
            
        with open(electron_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Vérifications des nouvelles fonctionnalités
        features = [
            ('openV2ProDashboard', 'Fonction dashboard V2 PRO'),
            ('assignWorkToAgent', 'Fonction attribution travail'),
            ('8000/dashboard', 'Lien dashboard V2 PRO'),
            ('8000/diagnostic', 'Lien diagnostic V2 PRO'),
            ('startJarvisV2Pro', 'Démarrage automatique V2 PRO'),
            ('testVoiceForget', 'Test commande oubli amélioré')
        ]
        
        for feature, description in features:
            if feature in content:
                print(f"  ✅ {description}: Présent")
                self.success.append(f"Electron: {description}")
            else:
                print(f"  ❌ {description}: MANQUANT")
                self.errors.append(f"Electron: {description} manquant")
                
    def check_v2pro_api(self):
        """Vérifier que l'API V2 PRO est accessible"""
        print("\n🚀 VÉRIFICATION API V2 PRO...")
        
        endpoints = [
            ('http://localhost:8000/health', 'Health check'),
            ('http://localhost:8000/dashboard', 'Dashboard'),
            ('http://localhost:8000/docs', 'Documentation'),
            ('http://localhost:8000/diagnostic', 'Diagnostic')
        ]
        
        for url, description in endpoints:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"  ✅ {description}: Accessible (200)")
                    self.success.append(f"API V2 PRO: {description} accessible")
                else:
                    print(f"  ⚠️ {description}: Status {response.status_code}")
                    self.warnings.append(f"API V2 PRO: {description} status {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"  ❌ {description}: Non accessible ({str(e)[:50]}...)")
                self.warnings.append(f"API V2 PRO: {description} non accessible")
                
    def check_backup_integrity(self):
        """Vérifier l'intégrité de la sauvegarde"""
        print("\n💾 VÉRIFICATION SAUVEGARDE...")
        
        backup_dirs = list(self.base_dir.glob('JARVIS_COMPLET_VERIFIE_*'))
        if backup_dirs:
            latest_backup = max(backup_dirs, key=lambda x: x.stat().st_mtime)
            print(f"  ✅ Sauvegarde trouvée: {latest_backup.name}")
            
            # Vérifier les fichiers dans la sauvegarde
            backup_files = [
                'jarvis_architecture_multi_fenetres.py',
                'jarvis_electron_final_complet.js',
                'package.json',
                'JARVIS_V2_PRO'
            ]
            
            for file_name in backup_files:
                backup_file = latest_backup / file_name
                if backup_file.exists():
                    print(f"    ✅ {file_name}: Sauvegardé")
                    self.success.append(f"Sauvegarde: {file_name}")
                else:
                    print(f"    ❌ {file_name}: MANQUANT dans sauvegarde")
                    self.errors.append(f"Sauvegarde: {file_name} manquant")
        else:
            print("  ❌ Aucune sauvegarde trouvée")
            self.errors.append("Aucune sauvegarde de sécurité trouvée")
            
    def generate_report(self):
        """Générer le rapport final"""
        print("\n" + "=" * 60)
        print("📊 RAPPORT FINAL DE VÉRIFICATION")
        print("=" * 60)
        
        print(f"\n✅ SUCCÈS ({len(self.success)}):")
        for success in self.success:
            print(f"  • {success}")
            
        if self.warnings:
            print(f"\n⚠️ AVERTISSEMENTS ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  • {warning}")
                
        if self.errors:
            print(f"\n❌ ERREURS ({len(self.errors)}):")
            for error in self.errors:
                print(f"  • {error}")
                
        # Score global
        total_checks = len(self.success) + len(self.warnings) + len(self.errors)
        success_rate = (len(self.success) / total_checks * 100) if total_checks > 0 else 0
        
        print(f"\n🎯 SCORE GLOBAL: {success_rate:.1f}% ({len(self.success)}/{total_checks})")
        
        if success_rate >= 90:
            print("🎉 MISE À JOUR EXCELLENTE ! Toutes les fonctionnalités sont opérationnelles.")
            return True
        elif success_rate >= 75:
            print("✅ MISE À JOUR RÉUSSIE avec quelques avertissements mineurs.")
            return True
        else:
            print("⚠️ MISE À JOUR PARTIELLE - Des corrections sont nécessaires.")
            return False
            
    def run_verification(self):
        """Exécuter la vérification complète"""
        self.print_header()
        
        self.check_file_integrity()
        self.check_package_json_updates()
        self.check_electron_updates()
        self.check_v2pro_api()
        self.check_backup_integrity()
        
        return self.generate_report()

def main():
    """Fonction principale"""
    verifier = VerificationElectronV2Pro()
    success = verifier.run_verification()
    
    if success:
        print("\n🚀 JARVIS ELECTRON V2 PRO PRÊT À L'UTILISATION !")
        print("   Commande: npm run final")
        sys.exit(0)
    else:
        print("\n❌ DES CORRECTIONS SONT NÉCESSAIRES")
        sys.exit(1)

if __name__ == "__main__":
    main()
