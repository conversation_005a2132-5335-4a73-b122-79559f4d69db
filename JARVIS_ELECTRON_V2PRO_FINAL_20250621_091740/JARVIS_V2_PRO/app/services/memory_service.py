#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - SERVICE MÉMOIRE THERMIQUE
Jean-Luc <PERSON> - 2025
Service de mémoire thermique selon architecture ChatGPT
"""

import json
import os
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import hashlib

class MemoryService:
    """Service de mémoire thermique pour JARVIS V2 PRO"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.memory_file = self.data_dir / "memory_thermique.json"
        self.index_file = self.data_dir / "memory_index.json"
        self.calendar_file = self.data_dir / "memory_calendar.json"
        
        # Structures de données
        self.index = {}
        self.calendar = {}
        self.compteur_acces = {}
        self.tags_index = {}
        
        # Charger les données
        self.charger_memoire()
    
    def charger_memoire(self):
        """Charge la mémoire depuis les fichiers"""
        try:
            # Charger l'index principal
            if self.memory_file.exists():
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.index = data.get('index', {})
                    self.compteur_acces = data.get('compteur_acces', {})
                    self.tags_index = data.get('tags_index', {})
            
            # Charger le calendrier
            if self.calendar_file.exists():
                with open(self.calendar_file, 'r', encoding='utf-8') as f:
                    self.calendar = json.load(f)
            
            print(f"💾 Mémoire chargée: {len(self.index)} entrées")
            
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            # Initialiser avec des structures vides
            self.index = {}
            self.calendar = {}
            self.compteur_acces = {}
            self.tags_index = {}
    
    def sauvegarder_memoire(self):
        """Sauvegarde la mémoire dans les fichiers"""
        try:
            # Sauvegarder l'index principal
            data = {
                'index': self.index,
                'compteur_acces': self.compteur_acces,
                'tags_index': self.tags_index,
                'last_save': datetime.now().isoformat(),
                'version': '2.0.0'
            }
            
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            # Sauvegarder le calendrier
            with open(self.calendar_file, 'w', encoding='utf-8') as f:
                json.dump(self.calendar, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire: {e}")
    
    def ajouter(self, cle: str, contenu: Any, date: str = None, 
               tags: List[str] = None, importance: int = 5) -> Dict[str, Any]:
        """Ajoute une entrée à la mémoire thermique"""
        
        if date is None:
            date = datetime.now().isoformat()
        
        if tags is None:
            tags = []
        
        # Créer l'entrée
        entry = {
            "contenu": contenu,
            "date": date,
            "acces": 0,
            "derniere_modification": datetime.now().isoformat(),
            "tags": tags,
            "importance": importance,
            "hash": self._calculer_hash(contenu)
        }
        
        # Ajouter à l'index
        self.index[cle] = entry
        
        # Ajouter au calendrier
        date_key = date.split('T')[0]  # Garder seulement la date
        if date_key not in self.calendar:
            self.calendar[date_key] = []
        self.calendar[date_key].append(cle)
        
        # Indexer les tags
        for tag in tags:
            if tag not in self.tags_index:
                self.tags_index[tag] = []
            if cle not in self.tags_index[tag]:
                self.tags_index[tag].append(cle)
        
        # Sauvegarder
        self.sauvegarder_memoire()
        
        print(f"💾 Mémoire ajoutée: '{cle}' avec {len(tags)} tags")
        
        return entry
    
    def rechercher(self, cle: str) -> Optional[Dict[str, Any]]:
        """Recherche par clé"""
        if cle in self.index:
            # Incrémenter le compteur d'accès
            self.compteur_acces[cle] = self.compteur_acces.get(cle, 0) + 1
            self.index[cle]['acces'] += 1
            
            # Mettre à jour la dernière consultation
            self.index[cle]['derniere_consultation'] = datetime.now().isoformat()
            
            # Sauvegarder les changements
            self.sauvegarder_memoire()
            
            # Retourner l'entrée avec la clé
            result = self.index[cle].copy()
            result['cle'] = cle
            return result
        
        return None
    
    def rechercher_par_date(self, date: str) -> List[Dict[str, Any]]:
        """Recherche par date"""
        date_key = date.split('T')[0]  # Garder seulement la date
        
        if date_key in self.calendar:
            results = []
            for cle in self.calendar[date_key]:
                entry = self.rechercher(cle)
                if entry:
                    results.append(entry)
            return results
        
        return []
    
    def rechercher_fuzzy(self, terme: str, limite: int = 10, 
                        date_debut: str = None, date_fin: str = None,
                        tags: List[str] = None) -> List[Dict[str, Any]]:
        """Recherche floue dans la mémoire"""
        resultats = []
        terme_lower = terme.lower()
        
        for cle, data in self.index.items():
            score = 0
            
            # Recherche dans la clé
            if terme_lower in cle.lower():
                score += 10
            
            # Recherche dans le contenu
            contenu_str = str(data['contenu']).lower()
            if terme_lower in contenu_str:
                score += 5
            
            # Recherche dans les tags
            for tag in data.get('tags', []):
                if terme_lower in tag.lower():
                    score += 3
            
            # Filtrer par date si spécifié
            if date_debut or date_fin:
                entry_date = data['date'].split('T')[0]
                if date_debut and entry_date < date_debut:
                    continue
                if date_fin and entry_date > date_fin:
                    continue
            
            # Filtrer par tags si spécifié
            if tags:
                entry_tags = data.get('tags', [])
                if not any(tag in entry_tags for tag in tags):
                    continue
            
            # Ajouter si score > 0
            if score > 0:
                result = data.copy()
                result['cle'] = cle
                result['score'] = score
                resultats.append(result)
        
        # Trier par score et importance
        resultats.sort(key=lambda x: (x['score'], x.get('importance', 5)), reverse=True)
        
        return resultats[:limite]
    
    def mettre_a_jour(self, cle: str, contenu: Any = None, 
                     tags: List[str] = None, importance: int = None) -> bool:
        """Met à jour une entrée existante"""
        if cle not in self.index:
            return False
        
        entry = self.index[cle]
        
        # Mettre à jour les champs modifiés
        if contenu is not None:
            entry['contenu'] = contenu
            entry['hash'] = self._calculer_hash(contenu)
        
        if tags is not None:
            # Supprimer des anciens tags
            for old_tag in entry.get('tags', []):
                if old_tag in self.tags_index and cle in self.tags_index[old_tag]:
                    self.tags_index[old_tag].remove(cle)
            
            # Ajouter aux nouveaux tags
            entry['tags'] = tags
            for tag in tags:
                if tag not in self.tags_index:
                    self.tags_index[tag] = []
                if cle not in self.tags_index[tag]:
                    self.tags_index[tag].append(cle)
        
        if importance is not None:
            entry['importance'] = importance
        
        # Mettre à jour la date de modification
        entry['derniere_modification'] = datetime.now().isoformat()
        
        # Sauvegarder
        self.sauvegarder_memoire()
        
        return True
    
    def supprimer(self, cle: str) -> bool:
        """Supprime une entrée"""
        if cle not in self.index:
            return False
        
        entry = self.index[cle]
        
        # Supprimer des tags
        for tag in entry.get('tags', []):
            if tag in self.tags_index and cle in self.tags_index[tag]:
                self.tags_index[tag].remove(cle)
                # Supprimer le tag s'il n'y a plus d'entrées
                if not self.tags_index[tag]:
                    del self.tags_index[tag]
        
        # Supprimer du calendrier
        date_key = entry['date'].split('T')[0]
        if date_key in self.calendar and cle in self.calendar[date_key]:
            self.calendar[date_key].remove(cle)
            # Supprimer la date s'il n'y a plus d'entrées
            if not self.calendar[date_key]:
                del self.calendar[date_key]
        
        # Supprimer de l'index principal
        del self.index[cle]
        
        # Supprimer du compteur d'accès
        if cle in self.compteur_acces:
            del self.compteur_acces[cle]
        
        # Sauvegarder
        self.sauvegarder_memoire()
        
        return True
    
    def get_statistiques(self) -> Dict[str, Any]:
        """Retourne les statistiques de la mémoire"""
        total_entries = len(self.index)
        total_acces = sum(self.compteur_acces.values())
        
        # Entrées les plus consultées
        top_entries = sorted(
            self.compteur_acces.items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # Tags les plus utilisés
        tag_counts = {tag: len(entries) for tag, entries in self.tags_index.items()}
        top_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Répartition par importance
        importance_counts = {}
        for entry in self.index.values():
            imp = entry.get('importance', 5)
            importance_counts[imp] = importance_counts.get(imp, 0) + 1
        
        return {
            'total_entries': total_entries,
            'total_acces': total_acces,
            'total_tags': len(self.tags_index),
            'top_entries': top_entries,
            'top_tags': top_tags,
            'importance_distribution': importance_counts,
            'dates_couvertes': len(self.calendar),
            'derniere_modification': max(
                (entry.get('derniere_modification', '') for entry in self.index.values()),
                default=''
            )
        }
    
    def lister_memoires(self, limite: int = 50, offset: int = 0, 
                       tri: str = "date") -> List[Dict[str, Any]]:
        """Liste les mémoires avec pagination"""
        entries = []
        
        for cle, data in self.index.items():
            entry = data.copy()
            entry['cle'] = cle
            entries.append(entry)
        
        # Trier selon le critère
        if tri == "date":
            entries.sort(key=lambda x: x.get('date', ''), reverse=True)
        elif tri == "acces":
            entries.sort(key=lambda x: x.get('acces', 0), reverse=True)
        elif tri == "importance":
            entries.sort(key=lambda x: x.get('importance', 5), reverse=True)
        elif tri == "cle":
            entries.sort(key=lambda x: x['cle'])
        
        # Appliquer la pagination
        return entries[offset:offset + limite]
    
    def get_tous_tags(self) -> List[str]:
        """Retourne tous les tags utilisés"""
        return list(self.tags_index.keys())
    
    def creer_sauvegarde(self) -> str:
        """Crée une sauvegarde de la mémoire"""
        backup_dir = self.data_dir / "backups"
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = backup_dir / f"memory_backup_{timestamp}.json"
        
        backup_data = {
            'index': self.index,
            'calendar': self.calendar,
            'compteur_acces': self.compteur_acces,
            'tags_index': self.tags_index,
            'backup_date': datetime.now().isoformat(),
            'version': '2.0.0'
        }
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, ensure_ascii=False)
        
        return str(backup_path)
    
    def restaurer_sauvegarde(self, backup_path: str) -> bool:
        """Restaure la mémoire depuis une sauvegarde"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return False
            
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # Restaurer les données
            self.index = backup_data.get('index', {})
            self.calendar = backup_data.get('calendar', {})
            self.compteur_acces = backup_data.get('compteur_acces', {})
            self.tags_index = backup_data.get('tags_index', {})
            
            # Sauvegarder
            self.sauvegarder_memoire()
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur restauration: {e}")
            return False
    
    def _calculer_hash(self, contenu: Any) -> str:
        """Calcule un hash du contenu pour détecter les doublons"""
        contenu_str = json.dumps(contenu, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(contenu_str.encode()).hexdigest()

    # === [ FONCTIONS SÉCURITÉ & RGPD ] ===

    def effacer_donnees_personnelles(self, user_identifier: str,
                                   data_types: List[str] = None) -> List[str]:
        """Efface les données personnelles d'un utilisateur (RGPD)"""

        if data_types is None:
            data_types = ["all"]

        deleted_keys = []

        # Parcourir toutes les entrées
        keys_to_delete = []
        for cle, data in self.index.items():
            should_delete = False

            # Vérifier si l'entrée contient des données de cet utilisateur
            contenu_str = str(data['contenu']).lower()
            user_id_lower = user_identifier.lower()

            # Recherche dans le contenu
            if user_id_lower in contenu_str or user_id_lower in cle.lower():

                if "all" in data_types:
                    should_delete = True
                else:
                    # Vérifier les types spécifiques
                    if "conversations" in data_types and any(
                        mot in contenu_str for mot in ["conversation", "message", "dit", "répond"]
                    ):
                        should_delete = True

                    if "preferences" in data_types and any(
                        mot in contenu_str for mot in ["préférence", "aime", "déteste", "config"]
                    ):
                        should_delete = True

                    if "voice_data" in data_types and any(
                        mot in contenu_str for mot in ["voix", "audio", "parole", "reconnaissance"]
                    ):
                        should_delete = True

                    if "face_data" in data_types and any(
                        mot in contenu_str for mot in ["visage", "face", "reconnaissance", "caméra"]
                    ):
                        should_delete = True

            if should_delete:
                keys_to_delete.append(cle)

        # Supprimer les entrées identifiées
        for cle in keys_to_delete:
            if self.supprimer(cle):
                deleted_keys.append(cle)

        print(f"🗑️ Données personnelles effacées: {len(deleted_keys)} entrées pour {user_identifier}")

        return deleted_keys

    def recuperer_donnees_personnelles(self, user_identifier: str) -> List[Dict[str, Any]]:
        """Récupère toutes les données personnelles d'un utilisateur"""

        personal_data = []
        user_id_lower = user_identifier.lower()

        for cle, data in self.index.items():
            contenu_str = str(data['contenu']).lower()

            # Vérifier si l'entrée contient des données de cet utilisateur
            if user_id_lower in contenu_str or user_id_lower in cle.lower():
                entry = data.copy()
                entry['cle'] = cle

                # Classifier le type de données
                entry['data_type'] = self._classifier_type_donnees(contenu_str)

                personal_data.append(entry)

        return personal_data

    def anonymiser_donnees(self, user_identifier: str,
                          keep_analytics: bool = True) -> List[str]:
        """Anonymise les données d'un utilisateur"""

        anonymized_keys = []
        user_id_lower = user_identifier.lower()

        for cle, data in self.index.items():
            contenu_str = str(data['contenu']).lower()

            if user_id_lower in contenu_str or user_id_lower in cle.lower():

                if keep_analytics:
                    # Remplacer l'identifiant par un hash anonyme
                    anonymous_id = hashlib.sha256(user_identifier.encode()).hexdigest()[:8]

                    # Anonymiser le contenu
                    nouveau_contenu = str(data['contenu'])
                    nouveau_contenu = nouveau_contenu.replace(user_identifier, f"USER_{anonymous_id}")

                    # Mettre à jour l'entrée
                    data['contenu'] = nouveau_contenu
                    data['anonymized'] = True
                    data['anonymization_date'] = datetime.now().isoformat()

                    anonymized_keys.append(cle)
                else:
                    # Supprimer complètement
                    if self.supprimer(cle):
                        anonymized_keys.append(cle)

        # Sauvegarder les changements
        self.sauvegarder_memoire()

        return anonymized_keys

    def _classifier_type_donnees(self, contenu_str: str) -> str:
        """Classifie le type de données personnelles"""

        if any(mot in contenu_str for mot in ["conversation", "message", "dit", "répond"]):
            return "conversation"
        elif any(mot in contenu_str for mot in ["préférence", "aime", "déteste", "config"]):
            return "preference"
        elif any(mot in contenu_str for mot in ["voix", "audio", "parole"]):
            return "voice_data"
        elif any(mot in contenu_str for mot in ["visage", "face", "reconnaissance"]):
            return "face_data"
        else:
            return "other"

    def verifier_conformite_rgpd(self) -> Dict[str, Any]:
        """Vérifie la conformité RGPD de la mémoire"""

        # Analyser les données stockées
        total_entries = len(self.index)
        entries_with_personal_data = 0
        anonymized_entries = 0
        data_types_found = set()

        for cle, data in self.index.items():
            contenu_str = str(data['contenu']).lower()

            # Détecter les données personnelles
            has_personal_data = any(
                indicator in contenu_str
                for indicator in ["nom", "prénom", "email", "@", "téléphone", "adresse"]
            )

            if has_personal_data:
                entries_with_personal_data += 1

            if data.get('anonymized', False):
                anonymized_entries += 1

            # Classifier les types de données
            data_type = self._classifier_type_donnees(contenu_str)
            data_types_found.add(data_type)

        return {
            'total_entries': total_entries,
            'entries_with_personal_data': entries_with_personal_data,
            'anonymized_entries': anonymized_entries,
            'data_types_found': list(data_types_found),
            'conformity_score': (anonymized_entries / max(entries_with_personal_data, 1)) * 100,
            'recommendations': self._generer_recommandations_rgpd(
                entries_with_personal_data, anonymized_entries
            )
        }

    def _generer_recommandations_rgpd(self, personal_entries: int,
                                    anonymized_entries: int) -> List[str]:
        """Génère des recommandations pour la conformité RGPD"""

        recommendations = []

        if personal_entries > anonymized_entries:
            recommendations.append(
                "Considérer l'anonymisation des données personnelles non critiques"
            )

        if personal_entries > 100:
            recommendations.append(
                "Mettre en place une politique de rétention des données"
            )

        recommendations.extend([
            "Implémenter un système de consentement utilisateur",
            "Documenter les traitements de données personnelles",
            "Prévoir des sauvegardes sécurisées",
            "Former les utilisateurs aux bonnes pratiques RGPD"
        ])

        return recommendations

# Instance globale du service mémoire
memory_manager = MemoryService()
