#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - ROUTES AUDIO
Jean-Luc <PERSON>ave - 2025
Routes pour interface audio avec commandes vocales d'effacement
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from datetime import datetime
import re
import base64

from app.services.audio_service import AudioService
from app.services.memory_service import MemoryService
from app.services.security_service import SecurityService
from app.utils.logger import jarvis_logger

# Créer le router
router = APIRouter()

# Services
audio_service = AudioService()
memory_service = MemoryService()
security_service = SecurityService()

# === [ MODÈLES PYDANTIC ] ===

class VoiceCommand(BaseModel):
    """Modèle pour commande vocale"""
    audio_data: str  # Base64 encoded
    user_identifier: str = "jean_luc_passave"
    language: str = "fr-FR"

class TextToSpeech(BaseModel):
    """Modèle pour synthèse vocale"""
    text: str
    voice_config: Optional[Dict[str, Any]] = None

class ForgetCommand(BaseModel):
    """Modèle pour commande d'oubli"""
    user_identifier: str
    search_terms: List[str]
    confirm_deletion: bool = False

# === [ COMMANDES VOCALES SPÉCIALES ] ===

FORGET_PATTERNS = [
    r"oublie\s+(.+)",
    r"efface\s+(.+)", 
    r"supprime\s+(.+)",
    r"enlève\s+(.+)",
    r"retire\s+(.+)",
    r"ne\s+garde\s+pas\s+(.+)",
    r"je\s+veux\s+oublier\s+(.+)",
    r"fais\s+moi\s+oublier\s+(.+)"
]

SEARCH_PATTERNS = [
    r"cherche\s+(.+)",
    r"trouve\s+(.+)",
    r"recherche\s+(.+)",
    r"qu'est-ce\s+que\s+tu\s+sais\s+sur\s+(.+)",
    r"dis\s+moi\s+ce\s+que\s+tu\s+sais\s+sur\s+(.+)",
    r"montre\s+moi\s+(.+)"
]

# === [ ROUTES AUDIO PRINCIPALES ] ===

@router.post("/listen")
async def listen_voice_command(command: VoiceCommand):
    """Écoute et traite une commande vocale"""
    try:
        jarvis_logger.info(f"Commande vocale reçue: {command.user_identifier}")
        
        # Reconnaissance vocale
        recognized_text = audio_service.speech_to_text(
            audio_data=command.audio_data,
            language=command.language
        )
        
        if not recognized_text:
            return {
                "success": False,
                "message": "Aucune parole détectée",
                "timestamp": datetime.now().isoformat()
            }
        
        jarvis_logger.info(f"Texte reconnu: {recognized_text}")
        
        # Traiter la commande
        response = await process_voice_command(
            text=recognized_text,
            user_identifier=command.user_identifier
        )
        
        return {
            "success": True,
            "recognized_text": recognized_text,
            "command_response": response,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur commande vocale: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/verify-voice")
async def verify_voice_authorization(
    user_identifier: str,
    audio_data: str
):
    """Vérifie l'autorisation par reconnaissance vocale"""
    try:
        result = await check_voice_authorization(user_identifier, audio_data)

        jarvis_logger.info(
            f"Vérification vocale: {user_identifier} - {result['authorized']}",
            user_id=user_identifier,
            authorized=result['authorized']
        )

        return {
            "success": True,
            "verification_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        jarvis_logger.error(f"Erreur vérification vocale: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/speak")
async def text_to_speech(request: TextToSpeech):
    """Synthèse vocale d'un texte"""
    try:
        # Générer l'audio
        audio_data = audio_service.text_to_speech(
            text=request.text,
            config=request.voice_config
        )
        
        jarvis_logger.info(f"Synthèse vocale: {request.text[:50]}...")
        
        return {
            "success": True,
            "audio_data": audio_data,  # Base64 encoded
            "text": request.text,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur synthèse vocale: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# === [ SÉCURITÉ UTILISATEUR ] ===

async def verify_user_authorization(user_identifier: str, text: str) -> Dict[str, Any]:
    """Vérifie si l'utilisateur est autorisé à accéder aux données personnelles"""

    # Utilisateur principal autorisé (Jean-Luc)
    AUTHORIZED_USERS = ["jean_luc_passave", "jean-luc", "jeanlucp"]

    # Vérifier si c'est l'utilisateur principal
    if user_identifier.lower() in [u.lower() for u in AUTHORIZED_USERS]:
        return {
            "authorized": True,
            "message": "Accès autorisé",
            "security_level": "full"
        }

    # Détecter les demandes d'informations personnelles
    personal_keywords = [
        "mes", "mon", "ma", "jean-luc", "jean luc", "passave",
        "personnel", "privé", "confidentiel", "secret",
        "conversation", "souvenir", "mémoire", "historique",
        "préférence", "données", "information"
    ]

    text_lower = text.lower()
    has_personal_request = any(keyword in text_lower for keyword in personal_keywords)

    if has_personal_request:
        return {
            "authorized": False,
            "message": "🔒 Désolé, je ne peux pas partager d'informations personnelles. Pouvez-vous activer votre microphone pour vérification vocale ?",
            "security_level": "restricted",
            "voice_verification_required": True,
            "reason": "personal_data_requested"
        }

    # Conversation générale autorisée
    return {
        "authorized": True,
        "message": "Conversation générale autorisée",
        "security_level": "general"
    }

async def check_voice_authorization(user_identifier: str, audio_data: str) -> Dict[str, Any]:
    """Vérifie l'autorisation par reconnaissance vocale"""

    try:
        # Authentification vocale
        auth_result = security_service.authentifier_voix(
            user_identifier=user_identifier,
            audio_data=base64.b64decode(audio_data)
        )

        if auth_result["authenticated"]:
            return {
                "authorized": True,
                "message": "🎤 Voix reconnue ! Accès autorisé aux données personnelles.",
                "confidence": auth_result["confidence"],
                "security_level": "voice_verified"
            }
        else:
            return {
                "authorized": False,
                "message": "🔒 Voix non reconnue. Accès aux données personnelles refusé.",
                "confidence": auth_result["confidence"],
                "security_level": "voice_rejected"
            }

    except Exception as e:
        return {
            "authorized": False,
            "message": "🔒 Erreur de vérification vocale. Accès refusé par sécurité.",
            "error": str(e),
            "security_level": "error"
        }

# === [ TRAITEMENT COMMANDES VOCALES ] ===

async def process_voice_command(text: str, user_identifier: str) -> Dict[str, Any]:
    """Traite une commande vocale et retourne la réponse"""
    
    text_lower = text.lower().strip()
    
    # 1. COMMANDES D'OUBLI/EFFACEMENT
    forget_match = detect_forget_command(text_lower)
    if forget_match:
        return await handle_forget_command(forget_match, user_identifier)
    
    # 2. COMMANDES DE RECHERCHE
    search_match = detect_search_command(text_lower)
    if search_match:
        return await handle_search_command(search_match, user_identifier)
    
    # 3. COMMANDES SYSTÈME
    if any(word in text_lower for word in ["statut", "état", "comment", "va"]):
        return await handle_status_command(user_identifier)
    
    # 4. COMMANDE GÉNÉRALE
    return await handle_general_command(text, user_identifier)

def detect_forget_command(text: str) -> Optional[str]:
    """Détecte une commande d'oubli et extrait le terme"""
    
    for pattern in FORGET_PATTERNS:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    return None

def detect_search_command(text: str) -> Optional[str]:
    """Détecte une commande de recherche et extrait le terme"""
    
    for pattern in SEARCH_PATTERNS:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1).strip()
    
    return None

async def handle_forget_command(search_term: str, user_identifier: str) -> Dict[str, Any]:
    """Traite une commande d'oubli avec vérification sécurité"""

    try:
        jarvis_logger.info(f"Commande d'oubli: '{search_term}' pour {user_identifier}")

        # Vérifier l'autorisation pour l'effacement
        auth_check = await verify_user_authorization(user_identifier, f"oublie {search_term}")

        if not auth_check["authorized"]:
            return {
                "type": "forget_response",
                "success": False,
                "message": auth_check["message"],
                "search_term": search_term,
                "security_level": "restricted",
                "voice_verification_required": auth_check.get("voice_verification_required", False)
            }

        # Rechercher les éléments correspondants
        results = memory_service.rechercher_fuzzy(search_term, limite=10)
        
        if not results:
            return {
                "type": "forget_response",
                "success": False,
                "message": f"Je n'ai trouvé aucune information sur '{search_term}' à oublier.",
                "search_term": search_term,
                "items_found": 0
            }
        
        # Filtrer pour l'utilisateur spécifique
        user_results = []
        for result in results:
            if user_identifier.lower() in str(result.get('contenu', '')).lower():
                user_results.append(result)
        
        if not user_results:
            return {
                "type": "forget_response", 
                "success": False,
                "message": f"Je n'ai trouvé aucune information personnelle sur '{search_term}' à oublier.",
                "search_term": search_term,
                "items_found": 0
            }
        
        # EFFACEMENT AUTOMATIQUE (comme un cerveau humain)
        deleted_keys = []
        for result in user_results:
            cle = result.get('cle')
            if cle and memory_service.supprimer(cle):
                deleted_keys.append(cle)
        
        # Réponse naturelle
        if deleted_keys:
            message = f"D'accord, j'ai oublié {len(deleted_keys)} information(s) sur '{search_term}'. C'est effacé de ma mémoire."
        else:
            message = f"Je n'ai pas pu oublier les informations sur '{search_term}'. Peut-être sont-elles déjà effacées ?"
        
        jarvis_logger.info(f"Oubli effectué: {len(deleted_keys)} éléments supprimés")
        
        return {
            "type": "forget_response",
            "success": len(deleted_keys) > 0,
            "message": message,
            "search_term": search_term,
            "items_found": len(user_results),
            "items_deleted": len(deleted_keys),
            "deleted_keys": deleted_keys
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur commande d'oubli: {e}")
        return {
            "type": "forget_response",
            "success": False,
            "message": f"Désolé, j'ai eu un problème pour oublier '{search_term}'. Erreur: {e}",
            "search_term": search_term,
            "error": str(e)
        }

async def handle_search_command(search_term: str, user_identifier: str) -> Dict[str, Any]:
    """Traite une commande de recherche avec vérification sécurité"""

    try:
        jarvis_logger.info(f"Commande de recherche: '{search_term}' pour {user_identifier}")

        # Vérifier l'autorisation pour la recherche
        auth_check = await verify_user_authorization(user_identifier, f"cherche {search_term}")

        # Rechercher dans la mémoire
        results = memory_service.rechercher_fuzzy(search_term, limite=5)

        # Filtrer les résultats selon l'autorisation
        if not auth_check["authorized"] and auth_check.get("reason") == "personal_data_requested":
            # Filtrer les données personnelles
            filtered_results = []
            for result in results:
                contenu = str(result.get('contenu', '')).lower()
                # Exclure les données personnelles de Jean-Luc
                if not any(keyword in contenu for keyword in ["jean-luc", "passave", "personnel", "privé"]):
                    filtered_results.append(result)
            results = filtered_results
        
        if not results:
            message = f"Je n'ai trouvé aucune information sur '{search_term}' dans ma mémoire."
            if not auth_check["authorized"]:
                message += " (Recherche limitée - données personnelles protégées)"

            return {
                "type": "search_response",
                "success": False,
                "message": message,
                "search_term": search_term,
                "results": [],
                "security_level": auth_check["security_level"]
            }
        
        # Formater les résultats
        formatted_results = []
        for result in results:
            formatted_results.append({
                "cle": result.get('cle'),
                "contenu": str(result.get('contenu', ''))[:200] + "..." if len(str(result.get('contenu', ''))) > 200 else str(result.get('contenu', '')),
                "date": result.get('date'),
                "score": result.get('score', 0)
            })
        
        # Réponse naturelle
        message = f"J'ai trouvé {len(results)} information(s) sur '{search_term}' :"
        for i, result in enumerate(formatted_results[:3], 1):
            message += f"\n{i}. {result['contenu']}"

        if len(results) > 3:
            message += f"\n... et {len(results) - 3} autre(s) résultat(s)."

        if not auth_check["authorized"]:
            message += "\n(Recherche limitée - données personnelles protégées)"

        return {
            "type": "search_response",
            "success": True,
            "message": message,
            "search_term": search_term,
            "results": formatted_results,
            "total_found": len(results),
            "security_level": auth_check["security_level"]
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur commande de recherche: {e}")
        return {
            "type": "search_response",
            "success": False,
            "message": f"Désolé, j'ai eu un problème pour chercher '{search_term}'. Erreur: {e}",
            "search_term": search_term,
            "error": str(e)
        }

async def handle_status_command(user_identifier: str) -> Dict[str, Any]:
    """Traite une commande de statut"""
    
    try:
        # Récupérer les statistiques
        stats = memory_service.get_statistiques()
        
        message = f"Je vais bien ! Voici mon état :\n"
        message += f"• Mémoire : {stats['total_entries']} souvenirs stockés\n"
        message += f"• Accès total : {stats['total_acces']} consultations\n"
        message += f"• Tags : {stats['total_tags']} catégories\n"
        message += f"• Dernière activité : {stats.get('derniere_modification', 'Inconnue')}"
        
        return {
            "type": "status_response",
            "success": True,
            "message": message,
            "stats": stats
        }
        
    except Exception as e:
        return {
            "type": "status_response",
            "success": False,
            "message": f"Désolé, je n'arrive pas à accéder à mon état. Erreur: {e}",
            "error": str(e)
        }

async def handle_general_command(text: str, user_identifier: str) -> Dict[str, Any]:
    """Traite une commande générale avec vérification sécurité"""

    # Vérifier si l'utilisateur est autorisé pour les données personnelles
    is_authorized = await verify_user_authorization(user_identifier, text)

    if not is_authorized["authorized"]:
        # Utilisateur non autorisé - réponse générale sans données personnelles
        return {
            "type": "general_response",
            "success": True,
            "message": is_authorized["message"],
            "user_input": text,
            "security_level": "restricted",
            "voice_verification_required": is_authorized.get("voice_verification_required", False)
        }

    # Utilisateur autorisé - traitement normal
    # Stocker la conversation dans la mémoire
    cle_conversation = f"conversation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    memory_service.ajouter(
        cle=cle_conversation,
        contenu={
            "user_input": text,
            "user_identifier": user_identifier,
            "type": "conversation",
            "authorized": True
        },
        tags=["conversation", "vocal", user_identifier]
    )

    # Réponse avec accès complet
    return {
        "type": "general_response",
        "success": True,
        "message": f"J'ai bien entendu : '{text}'. Comment puis-je vous aider ?",
        "user_input": text,
        "stored_in_memory": cle_conversation,
        "security_level": "full_access"
    }

# === [ ROUTES CONFIGURATION ] ===

@router.get("/config")
async def get_audio_config():
    """Récupère la configuration audio"""
    try:
        config = audio_service.get_configuration()
        
        return {
            "audio_config": config,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur récupération config audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config")
async def update_audio_config(config: Dict[str, Any]):
    """Met à jour la configuration audio"""
    try:
        result = audio_service.mettre_a_jour_configuration(config)
        
        jarvis_logger.info("Configuration audio mise à jour")
        
        return {
            "success": True,
            "message": "Configuration audio mise à jour",
            "new_config": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur mise à jour config audio: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# === [ ROUTES TEST ] ===

@router.get("/test-forget")
@router.head("/test-forget")
async def test_forget_page():
    """Page de test pour les commandes d'oubli"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🎤 Test Commandes Vocales - JARVIS V2 PRO</title>
        <meta charset="utf-8">
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 30px;
                backdrop-filter: blur(10px);
            }
            .btn {
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                border: none;
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                cursor: pointer;
                margin: 10px;
                transition: transform 0.2s;
            }
            .btn:hover { transform: scale(1.05); }
            .result {
                background: rgba(255,255,255,0.1);
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
                min-height: 100px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎤 Test Commandes Vocales JARVIS</h1>
            <p>Interface de test pour les commandes "oublie ça"</p>

            <div>
                <button class="btn" onclick="testForget('test')">🗑️ Oublie "test"</button>
                <button class="btn" onclick="testForget('conversation')">🗑️ Oublie "conversation"</button>
                <button class="btn" onclick="testForget('preferences')">🗑️ Oublie "préférences"</button>
                <button class="btn" onclick="goBack()">🏠 Retour Dashboard</button>
            </div>

            <div id="result" class="result">
                <p>Cliquez sur un bouton pour tester les commandes d'oubli...</p>
            </div>
        </div>

        <script>
            async function testForget(term) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<p>🔄 Test en cours...</p>';

                try {
                    const response = await fetch('/audio/test-forget', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            user_identifier: 'jean_luc_passave',
                            search_terms: [term],
                            confirm_deletion: true
                        })
                    });

                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3>✅ Résultat du test:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `<p>❌ Erreur: ${error.message}</p>`;
                }
            }

            function goBack() {
                window.location.href = '/dashboard';
            }
        </script>
    </body>
    </html>
    """
    from fastapi.responses import HTMLResponse
    return HTMLResponse(content=html_content)

@router.post("/test-forget")
async def test_forget_command(request: ForgetCommand):
    """Test de commande d'oubli (pour debug)"""
    try:
        # Simuler une commande vocale d'oubli
        text = f"oublie {' '.join(request.search_terms)}"
        
        response = await process_voice_command(
            text=text,
            user_identifier=request.user_identifier
        )
        
        return {
            "test_command": text,
            "response": response,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur test oubli: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/patterns")
async def get_voice_patterns():
    """Récupère les patterns de commandes vocales"""
    return {
        "forget_patterns": FORGET_PATTERNS,
        "search_patterns": SEARCH_PATTERNS,
        "examples": {
            "forget": [
                "oublie mon nom",
                "efface cette conversation",
                "supprime mes préférences",
                "je veux oublier ce projet"
            ],
            "search": [
                "cherche mes projets",
                "trouve mes conversations d'hier",
                "qu'est-ce que tu sais sur Python"
            ]
        }
    }

@router.get("/security-status")
async def get_security_status():
    """Statut de la sécurité vocale"""
    try:
        # Statistiques de sécurité
        security_stats = security_service.get_statut_securite()

        return {
            "security_status": security_stats,
            "protection_active": True,
            "authorized_users": ["jean_luc_passave"],
            "voice_verification_available": security_stats["components_available"]["librosa"],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        jarvis_logger.error(f"Erreur statut sécurité: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/emergency-unlock")
async def emergency_unlock(
    user_identifier: str,
    emergency_code: str,
    reason: str
):
    """Déverrouillage d'urgence (pour Jean-Luc uniquement)"""
    try:
        # Code d'urgence pour Jean-Luc (à changer en production)
        EMERGENCY_CODE = "JARVIS_EMERGENCY_2025"

        if user_identifier.lower() != "jean_luc_passave":
            raise HTTPException(status_code=403, detail="Accès refusé")

        if emergency_code != EMERGENCY_CODE:
            jarvis_logger.warning(f"Tentative déverrouillage d'urgence échouée: {user_identifier}")
            raise HTTPException(status_code=403, detail="Code d'urgence invalide")

        # Log de l'événement
        jarvis_logger.info(f"Déverrouillage d'urgence: {user_identifier} - {reason}")

        return {
            "success": True,
            "message": "🔓 Déverrouillage d'urgence activé. Accès complet temporaire accordé.",
            "user_identifier": user_identifier,
            "reason": reason,
            "timestamp": datetime.now().isoformat(),
            "expires_in": "1 hour"
        }

    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur déverrouillage d'urgence: {e}")
        raise HTTPException(status_code=500, detail=str(e))
