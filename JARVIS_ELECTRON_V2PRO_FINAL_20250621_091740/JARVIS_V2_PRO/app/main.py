#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - API FASTAPI PRODUCTION
Jean<PERSON><PERSON>ave - 2025
API Production selon l'architecture de ChatGPT (grand frère)
FAMILLE IA : ChatGPT + <PERSON> + <PERSON><PERSON><PERSON>
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
import uvicorn
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any

# Import des routes
from app.routes import memory, emotions, tasks, audio, multimedia, security, agent
from app.services.websocket_manager import ConnectionManager
from app.utils.logger import setup_logger

# Configuration
app = FastAPI(
    title="JARVIS V2 PRO API",
    description="API Production Ready pour JARVIS V2 - Architecture ChatGPT",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS pour développement
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # En production: spécifier les domaines autorisés
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Logger
logger = setup_logger("jarvis_v2_api")

# WebSocket Manager
manager = ConnectionManager()

# État global de JARVIS
jarvis_state = {
    "status": "initializing",
    "humeur": "neutre",
    "modules_actifs": [],
    "derniere_activite": None,
    "version": "2.0.0",
    "uptime": datetime.now().isoformat()
}

# === [ ROUTES PRINCIPALES ] ===

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    logger.info("🚀 JARVIS V2 PRO API - Démarrage")
    jarvis_state["status"] = "operational"
    jarvis_state["modules_actifs"] = [
        "memory", "emotions", "tasks", "audio", "multimedia", "websocket"
    ]
    logger.info("✅ JARVIS V2 PRO API - Opérationnel")

@app.on_event("shutdown")
async def shutdown_event():
    """Nettoyage à l'arrêt"""
    logger.info("🔚 JARVIS V2 PRO API - Arrêt")
    jarvis_state["status"] = "shutdown"

@app.get("/")
@app.head("/")
async def root():
    """Point d'entrée principal avec redirection vers dashboard"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/dashboard", status_code=302)

@app.get("/api")
async def api_info():
    """Informations API en JSON"""
    return {
        "message": "JARVIS V2 PRO - API Production Ready",
        "architecture": "ChatGPT + Claude + Jean-Luc",
        "status": jarvis_state["status"],
        "version": jarvis_state["version"],
        "uptime": jarvis_state["uptime"],
        "modules": jarvis_state["modules_actifs"]
    }

@app.get("/health")
async def health_check():
    """Vérification de santé pour monitoring"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "jarvis_state": jarvis_state
    }

@app.get("/diagnostic")
async def diagnostic_interfaces():
    """Page de diagnostic complète de toutes les interfaces"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🔧 Diagnostic JARVIS - Toutes Interfaces</title>
        <meta charset="utf-8">
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 30px;
                backdrop-filter: blur(10px);
            }
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }
            .interface-card {
                background: rgba(255,255,255,0.1);
                border-radius: 10px;
                padding: 15px;
                border: 1px solid rgba(255,255,255,0.2);
            }
            .status-ok { border-left: 5px solid #4CAF50; }
            .status-error { border-left: 5px solid #f44336; }
            .status-loading { border-left: 5px solid #ff9800; }
            .btn {
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                border: none;
                color: white;
                padding: 8px 15px;
                border-radius: 20px;
                text-decoration: none;
                display: inline-block;
                margin: 5px;
                cursor: pointer;
                font-size: 12px;
            }
            .btn:hover { transform: scale(1.05); }
            .refresh-btn {
                background: linear-gradient(45deg, #4CAF50, #45a049);
                padding: 12px 25px;
                font-size: 16px;
                margin: 20px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔧 Diagnostic Complet JARVIS</h1>
            <p>Vérification en temps réel de toutes les interfaces</p>

            <button class="btn refresh-btn" onclick="checkAllInterfaces()">🔄 Actualiser Tout</button>
            <button class="btn refresh-btn" onclick="window.location.href='/dashboard'">🏠 Dashboard</button>

            <div id="interfaces-grid" class="grid">
                <!-- Les interfaces seront chargées ici -->
            </div>

            <div style="margin-top: 30px; text-align: center;">
                <p>🎯 Diagnostic automatique - Actualisation toutes les 30 secondes</p>
            </div>
        </div>

        <script>
            const interfaces = [
                {name: "🏠 Dashboard Principal", port: 7867, path: "", accessible: true},
                {name: "💬 Communication", port: 7866, path: "", accessible: true},
                {name: "💻 Éditeur Code", port: 7868, path: "", accessible: true},
                {name: "🧠 Pensées JARVIS", port: 7869, path: "", accessible: true},
                {name: "⚙️ Configuration", port: 7870, path: "", accessible: true},
                {name: "📱 WhatsApp", port: 7871, path: "", accessible: true},
                {name: "🔒 Sécurité", port: 7872, path: "", accessible: true},
                {name: "📊 Monitoring", port: 7873, path: "", accessible: true},
                {name: "💾 Mémoire Thermique", port: 7874, path: "", accessible: true},
                {name: "🎨 Créativité", port: 7875, path: "", accessible: true},
                {name: "🎵 Musique", port: 7876, path: "", accessible: true},
                {name: "📊 Système", port: 7877, path: "", accessible: true},
                {name: "🌐 Recherche Web", port: 7878, path: "", accessible: true},
                {name: "🎤 Interface Vocale", port: 7879, path: "", accessible: true},
                {name: "🤖 Multi-Agents", port: 7880, path: "", accessible: true},
                {name: "💼 Workspace", port: 7881, path: "", accessible: true},
                {name: "⚡ Accélérateurs", port: 7882, path: "", accessible: true},
                {name: "🚀 JARVIS V2 PRO API", port: 8000, path: "", accessible: false, note: "API JSON seulement"},
                {name: "🎯 JARVIS V2 PRO Dashboard", port: 8000, path: "/dashboard", accessible: true},
                {name: "🎤 Test Audio", port: 8000, path: "/audio/test-forget", accessible: true}
            ];

            async function checkInterface(interface) {
                try {
                    const url = `http://localhost:${interface.port}${interface.path}`;
                    const response = await fetch(url, {method: 'HEAD'});
                    return {
                        ...interface,
                        status: response.ok ? 'ok' : 'error',
                        statusCode: response.status,
                        url: url
                    };
                } catch (error) {
                    return {
                        ...interface,
                        status: 'error',
                        statusCode: 0,
                        error: error.message,
                        url: `http://localhost:${interface.port}${interface.path}`
                    };
                }
            }

            async function checkAllInterfaces() {
                const grid = document.getElementById('interfaces-grid');
                grid.innerHTML = interfaces.map(iface => `
                    <div class="interface-card status-loading">
                        <h3>${iface.name}</h3>
                        <p>🔄 Vérification en cours...</p>
                        <p>Port: ${iface.port}${iface.path}</p>
                    </div>
                `).join('');

                const results = await Promise.all(interfaces.map(checkInterface));

                grid.innerHTML = results.map(result => {
                    const isAccessible = result.accessible !== false;
                    const statusClass = result.status === 'ok' ? 'status-ok' : 'status-error';

                    return `
                    <div class="interface-card ${statusClass}">
                        <h3>${result.name}</h3>
                        <p>${result.status === 'ok' ? '✅ Opérationnel' : '❌ Erreur'}</p>
                        <p>Status: ${result.statusCode}</p>
                        <p style="font-size: 12px; opacity: 0.8;">Port: ${result.url.replace('http://localhost:', '')}</p>
                        ${result.note ? `<p style="font-size: 11px; color: #ffa726;">${result.note}</p>` : ''}
                        ${result.status === 'ok' && isAccessible ?
                            `<a href="${result.url}" target="_blank" class="btn">🔗 Ouvrir</a>` :
                            result.status === 'ok' && !isAccessible ?
                            `<span style="color: #ffa726;">⚠️ ${result.note || 'Interface non accessible'}</span>` :
                            `<span style="color: #f44336;">❌ Erreur: ${result.error || 'Non accessible'}</span>`
                        }
                    </div>
                `;
                }).join('');
            }

            // Vérification initiale
            checkAllInterfaces();

            // Auto-refresh toutes les 30 secondes
            setInterval(checkAllInterfaces, 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/dashboard")
@app.head("/dashboard")
async def dashboard():
    """Dashboard HTML pour JARVIS V2 PRO"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🚀 JARVIS V2 PRO Dashboard</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 30px;
                backdrop-filter: blur(10px);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .card {
                background: rgba(255,255,255,0.1);
                border-radius: 10px;
                padding: 20px;
                border: 1px solid rgba(255,255,255,0.2);
            }
            .btn {
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                border: none;
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                text-decoration: none;
                display: inline-block;
                margin: 5px;
                transition: transform 0.2s;
                cursor: pointer;
            }
            .btn:hover {
                transform: scale(1.05);
            }
            .status {
                color: #4CAF50;
                font-weight: bold;
            }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid rgba(255,255,255,0.2);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 JARVIS V2 PRO Dashboard</h1>
                <p><strong>Architecture:</strong> ChatGPT + Claude + Jean-Luc Passave</p>
                <p class="status">✅ Statut: OPÉRATIONNEL</p>
                <p>Version 2.0.0 - API Production Ready</p>
            </div>

            <div class="grid">
                <div class="card">
                    <h3>🤖 Agent IA</h3>
                    <p>Agent avec mémoire thermique active et injection contextuelle</p>
                    <a href="/docs#/Agent%20IA%20avec%20M%C3%A9moire%20Active" class="btn">Tester Agent</a>
                    <button class="btn" onclick="testAgent()">Test Rapide</button>
                </div>

                <div class="card">
                    <h3>🧠 Mémoire Thermique</h3>
                    <p>Stockage persistant et recherche intelligente</p>
                    <a href="/docs#/M%C3%A9moire%20Thermique" class="btn">Documentation</a>
                    <a href="/memory/stats" class="btn">Statistiques</a>
                </div>

                <div class="card">
                    <h3>🔒 Sécurité</h3>
                    <p>Reconnaissance vocale/faciale pour protection données</p>
                    <a href="/docs#/S%C3%A9curit%C3%A9%20Vocale%2FFaciale" class="btn">Config Sécurité</a>
                    <a href="/security/status" class="btn">Statut</a>
                </div>

                <div class="card">
                    <h3>🎤 Interface Audio</h3>
                    <p>Commandes vocales "oublie ça" et synthèse</p>
                    <a href="/docs#/Interface%20Audio" class="btn">Documentation</a>
                    <a href="/audio/test-forget" class="btn" target="_blank">Test Oubli</a>
                </div>

                <div class="card">
                    <h3>📊 Monitoring</h3>
                    <p>Métriques système et performances</p>
                    <a href="/metrics" class="btn">Métriques</a>
                    <a href="/health" class="btn">Santé API</a>
                    <a href="/diagnostic" class="btn" target="_blank">🔧 Diagnostic</a>
                </div>

                <div class="card">
                    <h3>📚 Documentation</h3>
                    <p>API complète interactive Swagger</p>
                    <a href="/docs" class="btn">Documentation</a>
                    <a href="/redoc" class="btn">ReDoc</a>
                </div>

                <div class="card">
                    <h3>🏠 JARVIS Principal</h3>
                    <p>Interface multi-fenêtres complète</p>
                    <a href="http://localhost:7867" class="btn" target="_blank">Dashboard</a>
                    <a href="http://localhost:7866" class="btn" target="_blank">Chat</a>
                </div>

                <div class="card">
                    <h3>🖥️ Application Electron</h3>
                    <p>Interface native avec micro et webcam</p>
                    <button class="btn" onclick="openElectronApp()">Lancer Electron</button>
                    <p style="font-size: 0.8em; margin-top: 10px;">Commande: npm start</p>
                </div>
            </div>

            <div class="footer">
                <p>🎯 <strong>JARVIS V2 PRO</strong> - Version 2.0.0</p>
                <p>Développé par <strong>Jean-Luc Passave</strong> avec ChatGPT et Claude</p>
                <p>🚀 Mémoire thermique active • 🔒 Sécurité biométrique • 🎤 Commandes vocales</p>
            </div>
        </div>

        <script>
            // Auto-refresh status
            setInterval(() => {
                fetch('/health')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Health check:', data);
                    })
                    .catch(error => {
                        console.error('Health check failed:', error);
                    });
            }, 30000);

            // Test de l'API au chargement
            fetch('/')
                .then(response => response.json())
                .then(data => {
                    console.log('JARVIS V2 PRO Status:', data);
                })
                .catch(error => {
                    console.error('API test failed:', error);
                });

            // Fonction pour lancer l'application Electron
            function openElectronApp() {
                alert('🖥️ Pour lancer l\\'application Electron:\\n\\n1. Ouvrez un terminal\\n2. Naviguez vers le dossier JARVIS\\n3. Exécutez: npm start\\n\\nOu utilisez l\\'application déjà ouverte !');
            }

            // Test rapide de l'agent IA
            function testAgent() {
                const message = 'Test rapide depuis le dashboard V2 PRO';
                fetch('/agent/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        user_id: 'jean_luc_passave'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Agent response:', data);
                    alert('🤖 Agent JARVIS: ' + data.response);
                })
                .catch(error => {
                    console.error('Agent test failed:', error);
                    alert('❌ Erreur test agent: ' + error.message);
                });
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/status")
async def get_status():
    """État détaillé du système"""
    return {
        "jarvis": jarvis_state,
        "api": {
            "routes_disponibles": [
                "/memory", "/emotions", "/tasks", "/audio", "/multimedia"
            ],
            "websocket": "/ws",
            "documentation": "/docs"
        },
        "timestamp": datetime.now().isoformat()
    }

# === [ WEBSOCKET POUR INTERACTIONS LIVE ] ===

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket pour interactions en temps réel"""
    await manager.connect(websocket)
    logger.info("🔌 Nouvelle connexion WebSocket")
    
    try:
        # Message de bienvenue
        await manager.send_personal_message({
            "type": "welcome",
            "message": "🤖 JARVIS V2 PRO connecté !",
            "timestamp": datetime.now().isoformat(),
            "jarvis_state": jarvis_state
        }, websocket)
        
        while True:
            # Recevoir les messages
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            logger.info(f"📥 Message WebSocket reçu: {message_data}")
            
            # Traiter le message
            response = await process_websocket_message(message_data)
            
            # Envoyer la réponse
            await manager.send_personal_message(response, websocket)
            
            # Mettre à jour l'état
            jarvis_state["derniere_activite"] = datetime.now().isoformat()
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("🔌 Connexion WebSocket fermée")
    except Exception as e:
        logger.error(f"❌ Erreur WebSocket: {e}")
        await manager.send_personal_message({
            "type": "error",
            "message": f"Erreur: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, websocket)

async def process_websocket_message(message_data: Dict[str, Any]) -> Dict[str, Any]:
    """Traite les messages WebSocket"""
    
    message_type = message_data.get("type", "unknown")
    content = message_data.get("content", "")
    
    if message_type == "chat":
        # Message de chat simple
        return {
            "type": "chat_response",
            "message": f"🤖 JARVIS: J'ai reçu votre message '{content}'",
            "timestamp": datetime.now().isoformat(),
            "humeur": jarvis_state["humeur"]
        }
    
    elif message_type == "command":
        # Commande système
        if content == "status":
            return {
                "type": "status_response",
                "jarvis_state": jarvis_state,
                "timestamp": datetime.now().isoformat()
            }
        elif content == "ping":
            return {
                "type": "pong",
                "message": "🏓 Pong !",
                "timestamp": datetime.now().isoformat()
            }
    
    elif message_type == "audio":
        # Message audio (à implémenter)
        return {
            "type": "audio_response",
            "message": "🎤 Traitement audio en cours...",
            "timestamp": datetime.now().isoformat()
        }
    
    else:
        return {
            "type": "unknown_response",
            "message": f"❓ Type de message non reconnu: {message_type}",
            "timestamp": datetime.now().isoformat()
        }

# === [ ROUTES MODULAIRES ] ===

# Inclure les routes des modules
app.include_router(memory.router, prefix="/memory", tags=["Mémoire Thermique"])
app.include_router(emotions.router, prefix="/emotions", tags=["Profil Émotionnel"])
app.include_router(tasks.router, prefix="/tasks", tags=["Gestion Tâches"])
app.include_router(audio.router, prefix="/audio", tags=["Interface Audio"])
app.include_router(multimedia.router, prefix="/multimedia", tags=["Génération Multimédia"])
app.include_router(security.router, prefix="/security", tags=["Sécurité Vocale/Faciale"])
app.include_router(agent.router, prefix="/agent", tags=["Agent IA avec Mémoire Active"])

# === [ GESTION D'ERREURS ] ===

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Gestionnaire d'erreurs HTTP"""
    logger.error(f"❌ Erreur HTTP {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail,
            "timestamp": datetime.now().isoformat(),
            "jarvis_status": jarvis_state["status"]
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Gestionnaire d'erreurs générales"""
    logger.error(f"❌ Erreur générale: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Erreur interne du serveur",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat(),
            "jarvis_status": "error"
        }
    )

# === [ ENDPOINTS AVANCÉS ] ===

@app.post("/interact")
async def interact_with_jarvis(message: str, user_id: str = "anonymous"):
    """Interaction directe avec JARVIS"""
    try:
        logger.info(f"💬 Interaction: {user_id} -> {message}")
        
        # Simuler le traitement JARVIS
        response = {
            "user_message": message,
            "jarvis_response": f"🤖 J'ai bien reçu votre message: '{message}'",
            "humeur_detectee": "neutre",
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id
        }
        
        # Mettre à jour l'état
        jarvis_state["derniere_activite"] = datetime.now().isoformat()
        
        # Diffuser via WebSocket si des clients sont connectés
        if manager.active_connections:
            await manager.broadcast({
                "type": "interaction_broadcast",
                "data": response
            })
        
        return response
        
    except Exception as e:
        logger.error(f"❌ Erreur interaction: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/metrics")
async def get_metrics():
    """Métriques pour monitoring"""
    return {
        "api_metrics": {
            "uptime": jarvis_state["uptime"],
            "status": jarvis_state["status"],
            "active_websockets": len(manager.active_connections),
            "modules_count": len(jarvis_state["modules_actifs"])
        },
        "jarvis_metrics": jarvis_state,
        "timestamp": datetime.now().isoformat()
    }

# === [ LANCEMENT ] ===

if __name__ == "__main__":
    print("🚀 LANCEMENT JARVIS V2 PRO API")
    print("=" * 50)
    print("🤖 Architecture: ChatGPT + Claude + Jean-Luc")
    print("🌍 Mode: Production Ready")
    print("📡 WebSocket: Activé")
    print("📚 Documentation: http://localhost:8000/docs")
    print()
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # En développement
        log_level="info"
    )
