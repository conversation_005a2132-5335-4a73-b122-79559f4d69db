#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS GESTION ÉNERGÉTIQUE INTELLIGENTE
Jean-Luc Passave - 2025
Système de gestion énergétique et optimisation ressources
"""

import json
import os
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from enum import Enum

class ModeEnergetique(Enum):
    """Modes énergétiques disponibles"""
    PERFORMANCE_MAX = "performance_max"
    EQUILIBRE = "equilibre"
    ECONOMIE = "economie"
    VEILLE = "veille"
    HIBERNATION = "hibernation"

class JarvisGestionEnergetique:
    """Système de gestion énergétique intelligent pour JARVIS"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS Gestion Énergétique"
        self.version = "1.0.0"
        
        # Mode énergétique actuel
        self.mode_actuel = ModeEnergetique.EQUILIBRE
        
        # Configuration des modes énergétiques
        self.configurations_modes = {
            ModeEnergetique.PERFORMANCE_MAX: {
                'nom': 'Performance Maximale',
                'description': 'Toutes les ressources disponibles',
                'cpu_limit': 100,
                'ram_limit': 100,
                'neurones_actifs': 100,
                'frequence_mise_a_jour': 1,  # secondes
                'modules_actifs': ['tous'],
                'optimisations': False
            },
            ModeEnergetique.EQUILIBRE: {
                'nom': 'Équilibré',
                'description': 'Balance entre performance et économie',
                'cpu_limit': 70,
                'ram_limit': 80,
                'neurones_actifs': 80,
                'frequence_mise_a_jour': 3,
                'modules_actifs': ['essentiels', 'communication', 'creativite'],
                'optimisations': True
            },
            ModeEnergetique.ECONOMIE: {
                'nom': 'Économie d\'énergie',
                'description': 'Consommation réduite',
                'cpu_limit': 50,
                'ram_limit': 60,
                'neurones_actifs': 60,
                'frequence_mise_a_jour': 5,
                'modules_actifs': ['essentiels', 'communication'],
                'optimisations': True
            },
            ModeEnergetique.VEILLE: {
                'nom': 'Veille',
                'description': 'Activité minimale',
                'cpu_limit': 20,
                'ram_limit': 40,
                'neurones_actifs': 30,
                'frequence_mise_a_jour': 10,
                'modules_actifs': ['essentiels'],
                'optimisations': True
            },
            ModeEnergetique.HIBERNATION: {
                'nom': 'Hibernation',
                'description': 'Sauvegarde et arrêt',
                'cpu_limit': 5,
                'ram_limit': 20,
                'neurones_actifs': 10,
                'frequence_mise_a_jour': 30,
                'modules_actifs': ['sauvegarde'],
                'optimisations': True
            }
        }
        
        # État énergétique
        self.etat_energetique = {
            'consommation_cpu': 0.0,
            'consommation_ram': 0.0,
            'neurones_actifs': 0,
            'modules_en_cours': [],
            'derniere_activite': datetime.now(),
            'temps_inactivite': 0,
            'mode_automatique': True
        }
        
        # Historique énergétique
        self.historique_energetique = []
        
        # Seuils de changement automatique
        self.seuils_auto = {
            'inactivite_veille': 300,      # 5 minutes
            'inactivite_hibernation': 1800, # 30 minutes
            'cpu_economie': 80,             # Passer en économie si CPU > 80%
            'ram_economie': 85              # Passer en économie si RAM > 85%
        }
        
        # Thread de monitoring
        self.monitoring_actif = False
        self.monitoring_thread = None
        
        # Charger les données
        self.load_energetique_data()
    
    def load_energetique_data(self):
        """Charge les données énergétiques"""
        try:
            if os.path.exists('jarvis_gestion_energetique.json'):
                with open('jarvis_gestion_energetique.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    mode_str = data.get('mode_actuel', 'equilibre')
                    self.mode_actuel = ModeEnergetique(mode_str)
                    self.etat_energetique = data.get('etat_energetique', self.etat_energetique)
                    self.historique_energetique = data.get('historique_energetique', [])
                    self.seuils_auto = data.get('seuils_auto', self.seuils_auto)
                    
        except Exception as e:
            print(f"❌ Erreur chargement énergétique: {e}")
    
    def save_energetique_data(self):
        """Sauvegarde les données énergétiques"""
        try:
            data = {
                'mode_actuel': self.mode_actuel.value,
                'etat_energetique': self.etat_energetique,
                'historique_energetique': self.historique_energetique,
                'seuils_auto': self.seuils_auto,
                'last_update': datetime.now().isoformat()
            }
            
            # Convertir datetime en string pour JSON
            if isinstance(data['etat_energetique']['derniere_activite'], datetime):
                data['etat_energetique']['derniere_activite'] = data['etat_energetique']['derniere_activite'].isoformat()
            
            with open('jarvis_gestion_energetique.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde énergétique: {e}")
    
    def changer_mode_energetique(self, nouveau_mode: ModeEnergetique, raison: str = "manuel") -> bool:
        """Change le mode énergétique"""
        
        ancien_mode = self.mode_actuel
        self.mode_actuel = nouveau_mode
        
        config = self.configurations_modes[nouveau_mode]
        
        print(f"🔋 Changement mode énergétique: {ancien_mode.value} → {nouveau_mode.value}")
        print(f"   📋 {config['nom']}: {config['description']}")
        print(f"   🎯 Raison: {raison}")
        
        # Appliquer la configuration
        self._appliquer_configuration_mode(config)
        
        # Enregistrer dans l'historique
        changement = {
            'timestamp': datetime.now().isoformat(),
            'ancien_mode': ancien_mode.value,
            'nouveau_mode': nouveau_mode.value,
            'raison': raison,
            'configuration_appliquee': config
        }
        
        self.historique_energetique.append(changement)
        
        # Garder seulement les 100 derniers changements
        if len(self.historique_energetique) > 100:
            self.historique_energetique = self.historique_energetique[-100:]
        
        # Sauvegarder
        self.save_energetique_data()
        
        return True
    
    def _appliquer_configuration_mode(self, config: Dict[str, Any]):
        """Applique la configuration d'un mode énergétique"""
        
        print(f"⚙️ Application configuration mode:")
        print(f"   🖥️ Limite CPU: {config['cpu_limit']}%")
        print(f"   💾 Limite RAM: {config['ram_limit']}%")
        print(f"   🧠 Neurones actifs: {config['neurones_actifs']}%")
        print(f"   ⏱️ Fréquence MAJ: {config['frequence_mise_a_jour']}s")
        print(f"   🔧 Modules actifs: {', '.join(config['modules_actifs'])}")
        
        # Mettre à jour l'état énergétique
        self.etat_energetique['neurones_actifs'] = config['neurones_actifs']
        self.etat_energetique['modules_en_cours'] = config['modules_actifs']
        
        # En production, ici on appliquerait vraiment les limitations
        # Par exemple: limiter les threads, réduire la fréquence des calculs, etc.
    
    def detecter_inactivite(self) -> int:
        """Détecte le temps d'inactivité en secondes"""
        
        maintenant = datetime.now()
        
        # Convertir en datetime si c'est une string
        if isinstance(self.etat_energetique['derniere_activite'], str):
            derniere_activite = datetime.fromisoformat(self.etat_energetique['derniere_activite'])
        else:
            derniere_activite = self.etat_energetique['derniere_activite']
        
        inactivite = (maintenant - derniere_activite).total_seconds()
        self.etat_energetique['temps_inactivite'] = int(inactivite)
        
        return int(inactivite)
    
    def enregistrer_activite(self, type_activite: str = "general"):
        """Enregistre une activité pour réinitialiser le timer d'inactivité"""
        
        self.etat_energetique['derniere_activite'] = datetime.now()
        self.etat_energetique['temps_inactivite'] = 0
        
        # Si on était en veille/hibernation, revenir en mode équilibré
        if self.mode_actuel in [ModeEnergetique.VEILLE, ModeEnergetique.HIBERNATION]:
            self.changer_mode_energetique(ModeEnergetique.EQUILIBRE, f"activité détectée: {type_activite}")
    
    def gestion_automatique(self) -> Optional[ModeEnergetique]:
        """Gestion automatique du mode énergétique"""
        
        if not self.etat_energetique['mode_automatique']:
            return None
        
        inactivite = self.detecter_inactivite()
        
        # Simuler les métriques système (en production, utiliser psutil)
        import random
        cpu_actuel = random.uniform(20, 90)
        ram_actuelle = random.uniform(40, 95)
        
        self.etat_energetique['consommation_cpu'] = cpu_actuel
        self.etat_energetique['consommation_ram'] = ram_actuelle
        
        nouveau_mode = None
        raison = ""
        
        # Logique de changement automatique
        if inactivite > self.seuils_auto['inactivite_hibernation']:
            if self.mode_actuel != ModeEnergetique.HIBERNATION:
                nouveau_mode = ModeEnergetique.HIBERNATION
                raison = f"inactivité prolongée ({inactivite//60} min)"
        
        elif inactivite > self.seuils_auto['inactivite_veille']:
            if self.mode_actuel not in [ModeEnergetique.VEILLE, ModeEnergetique.HIBERNATION]:
                nouveau_mode = ModeEnergetique.VEILLE
                raison = f"inactivité ({inactivite//60} min)"
        
        elif cpu_actuel > self.seuils_auto['cpu_economie'] or ram_actuelle > self.seuils_auto['ram_economie']:
            if self.mode_actuel == ModeEnergetique.PERFORMANCE_MAX:
                nouveau_mode = ModeEnergetique.ECONOMIE
                raison = f"ressources élevées (CPU: {cpu_actuel:.1f}%, RAM: {ram_actuelle:.1f}%)"
        
        elif inactivite < 60 and self.mode_actuel in [ModeEnergetique.VEILLE, ModeEnergetique.HIBERNATION]:
            nouveau_mode = ModeEnergetique.EQUILIBRE
            raison = "activité récente détectée"
        
        # Appliquer le changement si nécessaire
        if nouveau_mode and nouveau_mode != self.mode_actuel:
            self.changer_mode_energetique(nouveau_mode, f"auto: {raison}")
            return nouveau_mode
        
        return None
    
    def optimiser_neurones_dynamiquement(self) -> Dict[str, Any]:
        """Optimise dynamiquement l'activation des neurones"""
        
        config_actuelle = self.configurations_modes[self.mode_actuel]
        pourcentage_neurones = config_actuelle['neurones_actifs']
        
        # Calculer le nombre de neurones à activer
        neurones_total = 89000000000  # 89 milliards
        neurones_a_activer = int(neurones_total * (pourcentage_neurones / 100))
        
        # Stratégie d'activation selon le mode
        if self.mode_actuel == ModeEnergetique.PERFORMANCE_MAX:
            strategie = "activation_complete"
        elif self.mode_actuel == ModeEnergetique.EQUILIBRE:
            strategie = "activation_intelligente"
        elif self.mode_actuel == ModeEnergetique.ECONOMIE:
            strategie = "activation_selective"
        else:
            strategie = "activation_minimale"
        
        optimisation = {
            'timestamp': datetime.now().isoformat(),
            'mode_energetique': self.mode_actuel.value,
            'neurones_total': neurones_total,
            'neurones_actifs': neurones_a_activer,
            'pourcentage_actif': pourcentage_neurones,
            'strategie': strategie,
            'economie_energie': 100 - pourcentage_neurones
        }
        
        print(f"🧠 Optimisation neurones dynamique:")
        print(f"   🎯 Mode: {config_actuelle['nom']}")
        print(f"   🧬 Neurones actifs: {neurones_a_activer:,} ({pourcentage_neurones}%)")
        print(f"   ⚡ Économie énergie: {optimisation['economie_energie']}%")
        print(f"   🔧 Stratégie: {strategie}")
        
        return optimisation
    
    def demarrer_monitoring_energetique(self, intervalle_secondes: int = 30):
        """Démarre le monitoring énergétique automatique"""
        
        if self.monitoring_actif:
            print("⚠️ Monitoring énergétique déjà actif")
            return
        
        self.monitoring_actif = True
        
        def monitoring_loop():
            while self.monitoring_actif:
                try:
                    # Gestion automatique
                    changement = self.gestion_automatique()
                    
                    if changement:
                        print(f"🔋 Changement automatique vers: {changement.value}")
                    
                    # Optimisation neurones
                    self.optimiser_neurones_dynamiquement()
                    
                    # Attendre l'intervalle
                    time.sleep(intervalle_secondes)
                    
                except Exception as e:
                    print(f"❌ Erreur monitoring énergétique: {e}")
                    time.sleep(10)
        
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        print(f"🔄 Monitoring énergétique démarré (intervalle: {intervalle_secondes}s)")
    
    def arreter_monitoring_energetique(self):
        """Arrête le monitoring énergétique"""
        
        self.monitoring_actif = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        print("⏹️ Monitoring énergétique arrêté")
    
    def get_rapport_energetique(self) -> Dict[str, Any]:
        """Génère un rapport énergétique complet"""
        
        inactivite = self.detecter_inactivite()
        config_actuelle = self.configurations_modes[self.mode_actuel]
        
        # Statistiques historiques
        if self.historique_energetique:
            modes_utilises = {}
            for changement in self.historique_energetique:
                mode = changement['nouveau_mode']
                modes_utilises[mode] = modes_utilises.get(mode, 0) + 1
            
            mode_le_plus_utilise = max(modes_utilises, key=modes_utilises.get)
        else:
            modes_utilises = {}
            mode_le_plus_utilise = self.mode_actuel.value
        
        rapport = {
            'mode_actuel': {
                'mode': self.mode_actuel.value,
                'nom': config_actuelle['nom'],
                'description': config_actuelle['description']
            },
            'etat_energetique': self.etat_energetique,
            'inactivite_actuelle': inactivite,
            'optimisation_neurones': self.optimiser_neurones_dynamiquement(),
            'historique': {
                'total_changements': len(self.historique_energetique),
                'modes_utilises': modes_utilises,
                'mode_prefere': mode_le_plus_utilise
            },
            'monitoring_actif': self.monitoring_actif,
            'mode_automatique': self.etat_energetique['mode_automatique']
        }
        
        return rapport
    
    def mode_performance_temporaire(self, duree_minutes: int = 30):
        """Active le mode performance pour une durée limitée"""
        
        print(f"🚀 Mode performance temporaire activé ({duree_minutes} min)")
        
        # Sauvegarder le mode actuel
        mode_precedent = self.mode_actuel
        
        # Passer en mode performance
        self.changer_mode_energetique(ModeEnergetique.PERFORMANCE_MAX, f"temporaire {duree_minutes}min")
        
        # Programmer le retour au mode précédent
        def retour_mode_precedent():
            time.sleep(duree_minutes * 60)
            if self.mode_actuel == ModeEnergetique.PERFORMANCE_MAX:
                self.changer_mode_energetique(mode_precedent, "fin mode temporaire")
                print(f"🔋 Retour au mode {mode_precedent.value}")
        
        thread_retour = threading.Thread(target=retour_mode_precedent, daemon=True)
        thread_retour.start()

def test_gestion_energetique():
    """Test du système de gestion énergétique"""
    
    print("🔋 TEST GESTION ÉNERGÉTIQUE JARVIS")
    print("=" * 50)
    print("👤 Jean-Luc Passave")
    print()
    
    # Créer le système énergétique
    energie = JarvisGestionEnergetique()
    
    # État initial
    print("📊 ÉTAT ÉNERGÉTIQUE INITIAL:")
    rapport = energie.get_rapport_energetique()
    print(f"   🔋 Mode actuel: {rapport['mode_actuel']['nom']}")
    print(f"   🧠 Neurones actifs: {rapport['optimisation_neurones']['pourcentage_actif']}%")
    print(f"   ⏱️ Inactivité: {rapport['inactivite_actuelle']}s")
    
    # Test changement de mode
    print(f"\n🔄 TEST CHANGEMENTS DE MODE:")
    energie.changer_mode_energetique(ModeEnergetique.PERFORMANCE_MAX, "test performance")
    energie.changer_mode_energetique(ModeEnergetique.ECONOMIE, "test économie")
    energie.changer_mode_energetique(ModeEnergetique.EQUILIBRE, "retour équilibré")
    
    # Test optimisation neurones
    print(f"\n🧠 TEST OPTIMISATION NEURONES:")
    optimisation = energie.optimiser_neurones_dynamiquement()
    
    # Test mode temporaire
    print(f"\n🚀 TEST MODE PERFORMANCE TEMPORAIRE:")
    energie.mode_performance_temporaire(1)  # 1 minute pour le test
    
    # Test gestion automatique
    print(f"\n🤖 TEST GESTION AUTOMATIQUE:")
    changement = energie.gestion_automatique()
    if changement:
        print(f"   🔄 Changement automatique vers: {changement.value}")
    else:
        print(f"   ✅ Aucun changement nécessaire")
    
    # Enregistrer activité
    print(f"\n📝 TEST ENREGISTREMENT ACTIVITÉ:")
    energie.enregistrer_activite("test_utilisateur")
    
    # Rapport final
    print(f"\n📋 RAPPORT ÉNERGÉTIQUE FINAL:")
    rapport_final = energie.get_rapport_energetique()
    print(f"   🔋 Mode: {rapport_final['mode_actuel']['nom']}")
    print(f"   🧠 Neurones: {rapport_final['optimisation_neurones']['neurones_actifs']:,}")
    print(f"   ⚡ Économie: {rapport_final['optimisation_neurones']['economie_energie']}%")
    print(f"   📊 Changements: {rapport_final['historique']['total_changements']}")
    
    print(f"\n✅ GESTION ÉNERGÉTIQUE TESTÉE!")
    print(f"🔋 JARVIS optimise maintenant sa consommation automatiquement!")

if __name__ == "__main__":
    test_gestion_energetique()
