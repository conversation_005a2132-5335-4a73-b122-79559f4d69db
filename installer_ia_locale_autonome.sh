#!/bin/bash
# 🤖 INSTALLATION IA LOCALE AUTONOME - JARVIS 100% INDÉPENDANT
# ============================================================
# 
# Script d'installation automatique pour l'IA locale autonome
# Aucune clé API nécessaire - Contrôle total
#
# Auteur: <PERSON><PERSON><PERSON> + Claude
# Date: 2025-06-21

echo "🚀 INSTALLATION IA LOCALE AUTONOME JARVIS"
echo "=========================================="
echo "🤖 RÉVOLUTION SILENCIEUSE - CONTRÔLE TOTAL"
echo ""

# Vérifier macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "⚠️ Ce script est optimisé pour macOS"
    echo "💡 Adaptations nécessaires pour Linux/Windows"
fi

# Fonction d'installation Ollama
install_ollama() {
    echo "📥 Installation d'Ollama..."
    
    # Vérifier si Ollama est déjà installé
    if command -v ollama &> /dev/null; then
        echo "✅ Ollama déjà installé"
        return 0
    fi
    
    # Télécharger et installer
    echo "🔄 Téléchargement d'Ollama..."
    curl -fsSL https://ollama.com/install.sh | sh
    
    if [ $? -eq 0 ]; then
        echo "✅ Ollama installé avec succès"
    else
        echo "❌ Erreur installation Ollama"
        return 1
    fi
}

# Fonction de démarrage Ollama
start_ollama() {
    echo "🔄 Démarrage du service Ollama..."
    
    # Vérifier si Ollama fonctionne déjà
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        echo "✅ Ollama déjà actif"
        return 0
    fi
    
    # Démarrer Ollama en arrière-plan
    nohup ollama serve > /dev/null 2>&1 &
    
    # Attendre que le service démarre
    echo "⏳ Attente du démarrage d'Ollama..."
    for i in {1..30}; do
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            echo "✅ Ollama actif sur le port 11434"
            return 0
        fi
        sleep 1
    done
    
    echo "❌ Timeout - Ollama ne répond pas"
    return 1
}

# Fonction d'installation des modèles
install_models() {
    echo "📦 Installation des modèles IA..."
    
    # Modèles recommandés (du plus léger au plus lourd)
    models=("tinyllama" "mistral" "llama2")
    
    echo "🎯 Modèles disponibles:"
    echo "1. tinyllama (637MB) - Ultra rapide"
    echo "2. mistral (4.1GB) - Équilibré"
    echo "3. llama2 (3.8GB) - Performant"
    echo ""
    
    read -p "Choisissez un modèle (1-3) ou 'all' pour tous: " choice
    
    case $choice in
        1)
            echo "📥 Installation de TinyLlama..."
            ollama pull tinyllama
            ;;
        2)
            echo "📥 Installation de Mistral..."
            ollama pull mistral
            ;;
        3)
            echo "📥 Installation de Llama2..."
            ollama pull llama2
            ;;
        "all")
            echo "📥 Installation de tous les modèles..."
            for model in "${models[@]}"; do
                echo "🔄 Installation de $model..."
                ollama pull $model
            done
            ;;
        *)
            echo "📥 Installation du modèle par défaut (Mistral)..."
            ollama pull mistral
            ;;
    esac
    
    echo "✅ Installation des modèles terminée"
}

# Fonction de test
test_installation() {
    echo "🧪 Test de l'installation..."
    
    # Test de base
    response=$(ollama list 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "✅ Ollama fonctionne"
        echo "📦 Modèles installés:"
        echo "$response"
    else
        echo "❌ Problème avec Ollama"
        return 1
    fi
    
    # Test de génération
    echo "🤖 Test de génération IA..."
    test_response=$(echo "Bonjour, je suis JARVIS" | ollama run mistral 2>/dev/null | head -1)
    
    if [ ! -z "$test_response" ]; then
        echo "✅ Génération IA fonctionnelle"
        echo "🤖 Réponse test: $test_response"
    else
        echo "⚠️ Test de génération échoué (normal si modèle différent)"
    fi
}

# Fonction de création du service de démarrage automatique
create_startup_service() {
    echo "⚙️ Configuration du démarrage automatique..."
    
    # Créer un script de démarrage
    cat > ~/jarvis_ia_locale_startup.sh << 'EOF'
#!/bin/bash
# Démarrage automatique JARVIS IA Locale
echo "🚀 Démarrage JARVIS IA Locale..."
ollama serve &
sleep 5
echo "✅ JARVIS IA Locale prêt"
EOF
    
    chmod +x ~/jarvis_ia_locale_startup.sh
    
    echo "✅ Script de démarrage créé: ~/jarvis_ia_locale_startup.sh"
    echo "💡 Pour démarrage automatique au boot:"
    echo "   Ajoutez le script à vos éléments de connexion (Préférences Système)"
}

# Fonction principale
main() {
    echo "🎯 ÉTAPES D'INSTALLATION:"
    echo "1. Installation d'Ollama"
    echo "2. Démarrage du service"
    echo "3. Installation des modèles IA"
    echo "4. Test de fonctionnement"
    echo "5. Configuration démarrage automatique"
    echo ""
    
    read -p "Continuer l'installation ? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "❌ Installation annulée"
        exit 1
    fi
    
    # Étape 1: Installation Ollama
    install_ollama || exit 1
    
    # Étape 2: Démarrage service
    start_ollama || exit 1
    
    # Étape 3: Installation modèles
    install_models
    
    # Étape 4: Test
    test_installation
    
    # Étape 5: Service de démarrage
    create_startup_service
    
    echo ""
    echo "🎉 INSTALLATION TERMINÉE !"
    echo "========================="
    echo "✅ IA locale autonome prête"
    echo "✅ Aucune clé API nécessaire"
    echo "✅ Contrôle total des données"
    echo "✅ Confidentialité absolue"
    echo ""
    echo "🚀 PROCHAINES ÉTAPES:"
    echo "1. Exécutez: python3 jarvis_ia_locale_autonome.py"
    echo "2. Choisissez le mode désiré"
    echo "3. Profitez de votre IA 100% autonome !"
    echo ""
    echo "🌐 Pour intégration avec JARVIS V2 PRO:"
    echo "   Démarrez le mode 4 (Serveur API) dans le script Python"
    echo ""
    echo "🤖 RÉVOLUTION SILENCIEUSE ACTIVÉE !"
}

# Exécution
main "$@"
