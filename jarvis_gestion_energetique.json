{"mode_actuel": "performance_max", "etat_energetique": {"consommation_cpu": 0.0, "consommation_ram": 0.0, "neurones_actifs": 100, "modules_en_cours": ["tous"], "derniere_activite": "2025-06-21T03:25:10.056760", "temps_inactivite": 0, "mode_automatique": true}, "historique_energetique": [{"timestamp": "2025-06-21T03:25:10.056910", "ancien_mode": "equilibre", "nouveau_mode": "performance_max", "raison": "test performance", "configuration_appliquee": {"nom": "Performance Maximale", "description": "Toutes les ressources disponibles", "cpu_limit": 100, "ram_limit": 100, "neurones_actifs": 100, "frequence_mise_a_jour": 1, "modules_actifs": ["tous"], "optimisations": false}}, {"timestamp": "2025-06-21T03:25:10.057923", "ancien_mode": "performance_max", "nouveau_mode": "economie", "raison": "test économie", "configuration_appliquee": {"nom": "Économie d'énergie", "description": "Consommation réduite", "cpu_limit": 50, "ram_limit": 60, "neurones_actifs": 60, "frequence_mise_a_jour": 5, "modules_actifs": ["essentiels", "communication"], "optimisations": true}}, {"timestamp": "2025-06-21T03:25:10.060240", "ancien_mode": "economie", "nouveau_mode": "equilibre", "raison": "retour équilibré", "configuration_appliquee": {"nom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Balance entre performance et économie", "cpu_limit": 70, "ram_limit": 80, "neurones_actifs": 80, "frequence_mise_a_jour": 3, "modules_actifs": ["essentiels", "communication", "creativite"], "optimisations": true}}, {"timestamp": "2025-06-21T03:25:10.061165", "ancien_mode": "equilibre", "nouveau_mode": "performance_max", "raison": "temporaire 1min", "configuration_appliquee": {"nom": "Performance Maximale", "description": "Toutes les ressources disponibles", "cpu_limit": 100, "ram_limit": 100, "neurones_actifs": 100, "frequence_mise_a_jour": 1, "modules_actifs": ["tous"], "optimisations": false}}], "seuils_auto": {"inactivite_veille": 300, "inactivite_hibernation": 1800, "cpu_economie": 80, "ram_economie": 85}, "last_update": "2025-06-21T03:25:10.061180"}