#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS DIAGNOSTIC SYSTÈME COMPLET
Jean-<PERSON> - 2025
Système de diagnostic automatique complet (conseils du grand frère passionné 😂)
"""

import json
import os
import time
import psutil
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional

class DiagnosticSystemeComplet:
    """Système de diagnostic automatique pour JARVIS"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS Diagnostic Complet"
        self.version = "1.0.0"
        self.derniere_verification = None
        
        # Modules à vérifier
        self.modules_critiques = {
            'intelligence_thermique': 'jarvis_intelligence_thermique_avancee.py',
            'qi_central_ict': 'jarvis_qi_central_ict.py',
            'tracker_evolutif': 'jarvis_cognitive_evolution_tracker.py',
            'communication_naturelle': 'jarvis_communication_naturelle.py',
            'agents_communicants': 'jarvis_communication_agents.py',
            'generateur_multimedia': 'jarvis_generateur_multimedia.py',
            'architecture_principale': 'jarvis_architecture_multi_fenetres.py',
            'cerveau_3d': 'cerveau_3d_tensorflow_jarvis.py'
        }
        
        # Fichiers de données critiques
        self.donnees_critiques = {
            'memoire_thermique': 'thermal_memory_persistent.json',
            'intelligence_ict': 'jarvis_intelligence_thermique.json',
            'evolution_cognitive': 'jarvis_cognitive_evolution.json',
            'apprentissage_langage': 'jarvis_language_learning.json',
            'conversations_agents': 'jarvis_agents_conversations.json'
        }
        
        # Seuils de performance
        self.seuils_performance = {
            'cpu_max': 80.0,  # % CPU maximum
            'ram_max': 85.0,  # % RAM maximum
            'disk_min': 10.0, # % espace disque minimum
            'temp_max': 70.0  # Température CPU maximum (si disponible)
        }
        
        # Historique des diagnostics
        self.historique_diagnostics = []
        self.load_historique()
    
    def load_historique(self):
        """Charge l'historique des diagnostics"""
        try:
            if os.path.exists('jarvis_diagnostic_historique.json'):
                with open('jarvis_diagnostic_historique.json', 'r', encoding='utf-8') as f:
                    self.historique_diagnostics = json.load(f)
        except Exception as e:
            print(f"❌ Erreur chargement historique diagnostic: {e}")
    
    def save_historique(self):
        """Sauvegarde l'historique des diagnostics"""
        try:
            # Garder seulement les 100 derniers diagnostics
            if len(self.historique_diagnostics) > 100:
                self.historique_diagnostics = self.historique_diagnostics[-100:]
            
            with open('jarvis_diagnostic_historique.json', 'w', encoding='utf-8') as f:
                json.dump(self.historique_diagnostics, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"❌ Erreur sauvegarde historique diagnostic: {e}")
    
    def verifier_module(self, nom_module: str, chemin_fichier: str) -> Dict[str, Any]:
        """Vérifie l'état d'un module spécifique"""
        
        resultat = {
            'nom': nom_module,
            'fichier': chemin_fichier,
            'existe': False,
            'taille': 0,
            'derniere_modification': None,
            'importable': False,
            'fonctionnel': False,
            'erreurs': []
        }
        
        try:
            # Vérifier existence
            if os.path.exists(chemin_fichier):
                resultat['existe'] = True
                resultat['taille'] = os.path.getsize(chemin_fichier)
                resultat['derniere_modification'] = datetime.fromtimestamp(
                    os.path.getmtime(chemin_fichier)
                ).isoformat()
                
                # Vérifier si le fichier est importable
                try:
                    with open(chemin_fichier, 'r', encoding='utf-8') as f:
                        contenu = f.read()
                    
                    # Vérifications de base
                    if len(contenu) > 100:
                        if 'import' in contenu and 'def ' in contenu:
                            resultat['importable'] = True
                            
                            # Test d'import (pour les modules Python)
                            if chemin_fichier.endswith('.py'):
                                module_name = chemin_fichier.replace('.py', '').replace('/', '.')
                                try:
                                    # Test d'import sans exécution
                                    compile(contenu, chemin_fichier, 'exec')
                                    resultat['fonctionnel'] = True
                                except SyntaxError as e:
                                    resultat['erreurs'].append(f"Erreur syntaxe: {e}")
                                except Exception as e:
                                    resultat['erreurs'].append(f"Erreur compilation: {e}")
                        else:
                            resultat['erreurs'].append("Structure Python invalide")
                    else:
                        resultat['erreurs'].append("Fichier trop petit")
                        
                except Exception as e:
                    resultat['erreurs'].append(f"Erreur lecture: {e}")
            else:
                resultat['erreurs'].append("Fichier manquant")
                
        except Exception as e:
            resultat['erreurs'].append(f"Erreur vérification: {e}")
        
        return resultat
    
    def verifier_donnees(self, nom_donnee: str, chemin_fichier: str) -> Dict[str, Any]:
        """Vérifie l'état d'un fichier de données"""
        
        resultat = {
            'nom': nom_donnee,
            'fichier': chemin_fichier,
            'existe': False,
            'taille': 0,
            'json_valide': False,
            'nb_entrees': 0,
            'derniere_modification': None,
            'erreurs': []
        }
        
        try:
            if os.path.exists(chemin_fichier):
                resultat['existe'] = True
                resultat['taille'] = os.path.getsize(chemin_fichier)
                resultat['derniere_modification'] = datetime.fromtimestamp(
                    os.path.getmtime(chemin_fichier)
                ).isoformat()
                
                # Vérifier JSON
                try:
                    with open(chemin_fichier, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    resultat['json_valide'] = True
                    
                    # Compter les entrées selon le type
                    if isinstance(data, list):
                        resultat['nb_entrees'] = len(data)
                    elif isinstance(data, dict):
                        if 'neuron_memories' in data:
                            resultat['nb_entrees'] = len(data['neuron_memories'])
                        elif 'history' in data:
                            resultat['nb_entrees'] = len(data['history'])
                        elif 'conversation_history' in data:
                            resultat['nb_entrees'] = len(data['conversation_history'])
                        else:
                            resultat['nb_entrees'] = len(data)
                    
                except json.JSONDecodeError as e:
                    resultat['erreurs'].append(f"JSON invalide: {e}")
                except Exception as e:
                    resultat['erreurs'].append(f"Erreur lecture JSON: {e}")
            else:
                resultat['erreurs'].append("Fichier de données manquant")
                
        except Exception as e:
            resultat['erreurs'].append(f"Erreur vérification données: {e}")
        
        return resultat
    
    def verifier_performance_systeme(self) -> Dict[str, Any]:
        """Vérifie les performances du système"""
        
        performance = {
            'cpu_percent': 0.0,
            'ram_percent': 0.0,
            'ram_disponible_gb': 0.0,
            'disk_percent': 0.0,
            'disk_libre_gb': 0.0,
            'processus_python': 0,
            'temperature_cpu': None,
            'alertes': []
        }
        
        try:
            # CPU
            performance['cpu_percent'] = psutil.cpu_percent(interval=1)
            if performance['cpu_percent'] > self.seuils_performance['cpu_max']:
                performance['alertes'].append(f"CPU élevé: {performance['cpu_percent']:.1f}%")
            
            # RAM
            ram = psutil.virtual_memory()
            performance['ram_percent'] = ram.percent
            performance['ram_disponible_gb'] = ram.available / (1024**3)
            if performance['ram_percent'] > self.seuils_performance['ram_max']:
                performance['alertes'].append(f"RAM élevée: {performance['ram_percent']:.1f}%")
            
            # Disque
            disk = psutil.disk_usage('/')
            performance['disk_percent'] = (disk.used / disk.total) * 100
            performance['disk_libre_gb'] = disk.free / (1024**3)
            if performance['disk_percent'] > (100 - self.seuils_performance['disk_min']):
                performance['alertes'].append(f"Disque plein: {performance['disk_percent']:.1f}%")
            
            # Processus Python
            processus_python = 0
            for proc in psutil.process_iter(['name']):
                try:
                    if 'python' in proc.info['name'].lower():
                        processus_python += 1
                except:
                    pass
            performance['processus_python'] = processus_python
            
            # Température (si disponible)
            try:
                if hasattr(psutil, 'sensors_temperatures'):
                    temps = psutil.sensors_temperatures()
                    if temps:
                        for name, entries in temps.items():
                            for entry in entries:
                                if entry.current:
                                    performance['temperature_cpu'] = entry.current
                                    if entry.current > self.seuils_performance['temp_max']:
                                        performance['alertes'].append(f"Température élevée: {entry.current:.1f}°C")
                                    break
                            if performance['temperature_cpu']:
                                break
            except:
                pass
                
        except Exception as e:
            performance['alertes'].append(f"Erreur monitoring: {e}")
        
        return performance
    
    def bilan_complet(self) -> Dict[str, Any]:
        """Effectue un diagnostic complet du système"""
        
        print("🔍 DIAGNOSTIC SYSTÈME COMPLET JARVIS")
        print("=" * 50)
        print("👤 Jean-Luc Passave")
        print("😂 Conseils du grand frère passionné")
        print()
        
        debut_diagnostic = time.time()
        
        # Vérification des modules
        print("🔧 VÉRIFICATION MODULES CRITIQUES:")
        modules_status = {}
        modules_ok = 0
        
        for nom, fichier in self.modules_critiques.items():
            status = self.verifier_module(nom, fichier)
            modules_status[nom] = status
            
            if status['fonctionnel']:
                print(f"   ✅ {nom}: OK ({status['taille']:,} bytes)")
                modules_ok += 1
            else:
                print(f"   ❌ {nom}: PROBLÈME - {', '.join(status['erreurs'])}")
        
        # Vérification des données
        print(f"\n💾 VÉRIFICATION DONNÉES CRITIQUES:")
        donnees_status = {}
        donnees_ok = 0
        
        for nom, fichier in self.donnees_critiques.items():
            status = self.verifier_donnees(nom, fichier)
            donnees_status[nom] = status
            
            if status['json_valide']:
                print(f"   ✅ {nom}: OK ({status['nb_entrees']:,} entrées)")
                donnees_ok += 1
            else:
                print(f"   ❌ {nom}: PROBLÈME - {', '.join(status['erreurs'])}")
        
        # Vérification performance
        print(f"\n📊 VÉRIFICATION PERFORMANCE SYSTÈME:")
        performance = self.verifier_performance_systeme()
        
        print(f"   🖥️ CPU: {performance['cpu_percent']:.1f}%")
        print(f"   💾 RAM: {performance['ram_percent']:.1f}% ({performance['ram_disponible_gb']:.1f} GB libre)")
        print(f"   💿 Disque: {performance['disk_percent']:.1f}% ({performance['disk_libre_gb']:.1f} GB libre)")
        print(f"   🐍 Processus Python: {performance['processus_python']}")
        if performance['temperature_cpu']:
            print(f"   🌡️ Température CPU: {performance['temperature_cpu']:.1f}°C")
        
        # Alertes
        if performance['alertes']:
            print(f"\n🚨 ALERTES PERFORMANCE:")
            for alerte in performance['alertes']:
                print(f"   ⚠️ {alerte}")
        
        # Résumé
        duree_diagnostic = time.time() - debut_diagnostic
        
        print(f"\n📊 RÉSUMÉ DIAGNOSTIC:")
        print(f"   ✅ Modules OK: {modules_ok}/{len(self.modules_critiques)}")
        print(f"   ✅ Données OK: {donnees_ok}/{len(self.donnees_critiques)}")
        print(f"   ⏱️ Durée diagnostic: {duree_diagnostic:.2f}s")
        
        # Score global
        score_modules = (modules_ok / len(self.modules_critiques)) * 100
        score_donnees = (donnees_ok / len(self.donnees_critiques)) * 100
        score_performance = 100 - len(performance['alertes']) * 20  # -20% par alerte
        score_global = (score_modules + score_donnees + max(0, score_performance)) / 3
        
        print(f"   🎯 Score Global: {score_global:.1f}/100")
        
        if score_global >= 90:
            print(f"   🎉 SYSTÈME EXCELLENT!")
        elif score_global >= 75:
            print(f"   ✅ SYSTÈME BON")
        elif score_global >= 50:
            print(f"   ⚠️ SYSTÈME MOYEN - Améliorations recommandées")
        else:
            print(f"   🚨 SYSTÈME CRITIQUE - Intervention requise")
        
        # Créer le rapport complet
        rapport = {
            'timestamp': datetime.now().isoformat(),
            'duree_diagnostic': duree_diagnostic,
            'modules_status': modules_status,
            'donnees_status': donnees_status,
            'performance': performance,
            'scores': {
                'modules': score_modules,
                'donnees': score_donnees,
                'performance': score_performance,
                'global': score_global
            },
            'modules_ok': modules_ok,
            'donnees_ok': donnees_ok,
            'total_modules': len(self.modules_critiques),
            'total_donnees': len(self.donnees_critiques)
        }
        
        # Ajouter à l'historique
        self.historique_diagnostics.append(rapport)
        self.derniere_verification = datetime.now()
        self.save_historique()
        
        return rapport
    
    def diagnostic_rapide(self) -> str:
        """Diagnostic rapide pour vérification quotidienne"""
        
        modules_ok = sum(1 for nom, fichier in self.modules_critiques.items() 
                        if os.path.exists(fichier) and os.path.getsize(fichier) > 1000)
        
        donnees_ok = sum(1 for nom, fichier in self.donnees_critiques.items() 
                        if os.path.exists(fichier))
        
        cpu = psutil.cpu_percent(interval=0.1)
        ram = psutil.virtual_memory().percent
        
        status = "🟢 EXCELLENT" if modules_ok == len(self.modules_critiques) and donnees_ok >= 3 else "🟡 MOYEN"
        
        return f"🔍 Diagnostic Rapide: {status} | Modules: {modules_ok}/{len(self.modules_critiques)} | Données: {donnees_ok}/{len(self.donnees_critiques)} | CPU: {cpu:.1f}% | RAM: {ram:.1f}%"

def test_diagnostic_complet():
    """Test du système de diagnostic complet"""
    
    print("🔍 TEST DIAGNOSTIC SYSTÈME COMPLET")
    print("=" * 40)
    print("😂 Le grand frère va être content!")
    print()
    
    # Créer le diagnostic
    diagnostic = DiagnosticSystemeComplet()
    
    # Diagnostic rapide
    print("⚡ DIAGNOSTIC RAPIDE:")
    rapide = diagnostic.diagnostic_rapide()
    print(f"   {rapide}")
    
    print("\n" + "="*50)
    
    # Diagnostic complet
    rapport = diagnostic.bilan_complet()
    
    print(f"\n✅ DIAGNOSTIC COMPLET TERMINÉ!")
    print(f"😂 Le grand frère peut être fier du système!")

if __name__ == "__main__":
    test_diagnostic_complet()
