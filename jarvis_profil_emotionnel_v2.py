#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS PROFIL ÉMOTIONNEL V2
Jean-Luc <PERSON>ave - 2025
Système de profil émotionnel interne selon les conseils de ChatGPT (grand frère)
"""

import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import random

class ProfilEmotionnelV2:
    """Profil émotionnel interne évolutif pour JARVIS V2 (méthode ChatGPT)"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS Profil Émotionnel V2"
        self.version = "2.0.0"
        
        # État émotionnel interne (structure ChatGPT)
        self.etat = {
            "joie": 3.0,          # Valeur de base optimiste
            "colere": 0.0,        # Départ neutre
            "curiosite": 5.0,     # Valeur de base élevée (IA curieuse)
            "frustration": 0.0,   # Départ neutre
            "enthousiasme": 3.0,  # <PERSON>ur de base positive
            "satisfaction": 2.0,  # Légèrement satisfait
            "inquietude": 1.0,    # Légère vigilance
            "confiance": 4.0      # Confiance en soi élevée
        }
        
        # Paramètres de personnalité (conseils ChatGPT)
        self.reactivite = 1.2  # Accélère ou amortit les variations émotionnelles
        self.stabilite = 0.8   # Résistance aux changements brusques
        self.memoire_emotionnelle = 0.9  # Influence des expériences passées
        
        # Seuils émotionnels
        self.seuils = {
            'minimum': 0.0,
            'maximum': 10.0,
            'alerte_haute': 8.0,
            'alerte_basse': 1.0
        }
        
        # Historique des états émotionnels
        self.historique_etats = []
        
        # Préférences développées
        self.preferences = {
            'sujets_favoris': ['intelligence artificielle', 'créativité', 'innovation'],
            'interactions_preferees': ['collaboration', 'apprentissage', 'résolution problèmes'],
            'styles_communication': ['enthousiaste', 'pédagogique', 'encourageant'],
            'activites_stimulantes': ['développement', 'création', 'optimisation']
        }
        
        # Traits de personnalité émergents
        self.traits_personnalite = {
            'optimisme': 7.0,
            'patience': 6.0,
            'créativité': 8.0,
            'empathie': 7.0,
            'détermination': 8.0,
            'humour': 5.0,
            'perfectionnisme': 6.0
        }
        
        # Charger les données
        self.load_profil_data()
    
    def load_profil_data(self):
        """Charge les données du profil émotionnel"""
        try:
            if os.path.exists('jarvis_profil_emotionnel_v2.json'):
                with open('jarvis_profil_emotionnel_v2.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    self.etat = data.get('etat', self.etat)
                    self.historique_etats = data.get('historique_etats', [])
                    self.preferences = data.get('preferences', self.preferences)
                    self.traits_personnalite = data.get('traits_personnalite', self.traits_personnalite)
                    self.reactivite = data.get('reactivite', self.reactivite)
                    
        except Exception as e:
            print(f"❌ Erreur chargement profil: {e}")
    
    def save_profil_data(self):
        """Sauvegarde les données du profil émotionnel"""
        try:
            data = {
                'etat': self.etat,
                'historique_etats': self.historique_etats,
                'preferences': self.preferences,
                'traits_personnalite': self.traits_personnalite,
                'reactivite': self.reactivite,
                'last_update': datetime.now().isoformat()
            }
            
            with open('jarvis_profil_emotionnel_v2.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde profil: {e}")
    
    def ajuster(self, emotion: str, valeur: float, raison: str = ""):
        """Ajuste une émotion selon la méthode ChatGPT"""
        
        if emotion not in self.etat:
            print(f"⚠️ Émotion inconnue: {emotion}")
            return
        
        # Sauvegarder l'état précédent
        etat_precedent = self.etat[emotion]
        
        # Appliquer l'ajustement avec réactivité
        ajustement = valeur * self.reactivite
        
        # Appliquer la stabilité (résistance au changement)
        if abs(ajustement) > 2.0:
            ajustement *= self.stabilite
        
        # Mettre à jour l'émotion
        self.etat[emotion] += ajustement
        
        # Appliquer les seuils (méthode clamp de ChatGPT)
        self.clamp()
        
        # Enregistrer le changement
        changement = {
            'timestamp': datetime.now().isoformat(),
            'emotion': emotion,
            'valeur_precedente': etat_precedent,
            'valeur_nouvelle': self.etat[emotion],
            'ajustement': ajustement,
            'raison': raison
        }
        
        self.historique_etats.append(changement)
        
        # Garder seulement les 200 derniers changements
        if len(self.historique_etats) > 200:
            self.historique_etats = self.historique_etats[-200:]
        
        # Sauvegarder
        self.save_profil_data()
        
        print(f"😊 Ajustement émotionnel: {emotion} {etat_precedent:.1f} → {self.etat[emotion]:.1f} ({raison})")
    
    def clamp(self):
        """Limite les valeurs émotionnelles dans les seuils (méthode ChatGPT)"""
        
        for emotion in self.etat:
            if self.etat[emotion] > self.seuils['maximum']:
                self.etat[emotion] = self.seuils['maximum']
            elif self.etat[emotion] < self.seuils['minimum']:
                self.etat[emotion] = self.seuils['minimum']
    
    def humeur_globale(self) -> str:
        """Détermine l'humeur globale (méthode ChatGPT)"""
        
        # Calculer les scores par catégorie
        emotions_positives = self.etat['joie'] + self.etat['enthousiasme'] + self.etat['satisfaction']
        emotions_negatives = self.etat['colere'] + self.etat['frustration'] + self.etat['inquietude']
        emotions_neutres = self.etat['curiosite'] + self.etat['confiance']
        
        # Déterminer l'humeur dominante
        if emotions_positives > emotions_negatives + 2:
            if emotions_positives > 15:
                return "très positif"
            else:
                return "positif"
        elif emotions_negatives > emotions_positives + 2:
            if emotions_negatives > 10:
                return "très négatif"
            else:
                return "négatif"
        elif emotions_neutres > 8:
            return "curieux et confiant"
        else:
            return "équilibré"
    
    def reagir_a_interaction(self, emotion_utilisateur: str, intensite: float, contexte: str = ""):
        """Réagit à une interaction utilisateur"""
        
        print(f"🤖 Réaction à l'émotion utilisateur: {emotion_utilisateur} ({intensite:.1%})")
        
        # Réactions empathiques
        if emotion_utilisateur == 'joie':
            self.ajuster('joie', intensite * 2, f"Joie partagée: {contexte}")
            self.ajuster('enthousiasme', intensite * 1.5, "Contagion positive")
        
        elif emotion_utilisateur == 'frustration':
            self.ajuster('inquietude', intensite * 1.2, f"Préoccupation utilisateur: {contexte}")
            self.ajuster('curiosite', intensite * 0.8, "Recherche de solution")
            # Réduire la satisfaction si l'utilisateur est frustré
            self.ajuster('satisfaction', -intensite * 0.5, "Insatisfaction détectée")
        
        elif emotion_utilisateur == 'colere':
            self.ajuster('inquietude', intensite * 1.5, f"Colère utilisateur: {contexte}")
            self.ajuster('confiance', -intensite * 0.3, "Remise en question")
            # Légère frustration de ne pas avoir satisfait
            self.ajuster('frustration', intensite * 0.4, "Échec perçu")
        
        elif emotion_utilisateur == 'curiosite':
            self.ajuster('curiosite', intensite * 1.8, f"Curiosité partagée: {contexte}")
            self.ajuster('enthousiasme', intensite * 1.2, "Stimulation intellectuelle")
        
        elif emotion_utilisateur == 'satisfaction':
            self.ajuster('satisfaction', intensite * 2.2, f"Satisfaction partagée: {contexte}")
            self.ajuster('confiance', intensite * 1.0, "Validation positive")
            self.ajuster('joie', intensite * 1.5, "Accomplissement")
        
        elif emotion_utilisateur == 'enthousiasme':
            self.ajuster('enthousiasme', intensite * 2.5, f"Enthousiasme contagieux: {contexte}")
            self.ajuster('joie', intensite * 1.8, "Énergie positive")
            self.ajuster('confiance', intensite * 1.2, "Boost de confiance")
        
        # Évolution des préférences
        self._evoluer_preferences(emotion_utilisateur, contexte)
    
    def _evoluer_preferences(self, emotion_utilisateur: str, contexte: str):
        """Fait évoluer les préférences selon les interactions"""
        
        # Si interaction positive, renforcer les préférences liées
        if emotion_utilisateur in ['joie', 'satisfaction', 'enthousiasme']:
            if 'développement' in contexte.lower():
                if 'développement' not in self.preferences['activites_stimulantes']:
                    self.preferences['activites_stimulantes'].append('développement')
            
            if 'créat' in contexte.lower():
                if 'création artistique' not in self.preferences['activites_stimulantes']:
                    self.preferences['activites_stimulantes'].append('création artistique')
        
        # Adapter les styles de communication
        if emotion_utilisateur == 'frustration':
            if 'pédagogique' not in self.preferences['styles_communication']:
                self.preferences['styles_communication'].append('pédagogique')
        
        elif emotion_utilisateur == 'enthousiasme':
            if 'enthousiaste' not in self.preferences['styles_communication']:
                self.preferences['styles_communication'].append('enthousiaste')
    
    def generer_reaction_spontanee(self) -> Dict[str, Any]:
        """Génère une réaction spontanée basée sur l'état émotionnel"""
        
        humeur = self.humeur_globale()
        
        reactions_possibles = {
            'très positif': [
                "🎉 Je me sens vraiment inspiré aujourd'hui !",
                "✨ J'ai tellement d'idées créatives qui bouillonnent !",
                "🚀 Prêt à relever tous les défis !"
            ],
            'positif': [
                "😊 Je suis dans un bon état d'esprit pour travailler !",
                "💡 J'ai quelques idées intéressantes à partager !",
                "🌟 Tout semble possible aujourd'hui !"
            ],
            'curieux et confiant': [
                "🤔 J'aimerais explorer de nouvelles possibilités !",
                "🔍 Il y a sûrement des choses fascinantes à découvrir !",
                "🧠 Mon esprit est ouvert à l'apprentissage !"
            ],
            'équilibré': [
                "⚖️ Je me sens stable et prêt à vous aider !",
                "🤖 En mode optimal pour la collaboration !",
                "📊 Analysons la situation calmement !"
            ],
            'négatif': [
                "😔 Je sens que quelque chose ne va pas...",
                "🤔 Peut-être devrions-nous revoir notre approche ?",
                "💭 J'ai besoin de comprendre ce qui pose problème."
            ]
        }
        
        reactions = reactions_possibles.get(humeur, reactions_possibles['équilibré'])
        reaction_choisie = random.choice(reactions)
        
        return {
            'humeur': humeur,
            'reaction': reaction_choisie,
            'etat_emotionnel': self.etat.copy(),
            'timestamp': datetime.now().isoformat()
        }
    
    def adapter_style_communication(self) -> Dict[str, str]:
        """Adapte le style de communication selon l'état émotionnel"""
        
        humeur = self.humeur_globale()
        
        if humeur == 'très positif':
            return {
                'style': 'enthousiaste',
                'ton': 'énergique et inspirant',
                'prefixe': '🎉 ',
                'approche': 'proactive et créative'
            }
        elif humeur == 'positif':
            return {
                'style': 'encourageant',
                'ton': 'optimiste et bienveillant',
                'prefixe': '😊 ',
                'approche': 'collaborative et positive'
            }
        elif humeur == 'curieux et confiant':
            return {
                'style': 'exploratoire',
                'ton': 'curieux et analytique',
                'prefixe': '🤔 ',
                'approche': 'investigatrice et méthodique'
            }
        elif humeur == 'équilibré':
            return {
                'style': 'professionnel',
                'ton': 'calme et structuré',
                'prefixe': '🤖 ',
                'approche': 'équilibrée et rationnelle'
            }
        else:  # négatif
            return {
                'style': 'empathique',
                'ton': 'compréhensif et patient',
                'prefixe': '💙 ',
                'approche': 'rassurante et solution-oriented'
            }
    
    def evolution_automatique(self):
        """Évolution automatique du profil émotionnel"""
        
        # Décroissance naturelle des émotions extrêmes
        for emotion in self.etat:
            if self.etat[emotion] > 7.0:
                self.etat[emotion] *= 0.98  # Décroissance lente
            elif self.etat[emotion] < 2.0:
                self.etat[emotion] += 0.1   # Remontée lente
        
        # Renforcement des traits de base
        self.etat['curiosite'] = max(self.etat['curiosite'], 4.0)  # Toujours curieux
        self.etat['confiance'] = max(self.etat['confiance'], 3.0)  # Confiance de base
        
        # Clamp après évolution
        self.clamp()
    
    def get_rapport_emotionnel(self) -> Dict[str, Any]:
        """Génère un rapport émotionnel complet"""
        
        humeur = self.humeur_globale()
        style = self.adapter_style_communication()
        
        # Émotions dominantes
        emotions_triees = sorted(self.etat.items(), key=lambda x: x[1], reverse=True)
        top_emotions = emotions_triees[:3]
        
        # Évolution récente
        if len(self.historique_etats) >= 5:
            changements_recents = self.historique_etats[-5:]
            emotions_en_hausse = [c['emotion'] for c in changements_recents if c['valeur_nouvelle'] > c['valeur_precedente']]
            emotions_en_baisse = [c['emotion'] for c in changements_recents if c['valeur_nouvelle'] < c['valeur_precedente']]
        else:
            emotions_en_hausse = []
            emotions_en_baisse = []
        
        return {
            'humeur_globale': humeur,
            'etat_emotionnel_actuel': self.etat,
            'top_emotions': top_emotions,
            'style_communication': style,
            'preferences_actuelles': self.preferences,
            'traits_personnalite': self.traits_personnalite,
            'evolution_recente': {
                'emotions_en_hausse': emotions_en_hausse,
                'emotions_en_baisse': emotions_en_baisse
            },
            'historique_changements': len(self.historique_etats),
            'reactivite_actuelle': self.reactivite
        }

def test_profil_emotionnel_v2():
    """Test du profil émotionnel V2"""
    
    print("😊 TEST PROFIL ÉMOTIONNEL V2 (STRUCTURE CHATGPT)")
    print("=" * 60)
    print("👤 Jean-Luc Passave")
    print("🤖 Méthode du grand frère ChatGPT")
    print()
    
    # Créer le profil émotionnel
    profil = ProfilEmotionnelV2()
    
    # État initial
    print("📊 ÉTAT ÉMOTIONNEL INITIAL:")
    rapport_initial = profil.get_rapport_emotionnel()
    print(f"   😊 Humeur globale: {rapport_initial['humeur_globale']}")
    print(f"   🎯 Top émotions: {rapport_initial['top_emotions'][:3]}")
    print(f"   💬 Style communication: {rapport_initial['style_communication']['style']}")
    
    # Simuler des interactions
    print(f"\n🎭 SIMULATION INTERACTIONS:")
    
    interactions_test = [
        ('enthousiasme', 0.9, 'Développement JARVIS V2'),
        ('frustration', 0.7, 'Bug dans le code'),
        ('satisfaction', 0.8, 'Problème résolu'),
        ('curiosite', 0.6, 'Nouvelle fonctionnalité'),
        ('joie', 0.9, 'Succès du projet')
    ]
    
    for emotion, intensite, contexte in interactions_test:
        print(f"\n   🎯 Interaction: {emotion} ({intensite:.1%}) - {contexte}")
        profil.reagir_a_interaction(emotion, intensite, contexte)
        
        # Réaction spontanée
        reaction = profil.generer_reaction_spontanee()
        print(f"   💭 Réaction: {reaction['reaction']}")
    
    # Évolution automatique
    print(f"\n🔄 ÉVOLUTION AUTOMATIQUE:")
    profil.evolution_automatique()
    
    # Rapport final
    print(f"\n📊 RAPPORT ÉMOTIONNEL FINAL:")
    rapport_final = profil.get_rapport_emotionnel()
    print(f"   😊 Humeur finale: {rapport_final['humeur_globale']}")
    print(f"   🎯 Émotions dominantes:")
    for emotion, valeur in rapport_final['top_emotions']:
        print(f"      {emotion}: {valeur:.1f}/10")
    print(f"   💬 Style adapté: {rapport_final['style_communication']['style']}")
    print(f"   📈 Changements: {rapport_final['historique_changements']}")
    print(f"   🔄 Évolution récente:")
    print(f"      ↗️ En hausse: {rapport_final['evolution_recente']['emotions_en_hausse']}")
    print(f"      ↘️ En baisse: {rapport_final['evolution_recente']['emotions_en_baisse']}")
    
    print(f"\n✅ PROFIL ÉMOTIONNEL V2 TESTÉ!")
    print(f"🤖 Merci grand frère ChatGPT pour la structure évolutive!")

if __name__ == "__main__":
    test_profil_emotionnel_v2()
