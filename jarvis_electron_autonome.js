#!/usr/bin/env node

const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');
const os = require('os');

let mainWindow;

function createWindow() {
    console.log('🚀 Création JARVIS Electron Autonome');
    
    mainWindow = new BrowserWindow({
        width: 1600,
        height: 1000,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false
        },
        title: 'JARVIS M4 - Interface Autonome',
        show: false
    });

    // Interface HTML complète autonome
    const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>JARVIS M4 Interface Autonome</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2d2d5a 100%);
                color: #ffffff;
                height: 100vh;
                overflow: hidden;
            }
            .header {
                background: rgba(0,0,0,0.3);
                padding: 15px 30px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }
            .logo { font-size: 24px; font-weight: bold; }
            .stats {
                display: flex;
                gap: 30px;
                font-size: 14px;
            }
            .stat { text-align: center; }
            .stat-value { font-size: 18px; font-weight: bold; color: #4CAF50; }
            
            .main-content {
                display: flex;
                height: calc(100vh - 80px);
            }
            .sidebar {
                width: 300px;
                background: rgba(0,0,0,0.2);
                padding: 20px;
                border-right: 1px solid rgba(255,255,255,0.1);
                overflow-y: auto;
            }
            .content-area {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }
            
            .nav-section {
                margin-bottom: 30px;
            }
            .nav-title {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 15px;
                color: #64B5F6;
                border-bottom: 1px solid rgba(100,181,246,0.3);
                padding-bottom: 5px;
            }
            .nav-item {
                display: block;
                padding: 12px 15px;
                margin: 5px 0;
                background: rgba(255,255,255,0.05);
                border: none;
                color: white;
                text-decoration: none;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s;
                width: 100%;
                text-align: left;
                font-size: 14px;
            }
            .nav-item:hover {
                background: rgba(255,255,255,0.15);
                transform: translateX(5px);
            }
            .nav-item.active {
                background: rgba(76,175,80,0.3);
                border-left: 4px solid #4CAF50;
            }
            
            .page {
                display: none;
                animation: fadeIn 0.3s ease-in;
            }
            .page.active {
                display: block;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            .welcome-card {
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 30px;
                margin-bottom: 30px;
                text-align: center;
            }
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .feature-card {
                background: rgba(255,255,255,0.08);
                border-radius: 12px;
                padding: 25px;
                border: 1px solid rgba(255,255,255,0.1);
                transition: all 0.3s;
            }
            .feature-card:hover {
                background: rgba(255,255,255,0.15);
                transform: translateY(-5px);
            }
            .feature-icon {
                font-size: 48px;
                margin-bottom: 15px;
                display: block;
            }
            .feature-title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #64B5F6;
            }
            
            .chat-container {
                background: rgba(0,0,0,0.3);
                border-radius: 12px;
                height: 500px;
                display: flex;
                flex-direction: column;
            }
            .chat-messages {
                flex: 1;
                padding: 20px;
                overflow-y: auto;
            }
            .chat-input-area {
                padding: 20px;
                border-top: 1px solid rgba(255,255,255,0.1);
                display: flex;
                gap: 10px;
            }
            .chat-input {
                flex: 1;
                padding: 12px;
                background: rgba(255,255,255,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                border-radius: 8px;
                color: white;
                font-size: 14px;
            }
            .chat-input::placeholder { color: rgba(255,255,255,0.6); }
            .send-btn {
                padding: 12px 20px;
                background: #4CAF50;
                border: none;
                border-radius: 8px;
                color: white;
                cursor: pointer;
                font-weight: bold;
            }
            .send-btn:hover { background: #45a049; }
            
            .message {
                margin: 10px 0;
                padding: 12px 15px;
                border-radius: 10px;
                max-width: 80%;
            }
            .message.user {
                background: rgba(33,150,243,0.3);
                margin-left: auto;
                text-align: right;
            }
            .message.jarvis {
                background: rgba(76,175,80,0.3);
            }
            
            .status-indicator {
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                margin-right: 8px;
            }
            .status-online { background: #4CAF50; }
            .status-offline { background: #f44336; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="logo">🤖 JARVIS M4 - Interface Autonome</div>
            <div class="stats">
                <div class="stat">
                    <div class="stat-value" id="neurons">89,067,389</div>
                    <div>Neurones</div>
                </div>
                <div class="stat">
                    <div class="stat-value" id="qi">648</div>
                    <div>QI Évolutif</div>
                </div>
                <div class="stat">
                    <div class="stat-value">
                        <span class="status-indicator status-online"></span>
                        Connecté
                    </div>
                    <div>Statut</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="nav-section">
                    <div class="nav-title">🏠 Navigation</div>
                    <button class="nav-item active" onclick="openDashboard()">🏠 Dashboard JARVIS</button>
                    <button class="nav-item" onclick="openCommunication()">💬 Communication</button>
                    <button class="nav-item" onclick="openCognitive()">🧠 Système Cognitif</button>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">🎬 Génération Vidéo</div>
                    <button class="nav-item" onclick="openLTXVideo()">🎬 LTX Vidéo</button>
                    <button class="nav-item" onclick="openStableVideo()">📹 Stable Video</button>
                    <button class="nav-item" onclick="openMultimedia()">🎨 Multimédia</button>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">🧠 Intelligence</div>
                    <button class="nav-item" onclick="openMemory()">🌡️ Mémoire Thermique</button>
                    <button class="nav-item" onclick="showPage('dreams')">🌙 Rêves & Créativité</button>
                    <button class="nav-item" onclick="showPage('analysis')">📊 Analyse</button>
                </div>
                
                <div class="nav-section">
                    <div class="nav-title">⚙️ Système</div>
                    <button class="nav-item" onclick="openAPI()">🚀 API JARVIS V2</button>
                    <button class="nav-item" onclick="showPage('settings')">⚙️ Configuration</button>
                </div>
            </div>
            
            <div class="content-area">
                <!-- Page Accueil -->
                <div id="home" class="page active">
                    <div class="welcome-card">
                        <h1>🤖 Bienvenue dans JARVIS M4</h1>
                        <p>Intelligence Artificielle avec Mémoire Thermique et Système Cognitif Avancé</p>
                        <p><strong>QI Évolutif:</strong> 164 | <strong>Neurones Actifs:</strong> 89.00B | <strong>Apple M4 Optimisé</strong></p>
                    </div>
                    
                    <div class="feature-grid">
                        <div class="feature-card">
                            <span class="feature-icon">🎬</span>
                            <div class="feature-title">Génération Vidéo</div>
                            <p>LTX Vidéo et Stable Video avec analyse cognitive automatique et indexation dans la mémoire thermique.</p>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">🧠</span>
                            <div class="feature-title">Système Cognitif</div>
                            <p>Dual Agents DeepSeek R1 8B avec analyse multimodale et connexions intelligentes.</p>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">🌡️</span>
                            <div class="feature-title">Mémoire Thermique</div>
                            <p>Stockage intelligent avec indexation automatique, recherche sémantique et associations multimodales.</p>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">🌙</span>
                            <div class="feature-title">Rêves & Créativité</div>
                            <p>Génération autonome de pensées créatives et rêves avec flux de conscience continu.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Page Communication -->
                <div id="chat" class="page">
                    <h2>💬 Communication avec JARVIS</h2>
                    <div class="chat-container">
                        <div class="chat-messages" id="chat-messages">
                            <div class="message jarvis">🤖 Bonjour Jean-Luc ! JARVIS M4 est prêt. Comment puis-je vous aider aujourd'hui ?</div>
                        </div>
                        <div class="chat-input-area">
                            <input type="text" class="chat-input" id="chat-input" placeholder="Tapez votre message..." onkeypress="handleEnter(event)">
                            <button class="send-btn" onclick="sendMessage()">Envoyer</button>
                        </div>
                    </div>
                </div>
                
                <!-- Autres pages... -->
                <div id="cognitive" class="page">
                    <h2>🧠 Système Cognitif</h2>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <span class="feature-icon">🤖</span>
                            <div class="feature-title">Agent 1 - Principal</div>
                            <p>Analyse générale et compréhension contextuelle</p>
                        </div>
                        <div class="feature-card">
                            <span class="feature-icon">⚡</span>
                            <div class="feature-title">Agent 2 - Turbo</div>
                            <p>Optimisation et accélération des processus</p>
                        </div>
                    </div>
                </div>
                
                <div id="ltx-video" class="page">
                    <h2>🎬 LTX Vidéo</h2>
                    <p>Génération vidéo avancée avec analyse cognitive automatique</p>
                </div>
                
                <div id="stable-video" class="page">
                    <h2>📹 Stable Video</h2>
                    <p>Génération vidéo stable avec dual agents cognitifs</p>
                </div>
                
                <div id="multimedia" class="page">
                    <h2>🎨 Générateur Multimédia</h2>
                    <p>Interface complète de génération multimédia</p>
                </div>
                
                <div id="memory" class="page">
                    <h2>🌡️ Mémoire Thermique</h2>
                    <p>Exploration et gestion de la mémoire thermique</p>
                </div>
                
                <div id="dreams" class="page">
                    <h2>🌙 Rêves & Créativité</h2>
                    <p>Flux de conscience et génération créative</p>
                </div>
                
                <div id="analysis" class="page">
                    <h2>📊 Analyse</h2>
                    <p>Analyse des performances et statistiques</p>
                </div>
                
                <div id="api" class="page">
                    <h2>🚀 API JARVIS V2</h2>
                    <p>Documentation et interface API</p>
                </div>
                
                <div id="settings" class="page">
                    <h2>⚙️ Configuration</h2>
                    <p>Paramètres et configuration du système</p>
                </div>
            </div>
        </div>
        
        <script>
            // Fonctions pour ouvrir ton programme DANS l'application Electron
            function openDashboard() {
                // Masquer le menu et afficher l'iframe avec ton programme
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7867" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openCommunication() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7866" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openLTXVideo() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7863" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openStableVideo() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7861" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openMultimedia() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7867/multimedia" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openCognitive() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7867/cognitive" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openMemory() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:7874" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function openAPI() {
                document.querySelector('.sidebar').style.display = 'none';
                document.querySelector('.main-content').innerHTML = `
                    <div style="position: relative; width: 100%; height: 100vh;">
                        <button onclick="showMenu()" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: #7b1fa2; color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer;">← Retour Menu</button>
                        <iframe src="http://localhost:8000/docs" style="width: 100%; height: 100%; border: none; margin-top: 50px;"></iframe>
                    </div>
                `;
            }

            function showMenu() {
                document.querySelector('.sidebar').style.display = 'block';
                location.reload(); // Recharger pour revenir au menu principal
            }

            function showPage(pageId) {
                // Masquer toutes les pages
                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });
                
                // Désactiver tous les nav-items
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                // Afficher la page sélectionnée
                document.getElementById(pageId).classList.add('active');
                
                // Activer le nav-item correspondant
                event.target.classList.add('active');
            }
            
            function sendMessage() {
                const input = document.getElementById('chat-input');
                const message = input.value.trim();
                if (!message) return;
                
                // Ajouter message utilisateur
                addMessage('user', message);
                input.value = '';
                
                // Simuler réponse JARVIS
                setTimeout(() => {
                    const responses = [
                        "🤖 Excellente question ! Je traite votre demande...",
                        "🧠 Analyse en cours avec le système cognitif...",
                        "🌡️ Consultation de la mémoire thermique...",
                        "⚡ Traitement avec les dual agents..."
                    ];
                    const response = responses[Math.floor(Math.random() * responses.length)];
                    addMessage('jarvis', response);
                }, 1000);
            }
            
            function addMessage(sender, text) {
                const messagesContainer = document.getElementById('chat-messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message ' + sender;
                messageDiv.textContent = text;
                messagesContainer.appendChild(messageDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
            
            function handleEnter(event) {
                if (event.key === 'Enter') {
                    sendMessage();
                }
            }
            
            // Mise à jour des stats
            setInterval(() => {
                const qi = document.getElementById('qi');
                const currentQI = parseInt(qi.textContent);
                qi.textContent = currentQI + Math.floor(Math.random() * 3 - 1); // Variation ±1
            }, 10000);
        </script>
    </body>
    </html>
    `;

    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent));
    
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        console.log('✅ Interface JARVIS Autonome affichée');
    });
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

console.log('🚀 JARVIS Electron Autonome - Démarrage');
