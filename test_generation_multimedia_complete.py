#!/usr/bin/env python3
"""
TEST COMPLET GÉNÉRATION MULTIMÉDIA - JEAN-LUC PASSAVE
Test de toutes les fonctionnalités de génération: Images, Vidéos, Musique
"""

import os
import time
from jarvis_generateur_multimedia_complet import get_generateur_multimedia

def test_generation_images():
    """Test de génération d'images"""
    
    print("🎨 TEST GÉNÉRATION D'IMAGES")
    print("=" * 50)
    
    generateur = get_generateur_multimedia()
    
    # Test 1: Image réaliste
    print("\n📸 TEST 1: Image réaliste")
    result1 = generateur.generer_image(
        prompt="Portrait d'un homme futuriste avec des lunettes cyberpunk",
        style="realistic",
        resolution="1024x1024",
        model="local"
    )
    
    if result1:
        print(f"  ✅ Image générée: {result1['filename']}")
        print(f"  📁 Chemin: {result1['filepath']}")
        print(f"  🎭 Style: {result1['style']}")
    else:
        print("  ❌ Échec génération image réaliste")
    
    # Test 2: Image artistique
    print("\n🎨 TEST 2: Image artistique")
    result2 = generateur.generer_image(
        prompt="Paysage de montagne au coucher du soleil, style impressionniste",
        style="artistic",
        resolution="1280x720",
        model="local"
    )
    
    if result2:
        print(f"  ✅ Image artistique générée: {result2['filename']}")
    else:
        print("  ❌ Échec génération image artistique")
    
    # Test 3: Image anime
    print("\n🌸 TEST 3: Image anime")
    result3 = generateur.generer_image(
        prompt="Personnage anime dans un jardin japonais avec des cerisiers",
        style="anime",
        resolution="768x768",
        model="local"
    )
    
    if result3:
        print(f"  ✅ Image anime générée: {result3['filename']}")
    else:
        print("  ❌ Échec génération image anime")
    
    return [result1, result2, result3]

def test_generation_videos():
    """Test de génération de vidéos"""
    
    print("\n🎬 TEST GÉNÉRATION DE VIDÉOS")
    print("=" * 50)
    
    generateur = get_generateur_multimedia()
    
    # Test 1: Vidéo courte
    print("\n🎞️ TEST 1: Vidéo courte")
    result1 = generateur.generer_video(
        prompt="Animation d'un coucher de soleil sur l'océan",
        duree=5,
        fps=24,
        resolution="1280x720",
        model="local"
    )
    
    if result1:
        print(f"  ✅ Vidéo démarrée: {result1['generation_id']}")
        print(f"  ⏱️ Temps estimé: {result1['estimated_time']}")
        
        # Attendre un peu et vérifier le statut
        time.sleep(3)
        status = generateur.get_status_generation(result1['generation_id'])
        print(f"  📊 Statut: {status.get('status', 'inconnu')}")
    else:
        print("  ❌ Échec démarrage vidéo courte")
    
    # Test 2: Vidéo moyenne
    print("\n🎬 TEST 2: Vidéo moyenne")
    result2 = generateur.generer_video(
        prompt="Animation de nuages qui bougent dans le ciel bleu",
        duree=10,
        fps=30,
        resolution="1920x1080",
        model="local"
    )
    
    if result2:
        print(f"  ✅ Vidéo moyenne démarrée: {result2['generation_id']}")
    else:
        print("  ❌ Échec démarrage vidéo moyenne")
    
    return [result1, result2]

def test_generation_musique():
    """Test de génération de musique"""
    
    print("\n🎵 TEST GÉNÉRATION DE MUSIQUE")
    print("=" * 50)
    
    generateur = get_generateur_multimedia()
    
    # Test 1: Musique électronique
    print("\n🎛️ TEST 1: Musique électronique")
    result1 = generateur.generer_musique(
        prompt="Musique électronique ambient relaxante",
        duree=30,
        style="electronic",
        model="local"
    )
    
    if result1:
        print(f"  ✅ Musique générée: {result1['filename']}")
        print(f"  📁 Chemin: {result1['filepath']}")
        print(f"  🎭 Style: {result1['style']}")
        print(f"  ⏱️ Durée: {result1['duree']}s")
    else:
        print("  ❌ Échec génération musique électronique")
    
    # Test 2: Musique classique
    print("\n🎼 TEST 2: Musique classique")
    result2 = generateur.generer_musique(
        prompt="Mélodie de piano classique douce et mélancolique",
        duree=45,
        style="classical",
        model="local"
    )
    
    if result2:
        print(f"  ✅ Musique classique générée: {result2['filename']}")
    else:
        print("  ❌ Échec génération musique classique")
    
    # Test 3: Musique ambient
    print("\n🌌 TEST 3: Musique ambient")
    result3 = generateur.generer_musique(
        prompt="Ambiance spatiale avec des sons synthétiques",
        duree=60,
        style="ambient",
        model="local"
    )
    
    if result3:
        print(f"  ✅ Musique ambient générée: {result3['filename']}")
    else:
        print("  ❌ Échec génération musique ambient")
    
    return [result1, result2, result3]

def test_historique_et_stats():
    """Test de l'historique et des statistiques"""
    
    print("\n📊 TEST HISTORIQUE ET STATISTIQUES")
    print("=" * 50)
    
    generateur = get_generateur_multimedia()
    
    # Obtenir l'historique
    historique = generateur.get_historique_generations(20)
    print(f"\n📋 Historique: {len(historique)} générations")
    
    for i, gen in enumerate(historique[-5:]):  # 5 dernières
        print(f"  {i+1}. {gen.get('type', 'inconnu')} - {gen.get('filename', 'N/A')}")
    
    # Obtenir les statistiques
    stats = generateur.get_stats_multimedia()
    print(f"\n📈 Statistiques:")
    print(f"  📊 Total générations: {stats['total_generations']}")
    print(f"  🔄 Générations actives: {stats['active_generations']}")
    print(f"  📁 Dossier sortie: {stats['output_directory']}")
    print(f"  💾 Utilisation disque: {stats['disk_usage']}")
    
    # Types générés
    if stats['types_generated']:
        print(f"\n🎯 Types générés:")
        for type_gen, count in stats['types_generated'].items():
            print(f"    {type_gen}: {count}")
    
    # Modèles utilisés
    if stats['models_used']:
        print(f"\n🤖 Modèles utilisés:")
        for model, count in stats['models_used'].items():
            print(f"    {model}: {count}")
    
    return stats

def test_gestion_fichiers():
    """Test de la gestion des fichiers générés"""
    
    print("\n📁 TEST GESTION FICHIERS")
    print("=" * 50)
    
    generateur = get_generateur_multimedia()
    
    # Vérifier le dossier de sortie
    output_dir = generateur.output_dir
    print(f"\n📁 Dossier de sortie: {output_dir}")
    
    if os.path.exists(output_dir):
        print("  ✅ Dossier existe")
        
        # Lister les fichiers
        fichiers = os.listdir(output_dir)
        print(f"  📄 Fichiers présents: {len(fichiers)}")
        
        for fichier in fichiers[:10]:  # 10 premiers
            filepath = os.path.join(output_dir, fichier)
            taille = os.path.getsize(filepath) / 1024  # KB
            print(f"    📄 {fichier} ({taille:.1f} KB)")
    else:
        print("  ❌ Dossier n'existe pas")
    
    return True

def main():
    """Test principal complet"""
    
    print("🎯 TEST COMPLET GÉNÉRATION MULTIMÉDIA JARVIS")
    print("=" * 80)
    print("👤 Jean-Luc Passave - Test de toutes les fonctionnalités")
    print("=" * 80)
    
    try:
        # Test 1: Génération d'images
        images_results = test_generation_images()
        images_success = sum(1 for r in images_results if r is not None)
        
        # Test 2: Génération de vidéos
        videos_results = test_generation_videos()
        videos_success = sum(1 for r in videos_results if r is not None)
        
        # Test 3: Génération de musique
        music_results = test_generation_musique()
        music_success = sum(1 for r in music_results if r is not None)
        
        # Test 4: Historique et stats
        stats = test_historique_et_stats()
        
        # Test 5: Gestion fichiers
        files_ok = test_gestion_fichiers()
        
        # Résumé final
        print("\n🎉 RÉSUMÉ FINAL")
        print("=" * 50)
        print(f"🎨 Images générées: {images_success}/3")
        print(f"🎬 Vidéos démarrées: {videos_success}/2")
        print(f"🎵 Musiques générées: {music_success}/3")
        print(f"📊 Statistiques: {'✅' if stats else '❌'}")
        print(f"📁 Gestion fichiers: {'✅' if files_ok else '❌'}")
        
        total_success = images_success + videos_success + music_success
        total_tests = 8
        
        print(f"\n📈 Score global: {total_success}/{total_tests} ({total_success/total_tests*100:.1f}%)")
        
        if total_success >= 6:
            print("\n🎉 EXCELLENT ! Générateur multimédia parfaitement fonctionnel")
            print("✅ Toutes les fonctionnalités de génération sont opérationnelles")
            print("🚀 JARVIS peut maintenant créer images, vidéos et musique !")
        elif total_success >= 4:
            print("\n👍 BON ! La plupart des fonctionnalités marchent")
            print("⚠️ Quelques améliorations possibles")
        else:
            print("\n⚠️ ATTENTION ! Plusieurs fonctionnalités ont des problèmes")
            print("🔧 Vérification et corrections nécessaires")
        
        return total_success >= 6
        
    except Exception as e:
        print(f"\n❌ ERREUR DURANT LES TESTS: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
