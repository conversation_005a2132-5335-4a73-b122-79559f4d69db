#!/bin/bash

# 💾 SAUVEGARDE JARVIS M4 AVEC CHOIX DU LIEU
# <PERSON><PERSON><PERSON> - 2025
# Sauvegarde avec choix du disque et dossier de destination

echo "💾 SAUVEGARDE JARVIS M4 AVEC CHOIX DU LIEU"
echo "========================================"
echo "👤 Jean-Luc Passave"
echo "📅 $(date)"
echo ""

# Fonction pour afficher les disques disponibles
show_available_disks() {
    echo "💿 DISQUES DISPONIBLES :"
    echo "======================"
    df -h | grep -E "^/dev/" | while read line; do
        disk=$(echo $line | awk '{print $1}')
        size=$(echo $line | awk '{print $2}')
        used=$(echo $line | awk '{print $3}')
        avail=$(echo $line | awk '{print $4}')
        mount=$(echo $line | awk '{print $6}')
        echo "📁 $mount ($disk) - Taille: $size, Libre: $avail"
    done
    echo ""
    
    echo "🔌 VOLUMES EXTERNES :"
    echo "==================="
    ls -la /Volumes/ 2>/dev/null | grep -v "^total" | grep -v "^\.$" | grep -v "^\..$" | while read line; do
        volume=$(echo $line | awk '{print $NF}')
        if [ "$volume" != "." ] && [ "$volume" != ".." ]; then
            echo "💾 /Volumes/$volume"
        fi
    done
    echo ""
}

# Fonction pour vérifier l'espace disponible
check_disk_space() {
    local target_dir="$1"
    local required_mb=200  # 200 MB minimum requis
    
    if [ -d "$target_dir" ]; then
        available_kb=$(df "$target_dir" | tail -1 | awk '{print $4}')
        available_mb=$((available_kb / 1024))
        
        if [ $available_mb -gt $required_mb ]; then
            echo "✅ Espace suffisant: ${available_mb} MB disponibles"
            return 0
        else
            echo "❌ Espace insuffisant: ${available_mb} MB disponibles (${required_mb} MB requis)"
            return 1
        fi
    else
        echo "❌ Répertoire non accessible: $target_dir"
        return 1
    fi
}

# Afficher les options disponibles
show_available_disks

# Demander le lieu de sauvegarde
echo "📁 CHOIX DU LIEU DE SAUVEGARDE :"
echo "==============================="
echo "1. 💾 Disque externe Seagate (/Volumes/seagate)"
echo "2. 🖥️ Répertoire local (~/Desktop/JARVIS_BACKUPS)"
echo "3. 📁 Documents (~/Documents/JARVIS_SAUVEGARDES)"
echo "4. 🔧 Personnalisé (vous choisissez)"
echo ""

read -p "Choisissez une option (1-4) : " choice

case $choice in
    1)
        BACKUP_BASE="/Volumes/seagate"
        BACKUP_FOLDER="SAUVEGARDES_JARVIS_M4"
        ;;
    2)
        BACKUP_BASE="$HOME/Desktop"
        BACKUP_FOLDER="JARVIS_BACKUPS"
        ;;
    3)
        BACKUP_BASE="$HOME/Documents"
        BACKUP_FOLDER="JARVIS_SAUVEGARDES"
        ;;
    4)
        echo ""
        read -p "📁 Entrez le chemin complet du répertoire : " custom_path
        BACKUP_BASE="$(dirname "$custom_path")"
        BACKUP_FOLDER="$(basename "$custom_path")"
        ;;
    *)
        echo "❌ Option invalide. Utilisation du répertoire par défaut."
        BACKUP_BASE="/Volumes/seagate/Louna_Electron_Latest"
        BACKUP_FOLDER="SAUVEGARDES_AUTO"
        ;;
esac

# Créer le chemin complet
FULL_BACKUP_PATH="$BACKUP_BASE/$BACKUP_FOLDER"

echo ""
echo "📍 LIEU DE SAUVEGARDE CHOISI :"
echo "============================="
echo "📁 Répertoire de base : $BACKUP_BASE"
echo "📂 Dossier de sauvegarde : $BACKUP_FOLDER"
echo "🎯 Chemin complet : $FULL_BACKUP_PATH"
echo ""

# Vérifier que le répertoire de base existe
if [ ! -d "$BACKUP_BASE" ]; then
    echo "❌ ERREUR: Le répertoire de base n'existe pas : $BACKUP_BASE"
    echo "💡 Vérifiez que le disque est monté ou que le chemin est correct"
    exit 1
fi

# Vérifier l'espace disponible
echo "🔍 VÉRIFICATION DE L'ESPACE DISQUE :"
echo "==================================="
if ! check_disk_space "$BACKUP_BASE"; then
    echo "❌ Espace disque insuffisant pour la sauvegarde"
    exit 1
fi

# Créer le répertoire de sauvegarde s'il n'existe pas
if [ ! -d "$FULL_BACKUP_PATH" ]; then
    echo "📁 Création du répertoire de sauvegarde..."
    mkdir -p "$FULL_BACKUP_PATH"
    if [ $? -eq 0 ]; then
        echo "✅ Répertoire créé : $FULL_BACKUP_PATH"
    else
        echo "❌ Erreur lors de la création du répertoire"
        exit 1
    fi
else
    echo "✅ Répertoire de sauvegarde existe déjà"
fi

# Créer le nom de sauvegarde avec timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="JARVIS_M4_BACKUP_${TIMESTAMP}"
BACKUP_DIR="$FULL_BACKUP_PATH/$BACKUP_NAME"

echo ""
echo "🚀 DÉBUT DE LA SAUVEGARDE :"
echo "=========================="
echo "📦 Nom de sauvegarde : $BACKUP_NAME"
echo "📁 Répertoire final : $BACKUP_DIR"
echo ""

# Créer le répertoire de cette sauvegarde
mkdir -p "$BACKUP_DIR"

# Liste des fichiers critiques à sauvegarder
CRITICAL_FILES=(
    "jarvis_electron_final_complet.js"
    "package.json"
    "dashboard_avec_onglets.py"
    "visualisation_memoire_thermique.py"
    "systeme_notifications_jarvis.py"
    "tableau_bord_final_ultime.py"
    "sauvegarde_automatique_jarvis.py"
    "monitoring_sante_jarvis_avance.py"
    "centre_commande_unifie_jarvis.py"
    "jarvis_architecture_multi_fenetres.py"
    "test_neurones_dynamiques.py"
    "test_agents_jarvis_complet.py"
    "validation_jarvis_m4_final_sans_simulation.py"
)

# Sauvegarder les fichiers critiques
echo "📋 SAUVEGARDE DES FICHIERS CRITIQUES :"
echo "====================================="
COPIED_COUNT=0
TOTAL_COUNT=${#CRITICAL_FILES[@]}

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$BACKUP_DIR/"
        echo "✅ $file"
        ((COPIED_COUNT++))
    else
        echo "⚠️ $file (non trouvé)"
    fi
done

# Sauvegarder la documentation
echo ""
echo "📚 SAUVEGARDE DE LA DOCUMENTATION :"
echo "=================================="
DOC_COUNT=0
for file in *.md; do
    if [ -f "$file" ]; then
        cp "$file" "$BACKUP_DIR/"
        echo "✅ $file"
        ((DOC_COUNT++))
    fi
done

# Créer un fichier d'inventaire détaillé
INVENTORY_FILE="$BACKUP_DIR/INVENTAIRE_COMPLET.md"
cat > "$INVENTORY_FILE" << EOF
# 💾 INVENTAIRE SAUVEGARDE JARVIS M4
## Jean-Luc Passave - $(date)

### 📍 INFORMATIONS SAUVEGARDE
**Date :** $(date)
**Lieu choisi :** $FULL_BACKUP_PATH
**Nom sauvegarde :** $BACKUP_NAME
**Chemin complet :** $BACKUP_DIR

### 📊 STATISTIQUES
**Fichiers critiques :** $COPIED_COUNT/$TOTAL_COUNT
**Documentation :** $DOC_COUNT fichiers
**Taille totale :** $(du -sh "$BACKUP_DIR" | cut -f1)

### 🎯 CHOIX DE JEAN-LUC PASSAVE
**Option choisie :** $choice
**Répertoire de base :** $BACKUP_BASE
**Dossier de sauvegarde :** $BACKUP_FOLDER

### 🔄 RESTAURATION
Pour restaurer cette sauvegarde :
1. Copier tous les fichiers vers le répertoire de travail
2. Relancer : npm run final
3. Vérifier tous les services

### ✅ FICHIERS SAUVEGARDÉS
EOF

# Ajouter la liste des fichiers à l'inventaire
echo "#### 🔥 Fichiers Critiques :" >> "$INVENTORY_FILE"
for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$BACKUP_DIR/$file" ]; then
        echo "- ✅ $file" >> "$INVENTORY_FILE"
    else
        echo "- ❌ $file (manquant)" >> "$INVENTORY_FILE"
    fi
done

echo "" >> "$INVENTORY_FILE"
echo "#### 📚 Documentation :" >> "$INVENTORY_FILE"
for file in "$BACKUP_DIR"/*.md; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        echo "- ✅ $filename" >> "$INVENTORY_FILE"
    fi
done

# Créer une archive compressée
echo ""
echo "📦 CRÉATION DE L'ARCHIVE COMPRESSÉE :"
echo "===================================="
ARCHIVE_PATH="$BACKUP_DIR.tar.gz"
cd "$FULL_BACKUP_PATH"
tar -czf "$ARCHIVE_PATH" "$BACKUP_NAME"

if [ $? -eq 0 ]; then
    echo "✅ Archive créée : $ARCHIVE_PATH"
    ARCHIVE_SIZE=$(du -sh "$ARCHIVE_PATH" | cut -f1)
    echo "📦 Taille archive : $ARCHIVE_SIZE"
else
    echo "❌ Erreur lors de la création de l'archive"
fi

# Résumé final
echo ""
echo "🎉 SAUVEGARDE TERMINÉE AVEC SUCCÈS !"
echo "==================================="
echo "📁 Lieu : $FULL_BACKUP_PATH"
echo "📦 Dossier : $BACKUP_NAME"
echo "📋 Archive : $ARCHIVE_PATH"
echo "📊 Fichiers critiques : $COPIED_COUNT/$TOTAL_COUNT"
echo "📚 Documentation : $DOC_COUNT fichiers"
echo "💾 Espace utilisé : $(du -sh "$BACKUP_DIR" | cut -f1)"
echo ""
echo "✅ JEAN-LUC PASSAVE : VOTRE TRAVAIL EST SAUVEGARDÉ !"
echo "🛡️ Aucun risque de perte - Sauvegarde complète réussie"
echo ""
echo "🔄 Pour restaurer : Voir le fichier INVENTAIRE_COMPLET.md"
