#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Centre de Commande Unifié JARVIS
Jean-<PERSON> - 2025
Centre de contrôle principal pour tout l'écosystème JARVIS
"""

import gradio as gr
import subprocess
import webbrowser
import requests
import psutil
import os
from datetime import datetime
import json

def get_ecosystem_status():
    """Récupère le statut complet de l'écosystème"""
    
    services = {
        'Dashboard Onglets': {'port': 7899, 'status': 'unknown', 'priority': 'high'},
        'Communication': {'port': 7866, 'status': 'unknown', 'priority': 'high'},
        'Electron App': {'port': None, 'status': 'unknown', 'priority': 'critical'},
        'Visualisation Mémoire': {'port': 7900, 'status': 'unknown', 'priority': 'medium'},
        'Notifications': {'port': 7901, 'status': 'unknown', 'priority': 'medium'},
        'Tableau Bord Ultime': {'port': 7902, 'status': 'unknown', 'priority': 'high'},
        'Sauvegarde Auto': {'port': 7903, 'status': 'unknown', 'priority': 'critical'},
        'Monitoring Santé': {'port': 7904, 'status': 'unknown', 'priority': 'medium'},
        'Test Neurones': {'port': 7898, 'status': 'unknown', 'priority': 'low'},
        'Centre Contrôle': {'port': 7897, 'status': 'unknown', 'priority': 'medium'}
    }
    
    # Vérifier chaque service
    for service_name, service_info in services.items():
        if service_info['port']:
            try:
                response = requests.get(f"http://localhost:{service_info['port']}", timeout=2)
                services[service_name]['status'] = 'active' if response.status_code == 200 else 'error'
            except:
                services[service_name]['status'] = 'inactive'
        else:
            # Vérifier Electron
            electron_running = any('electron' in p.name().lower() for p in psutil.process_iter(['name']))
            services[service_name]['status'] = 'active' if electron_running else 'inactive'
    
    return services

def create_command_center_dashboard():
    """Crée le tableau de bord du centre de commande"""
    
    services = get_ecosystem_status()
    
    # Statistiques globales
    total_services = len(services)
    active_services = sum(1 for s in services.values() if s['status'] == 'active')
    critical_services = sum(1 for s in services.values() if s['priority'] == 'critical' and s['status'] == 'active')
    
    # Score global
    global_score = (active_services / total_services) * 100
    score_color = '#4CAF50' if global_score >= 80 else '#FF9800' if global_score >= 60 else '#F44336'
    
    dashboard_html = f"""
    <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); color: white; padding: 30px; border-radius: 15px; margin: 10px 0;'>
        <h2 style='margin: 0 0 25px 0; text-align: center; font-size: 2.2em;'>🎯 CENTRE DE COMMANDE JARVIS</h2>
        
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;'>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>📊 Score Global</h3>
                <p style='margin: 0; font-size: 2.5em; font-weight: bold; color: {score_color};'>{global_score:.0f}%</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Écosystème</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>🌐 Services</h3>
                <p style='margin: 0; font-size: 2.5em; font-weight: bold;'>{active_services}/{total_services}</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Actifs</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>🚨 Critiques</h3>
                <p style='margin: 0; font-size: 2.5em; font-weight: bold; color: {"#4CAF50" if critical_services >= 2 else "#F44336"};'>{critical_services}/2</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Opérationnels</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>⏰ Statut</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold;'>{datetime.now().strftime("%H:%M")}</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Temps réel</p>
            </div>
        </div>
    </div>
    """
    
    # Services détaillés
    priority_order = {'critical': 1, 'high': 2, 'medium': 3, 'low': 4}
    sorted_services = sorted(services.items(), key=lambda x: priority_order.get(x[1]['priority'], 5))
    
    services_html = """
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>🎛️ CONTRÔLE SERVICES</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 15px;'>
    """
    
    status_colors = {
        'active': '#4CAF50',
        'inactive': '#F44336',
        'error': '#FF5722',
        'unknown': '#9E9E9E'
    }
    
    priority_colors = {
        'critical': '#F44336',
        'high': '#FF9800',
        'medium': '#2196F3',
        'low': '#4CAF50'
    }
    
    status_icons = {
        'active': '🟢',
        'inactive': '🔴',
        'error': '❌',
        'unknown': '❓'
    }
    
    for service_name, service_info in sorted_services:
        status = service_info['status']
        priority = service_info['priority']
        port = service_info['port']
        
        status_color = status_colors.get(status, '#9E9E9E')
        priority_color = priority_colors.get(priority, '#9E9E9E')
        icon = status_icons.get(status, '❓')
        
        port_text = f"Port {port}" if port else "Processus"
        
        services_html += f"""
        <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid {status_color};'>
            <div style='display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;'>
                <h4 style='margin: 0; color: #333;'>{icon} {service_name}</h4>
                <div style='display: flex; gap: 8px; align-items: center;'>
                    <span style='background: {priority_color}; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;'>
                        {priority.upper()}
                    </span>
                    <span style='color: {status_color}; font-weight: bold; font-size: 0.9em;'>{status.upper()}</span>
                </div>
            </div>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <span style='color: #666; font-size: 0.8em;'>{port_text}</span>
                <div style='display: flex; gap: 5px;'>
                    <button style='background: #2196F3; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 0.7em; cursor: pointer;'>
                        {"Ouvrir" if status == "active" else "Démarrer"}
                    </button>
                    <button style='background: #FF9800; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 0.7em; cursor: pointer;'>
                        Redémarrer
                    </button>
                </div>
            </div>
        </div>
        """
    
    services_html += "</div></div>"
    
    return dashboard_html + services_html

def launch_service(service_name):
    """Lance un service spécifique"""
    
    commands = {
        'Dashboard Onglets': ['python3', 'dashboard_avec_onglets.py'],
        'Visualisation Mémoire': ['python3', 'visualisation_memoire_thermique.py'],
        'Notifications': ['python3', 'systeme_notifications_jarvis.py'],
        'Tableau Bord Ultime': ['python3', 'tableau_bord_final_ultime.py'],
        'Sauvegarde Auto': ['python3', 'sauvegarde_automatique_jarvis.py'],
        'Monitoring Santé': ['python3', 'monitoring_sante_jarvis_avance.py'],
        'Electron App': ['npm', 'run', 'final']
    }
    
    if service_name in commands:
        try:
            process = subprocess.Popen(commands[service_name], cwd=os.getcwd())
            return f"✅ {service_name} lancé (PID: {process.pid})"
        except Exception as e:
            return f"❌ Erreur lancement {service_name}: {str(e)}"
    else:
        return f"❌ Service {service_name} non trouvé"

def open_service(service_name):
    """Ouvre un service dans le navigateur"""
    
    ports = {
        'Dashboard Onglets': 7899,
        'Communication': 7866,
        'Visualisation Mémoire': 7900,
        'Notifications': 7901,
        'Tableau Bord Ultime': 7902,
        'Sauvegarde Auto': 7903,
        'Monitoring Santé': 7904,
        'Test Neurones': 7898,
        'Centre Contrôle': 7897
    }
    
    if service_name in ports:
        url = f"http://localhost:{ports[service_name]}"
        webbrowser.open(url)
        return f"✅ {service_name} ouvert: {url}"
    else:
        return f"❌ Service {service_name} non trouvé"

def create_command_center():
    """Centre de commande unifié"""
    
    with gr.Blocks(
        title="🎯 Centre de Commande Unifié JARVIS",
        theme=gr.themes.Soft()
    ) as command_center:

        # CSS pour boutons colorés - JEAN-LUC PASSAVE
        gr.HTML("""
        <style>
            .command-primary {
                background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1) !important;
                color: white !important;
                border: none !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 1.1em !important;
                padding: 12px 20px !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 5px 20px rgba(255, 107, 107, 0.4) !important;
            }
            .command-primary:hover {
                background: linear-gradient(45deg, #4ECDC4, #45B7D1, #FF6B6B) !important;
                transform: translateY(-3px) !important;
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.5) !important;
            }
            .command-secondary {
                background: linear-gradient(45deg, #667eea, #764ba2, #f093fb) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
            }
            .command-secondary:hover {
                background: linear-gradient(45deg, #764ba2, #f093fb, #667eea) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
            }
            .command-success {
                background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3) !important;
            }
            .command-success:hover {
                background: linear-gradient(45deg, #8BC34A, #CDDC39, #4CAF50) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
            }
            .command-warning {
                background: linear-gradient(45deg, #FF9800, #FFC107, #FFEB3B) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
            }
            .command-warning:hover {
                background: linear-gradient(45deg, #FFC107, #FFEB3B, #FF9800) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
            }
        </style>

        <div style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); color: white; padding: 30px; margin: -20px -20px 25px -20px; position: relative;">
            <h1 style="margin: 0; font-size: 2.5em; text-shadow: 0 4px 8px rgba(0,0,0,0.3);">🎯 CENTRE DE COMMANDE UNIFIÉ</h1>
            <h2 style="margin: 15px 0; font-size: 1.5em; opacity: 0.95;">Contrôle Total de l'Écosystème JARVIS</h2>
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 15px; margin: 20px auto; max-width: 600px; backdrop-filter: blur(10px);">
                <p style="margin: 0; font-size: 1.2em;">👤 Jean-Luc Passave - Commandant en Chef</p>
            </div>
        </div>
        """)

        with gr.Tabs():
            
            # Onglet Vue d'ensemble
            with gr.Tab("📊 Vue d'ensemble"):
                command_dashboard = gr.HTML(
                    value=create_command_center_dashboard(),
                    label="Tableau de bord commande"
                )
                
                refresh_dashboard_btn = gr.Button(
                    "🔄 ACTUALISER TABLEAU DE BORD",
                    variant="primary",
                    size="lg"
                )
            
            # Onglet Lancement Rapide
            with gr.Tab("🚀 Lancement Rapide"):
                gr.HTML("<h2 style='text-align: center; color: #333;'>🚀 LANCEMENT RAPIDE SERVICES</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🔥 Services Critiques</h3>")
                        
                        launch_electron_btn = gr.Button("🖥️ Lancer Electron App", variant="primary", elem_classes=["command-primary"])
                        launch_backup_btn = gr.Button("💾 Lancer Sauvegarde Auto", variant="primary", elem_classes=["command-primary"])

                        gr.HTML("<h3>📋 Interfaces Principales</h3>")

                        launch_dashboard_btn = gr.Button("📋 Dashboard Onglets", variant="secondary", elem_classes=["command-secondary"])
                        launch_ultimate_btn = gr.Button("🌟 Tableau Bord Ultime", variant="secondary", elem_classes=["command-secondary"])
                        
                    with gr.Column():
                        gr.HTML("<h3>🧠 Intelligence</h3>")
                        
                        launch_memory_btn = gr.Button("🧠 Visualisation Mémoire", variant="secondary")
                        launch_notifications_btn = gr.Button("🔔 Notifications", variant="secondary")
                        
                        gr.HTML("<h3>📊 Monitoring</h3>")
                        
                        launch_health_btn = gr.Button("🏥 Monitoring Santé", variant="secondary")
                        launch_control_btn = gr.Button("🎯 Centre Contrôle", variant="secondary")

                launch_result = gr.Textbox(
                    label="Résultats Lancement",
                    lines=3,
                    interactive=False
                )
            
            # Onglet Accès Direct
            with gr.Tab("🌐 Accès Direct"):
                gr.HTML("<h2 style='text-align: center; color: #333;'>🌐 ACCÈS DIRECT INTERFACES</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🏠 Navigation Principale</h3>")
                        
                        open_dashboard_btn = gr.Button("📋 Dashboard Onglets", size="sm")
                        open_ultimate_btn = gr.Button("🌟 Tableau Bord Ultime", size="sm")
                        open_communication_btn = gr.Button("💬 Communication", size="sm")
                        
                    with gr.Column():
                        gr.HTML("<h3>🧠 Intelligence</h3>")
                        
                        open_memory_btn = gr.Button("🧠 Visualisation Mémoire", size="sm")
                        open_notifications_btn = gr.Button("🔔 Notifications", size="sm")
                        open_test_neurons_btn = gr.Button("🔬 Test Neurones", size="sm")
                        
                    with gr.Column():
                        gr.HTML("<h3>📊 Monitoring</h3>")

                        open_health_btn = gr.Button("🏥 Monitoring Santé", size="sm")
                        open_backup_btn = gr.Button("💾 Sauvegarde Auto", size="sm")
                        open_control_btn = gr.Button("🎯 Centre Contrôle", size="sm")

                access_result = gr.Textbox(
                    label="Résultats Accès",
                    lines=3,
                    interactive=False
                )

        # Connexions des boutons
        
        # Actualisation
        refresh_dashboard_btn.click(fn=create_command_center_dashboard, outputs=[command_dashboard])
        
        # Lancement services
        launch_electron_btn.click(fn=lambda: launch_service('Electron App'), outputs=[launch_result])
        launch_backup_btn.click(fn=lambda: launch_service('Sauvegarde Auto'), outputs=[launch_result])
        launch_dashboard_btn.click(fn=lambda: launch_service('Dashboard Onglets'), outputs=[launch_result])
        launch_ultimate_btn.click(fn=lambda: launch_service('Tableau Bord Ultime'), outputs=[launch_result])
        launch_memory_btn.click(fn=lambda: launch_service('Visualisation Mémoire'), outputs=[launch_result])
        launch_notifications_btn.click(fn=lambda: launch_service('Notifications'), outputs=[launch_result])
        launch_health_btn.click(fn=lambda: launch_service('Monitoring Santé'), outputs=[launch_result])
        
        # Accès direct
        open_dashboard_btn.click(fn=lambda: open_service('Dashboard Onglets'), outputs=[access_result])
        open_ultimate_btn.click(fn=lambda: open_service('Tableau Bord Ultime'), outputs=[access_result])
        open_communication_btn.click(fn=lambda: open_service('Communication'), outputs=[access_result])
        open_memory_btn.click(fn=lambda: open_service('Visualisation Mémoire'), outputs=[access_result])
        open_notifications_btn.click(fn=lambda: open_service('Notifications'), outputs=[access_result])
        open_test_neurons_btn.click(fn=lambda: open_service('Test Neurones'), outputs=[access_result])
        open_health_btn.click(fn=lambda: open_service('Monitoring Santé'), outputs=[access_result])
        open_backup_btn.click(fn=lambda: open_service('Sauvegarde Auto'), outputs=[access_result])

        # BOUTON RETOUR À LA PAGE PRINCIPALE
        gr.HTML("<hr style='margin: 30px 0;'>")
        with gr.Row():
            home_main_btn = gr.Button("🏠 Retour Page Principale", variant="primary", size="lg", elem_classes=["command-primary"])

        def go_to_main_dashboard():
            """Redirige vers la VRAIE page principale"""
            webbrowser.open("http://localhost:7867")
            return "🏠 Redirection vers Page Principale (7867)..."

        home_main_btn.click(fn=go_to_main_dashboard, outputs=[access_result])

        # Footer
        gr.HTML(f"""
        <div style='background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39); color: white; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>
            <h2 style='margin: 0 0 15px 0; font-size: 2em;'>🎯 JEAN-LUC PASSAVE</h2>
            <h3 style='margin: 0 0 10px 0; font-size: 1.5em;'>COMMANDANT EN CHEF DE L'ÉCOSYSTÈME JARVIS !</h3>
            <p style='margin: 10px 0; font-size: 1.2em;'>🌟 Contrôle Total | 🚀 Lancement Rapide | 🌐 Accès Unifié</p>
            <p style='margin: 10px 0; font-size: 1em; opacity: 0.9;'>Centre de commande opérationnel - {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}</p>
        </div>
        """)

    return command_center

if __name__ == "__main__":
    print("🎯 DÉMARRAGE CENTRE DE COMMANDE UNIFIÉ")
    print("=====================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Contrôle total écosystème JARVIS")
    print("")
    
    # Créer et lancer le centre de commande
    command_app = create_command_center()
    
    print("✅ Centre de commande unifié créé")
    print("🌐 Lancement sur http://localhost:7905")
    print("🎯 Contrôle total disponible")
    
    command_app.launch(
        server_name="127.0.0.1",
        server_port=7905,
        share=False,
        show_error=True,
        quiet=False
    )
