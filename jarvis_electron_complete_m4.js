const { app, BrowserWindow, shell, Menu, dialog, ipcMain, systemPreferences } = require('electron');
const path = require('path');
const fs = require('fs');
const http = require('http');
const { spawn, exec } = require('child_process');
const os = require('os');

// 🚀 JARVIS ELECTRON COMPLET M4 - JEAN-LUC PASSAVE
// Version complète avec mémoire thermique, optimisations Apple Silicon M4,
// génération multimédia, formation JARVIS et analyse évolutive
// Créé avec excellence par Claude pour Jean-Luc Passave

console.log('🍎 JARVIS ELECTRON M4 - Démarrage...');
console.log(`📱 Architecture: ${process.arch}`);
console.log(`💻 Plateforme: ${process.platform}`);
console.log(`🧠 CPU: ${os.cpus().length} cœurs`);
console.log(`💾 RAM: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)} GB`);

let mainWindow;
let interfaceWindows = {};
let pythonProcesses = {};
let isAppleSilicon = process.arch === 'arm64';
let connectionAttempts = 0;
let maxConnectionAttempts = 5;
let isConnecting = false;
let connectionMonitorInterval = null;

// Configuration des interfaces JARVIS COMPLÈTES
const JARVIS_INTERFACES = {
    dashboard: {
        port: 7867,
        title: "🏠 JARVIS Dashboard Principal",
        width: 1400,
        height: 900,
        script: "jarvis_nouvelles_fenetres_simple.py",
        priority: "high"
    },
    thermal_memory: {
        port: 7874,
        title: "🧠 Mémoire Thermique M4",
        width: 1200,
        height: 800,
        script: "memoire_thermique_turbo_adaptatif.py",
        priority: "high"
    },
    multimedia: {
        port: 7881,
        title: "🎨 Générateur Multimédia Complet",
        width: 1300,
        height: 900,
        script: "jarvis_interface_multimedia_complete.py",
        priority: "medium"
    },
    formation: {
        port: 7876,
        title: "🎓 Formation JARVIS Complète",
        width: 1100,
        height: 800,
        script: "jarvis_formation_complete.py",
        priority: "medium"
    },
    analysis: {
        port: 7877,
        title: "🧬 Analyse Évolutive",
        width: 1200,
        height: 800,
        script: "jarvis_analyse_evolutive_complete.py",
        priority: "medium"
    },
    communication: {
        port: 7866,
        title: "💬 Communication Principale",
        width: 1200,
        height: 800,
        priority: "high"
    },
    multiagents: {
        port: 7880,
        title: "🤖 Multi-Agents Complet",
        width: 1100,
        height: 700,
        priority: "medium"
    },
    code: {
        port: 7868,
        title: "💻 Éditeur Code",
        width: 1300,
        height: 800,
        priority: "medium"
    },
    security: {
        port: 7872,
        title: "🔐 Sécurité",
        width: 1000,
        height: 700,
        priority: "low"
    },
    system: {
        port: 7873,
        title: "📊 Système M4",
        width: 1100,
        height: 700,
        priority: "medium"
    },
    websearch: {
        port: 7878,
        title: "🌐 Recherche Web",
        width: 1200,
        height: 800,
        priority: "low"
    },
    voice: {
        port: 7879,
        title: "🎤 Interface Vocale",
        width: 900,
        height: 600,
        priority: "low"
    }
};

// Détection et optimisations Apple Silicon M4
function detectAppleSiliconCapabilities() {
    if (!isAppleSilicon) {
        console.log('ℹ️ Architecture non-Apple Silicon détectée');
        return {
            isAppleSilicon: false,
            performanceCores: Math.floor(os.cpus().length / 2),
            efficiencyCores: Math.floor(os.cpus().length / 2),
            neuralEngine: false,
            unifiedMemory: false
        };
    }

    console.log('🍎 Apple Silicon détecté - Activation optimisations M4');

    const cpuCount = os.cpus().length;
    let performanceCores = 6;  // M4 standard
    let efficiencyCores = 4;   // M4 standard

    // Détection plus précise selon le nombre de cœurs
    if (cpuCount === 10) {
        performanceCores = 6;
        efficiencyCores = 4;
        console.log('🔥 M4 Standard détecté (6P+4E)');
    } else if (cpuCount === 14) {
        performanceCores = 10;
        efficiencyCores = 4;
        console.log('🚀 M4 Pro détecté (10P+4E)');
    } else if (cpuCount >= 16) {
        performanceCores = 12;
        efficiencyCores = 4;
        console.log('⚡ M4 Max détecté (12P+4E)');
    }

    return {
        isAppleSilicon: true,
        performanceCores,
        efficiencyCores,
        neuralEngine: true,
        unifiedMemory: true,
        totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024),
        architecture: 'arm64'
    };
}

const m4Capabilities = detectAppleSiliconCapabilities();

function createMainWindow() {
    console.log('🖥️ CRÉATION FENÊTRE PRINCIPALE M4...');

    // Optimisations fenêtre pour Apple Silicon
    const windowOptions = {
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false,
            enableRemoteModule: true,
            // Optimisations Apple Silicon
            experimentalFeatures: isAppleSilicon,
            v8CacheOptions: isAppleSilicon ? 'bypassHeatCheck' : 'none'
        },
        title: '🤖 JARVIS M4 COMPLET - Centre de Contrôle',
        show: true,
        center: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        resizable: true,
        titleBarStyle: isAppleSilicon ? 'hiddenInset' : 'default',
        vibrancy: isAppleSilicon ? 'ultra-dark' : undefined
    };

    // Optimisations supplémentaires pour M4
    if (isAppleSilicon) {
        windowOptions.webPreferences.hardwareAcceleration = true;
        windowOptions.webPreferences.enableGPUAcceleration = true;
        console.log('🍎 Optimisations M4 activées pour la fenêtre principale');
    }

    mainWindow = new BrowserWindow(windowOptions);

    // Afficher directement la page de connexion au démarrage
    console.log('🎨 Affichage page de connexion JARVIS...');
    showJarvisConnectionPage();

    // Gérer la fermeture
    mainWindow.on('closed', () => {
        console.log('🔄 Fermeture application - Nettoyage...');
        mainWindow = null;

        // Fermer toutes les fenêtres d'interface
        Object.values(interfaceWindows).forEach(window => {
            if (window && !window.isDestroyed()) {
                window.close();
            }
        });
        interfaceWindows = {};

        // Arrêter tous les processus Python
        Object.values(pythonProcesses).forEach(process => {
            if (process && !process.killed) {
                process.kill('SIGTERM');
            }
        });
        pythonProcesses = {};

        // Arrêter le monitoring
        if (connectionMonitorInterval) {
            clearInterval(connectionMonitorInterval);
        }
    });

    // Créer le menu principal M4
    createMainMenuM4();

    // Démarrer les services Python essentiels
    startEssentialPythonServices();

    console.log('✅ Fenêtre principale M4 créée');
}

function startEssentialPythonServices() {
    console.log('🐍 Démarrage automatique du programme JARVIS...');

    // Vérifier si JARVIS fonctionne déjà
    const req = http.get('http://127.0.0.1:7867', (res) => {
        console.log('✅ JARVIS déjà en cours d\'exécution');
    });

    req.on('error', () => {
        console.log('🚀 Démarrage automatique de JARVIS...');

        // Démarrer JARVIS automatiquement
        const pythonPath = path.join(__dirname, 'venv_deepseek', 'bin', 'python');
        const jarvisScript = 'jarvis_architecture_multi_fenetres.py';

        if (fs.existsSync(jarvisScript)) {
            const jarvisProcess = spawn(pythonPath, [jarvisScript], {
                cwd: __dirname,
                stdio: ['pipe', 'pipe', 'pipe'],
                env: {
                    ...process.env,
                    PYTHONOPTIMIZE: '2',
                    APPLE_SILICON_OPTIMIZED: isAppleSilicon ? '1' : '0'
                }
            });

            pythonProcesses['jarvis_main'] = jarvisProcess;

            jarvisProcess.stdout.on('data', (data) => {
                console.log(`🤖 JARVIS: ${data.toString().trim()}`);
            });

            jarvisProcess.stderr.on('data', (data) => {
                console.error(`❌ JARVIS ERROR: ${data.toString().trim()}`);
            });

            jarvisProcess.on('close', (code) => {
                console.log(`🔄 JARVIS fermé avec code ${code}`);
                delete pythonProcesses['jarvis_main'];
            });

            console.log(`✅ JARVIS démarré automatiquement (PID: ${jarvisProcess.pid})`);
        } else {
            console.log('⚠️ Script JARVIS non trouvé');
        }
    });

    req.setTimeout(2000, () => {
        req.destroy();
    });
}

function startConnectionMonitoring() {
    console.log('📡 Démarrage monitoring de connexion...');

    // Monitoring moins agressif - toutes les 30 secondes
    connectionMonitorInterval = setInterval(() => {
        // Ne pas monitorer si on est déjà en train de se connecter
        if (isConnecting) {
            return;
        }

        const req = http.get('http://127.0.0.1:7867', (res) => {
            // Connexion OK - ne rien faire
        });

        req.on('error', () => {
            // Seulement si la fenêtre affiche actuellement JARVIS
            if (mainWindow && !mainWindow.isDestroyed()) {
                const currentURL = mainWindow.webContents.getURL();
                if (currentURL.includes('127.0.0.1:7867') || currentURL.includes('localhost:7867')) {
                    console.log('⚠️ Perte de connexion JARVIS détectée - Affichage page de connexion');
                    showJarvisConnectionPage();
                }
            }
        });

        req.setTimeout(2000, () => {
            req.destroy();
        });
    }, 30000); // 30 secondes au lieu de 10
}

function startPythonService(serviceName, config) {
    if (pythonProcesses[serviceName]) {
        console.log(`⚠️ Service ${serviceName} déjà démarré`);
        return;
    }

    console.log(`🚀 Démarrage service ${serviceName}...`);

    // Commande Python optimisée pour M4 avec environnement virtuel
    const pythonCmd = path.join(__dirname, 'venv_deepseek', 'bin', 'python');
    const scriptPath = config.script;

    // Variables d'environnement optimisées pour Apple Silicon
    const env = { ...process.env };
    if (isAppleSilicon) {
        env.PYTHONOPTIMIZE = '2';
        env.PYTORCH_ENABLE_MPS_FALLBACK = '1';
        env.APPLE_SILICON_OPTIMIZED = '1';
        env.M4_PERFORMANCE_CORES = m4Capabilities.performanceCores.toString();
        env.M4_EFFICIENCY_CORES = m4Capabilities.efficiencyCores.toString();
        env.M4_NEURAL_ENGINE = m4Capabilities.neuralEngine.toString();
    }

    const pythonProcess = spawn(pythonCmd, [scriptPath], {
        cwd: __dirname,
        env: env,
        stdio: ['pipe', 'pipe', 'pipe']
    });

    pythonProcesses[serviceName] = pythonProcess;

    pythonProcess.stdout.on('data', (data) => {
        console.log(`📊 ${serviceName}: ${data.toString().trim()}`);
    });

    pythonProcess.stderr.on('data', (data) => {
        console.error(`❌ ${serviceName} ERROR: ${data.toString().trim()}`);
    });

    pythonProcess.on('close', (code) => {
        console.log(`🔄 Service ${serviceName} fermé avec code ${code}`);
        delete pythonProcesses[serviceName];
    });

    pythonProcess.on('error', (error) => {
        console.error(`❌ Erreur démarrage ${serviceName}:`, error);
        delete pythonProcesses[serviceName];
    });

    console.log(`✅ Service ${serviceName} démarré (PID: ${pythonProcess.pid})`);
}

function loadInterface(interfaceName) {
    const config = JARVIS_INTERFACES[interfaceName];
    if (!config) {
        console.error(`❌ Interface '${interfaceName}' non trouvée`);
        return;
    }

    // Éviter les tentatives multiples simultanées
    if (isConnecting && interfaceName === 'dashboard') {
        console.log(`⚠️ Connexion ${interfaceName} déjà en cours...`);
        return;
    }

    if (interfaceName === 'dashboard') {
        isConnecting = true;
        connectionAttempts++;
    }

    console.log(`🌐 Chargement interface ${interfaceName} sur port ${config.port}... (Tentative ${connectionAttempts}/${maxConnectionAttempts})`);

    // Vérifier si l'interface est accessible
    const req = http.get(`http://127.0.0.1:${config.port}`, (res) => {
        console.log(`✅ Interface ${interfaceName} accessible, chargement...`);

        if (interfaceName === 'dashboard') {
            isConnecting = false;
            connectionAttempts = 0;
            // Arrêter le monitoring agressif
            if (connectionMonitorInterval) {
                clearInterval(connectionMonitorInterval);
                connectionMonitorInterval = null;
            }
        }

        mainWindow.loadURL(`http://127.0.0.1:${config.port}`);
        mainWindow.setTitle(config.title);

        // Optimisations M4 pour le rendu
        if (isAppleSilicon) {
            mainWindow.webContents.executeJavaScript(`
                console.log('🍎 Optimisations M4 activées pour ${interfaceName}');
                if (window.performance && window.performance.mark) {
                    window.performance.mark('m4-optimization-start');
                }
            `);
        }
    });

    req.on('error', () => {
        console.log(`⚠️ Interface ${interfaceName} non accessible`);

        if (interfaceName === 'dashboard') {
            isConnecting = false;

            if (connectionAttempts >= maxConnectionAttempts) {
                console.log(`❌ Échec connexion après ${maxConnectionAttempts} tentatives`);
                showJarvisConnectionPage();
                return;
            }

            // Réessayer après un délai croissant
            const delay = Math.min(connectionAttempts * 2000, 10000);
            setTimeout(() => {
                loadInterface(interfaceName);
            }, delay);
        }
    });

    req.setTimeout(5000, () => {
        req.destroy();
        console.log(`⏰ Timeout connexion ${interfaceName}`);

        if (interfaceName === 'dashboard') {
            isConnecting = false;

            if (connectionAttempts >= maxConnectionAttempts) {
                console.log(`❌ Timeout après ${maxConnectionAttempts} tentatives`);
                showJarvisConnectionPage();
                return;
            }

            // Réessayer après un délai
            setTimeout(() => {
                loadInterface(interfaceName);
            }, 3000);
        }
    });
}

function openInterfaceWindow(interfaceName) {
    const config = JARVIS_INTERFACES[interfaceName];
    if (!config) {
        console.error(`❌ Interface '${interfaceName}' non trouvée`);
        return;
    }

    // Si la fenêtre existe déjà, la mettre au premier plan
    if (interfaceWindows[interfaceName] && !interfaceWindows[interfaceName].isDestroyed()) {
        interfaceWindows[interfaceName].focus();
        return;
    }

    console.log(`🪟 Ouverture nouvelle fenêtre ${interfaceName}...`);

    // Options de fenêtre optimisées pour M4
    const windowOptions = {
        width: config.width,
        height: config.height,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false,
            hardwareAcceleration: isAppleSilicon,
            enableGPUAcceleration: isAppleSilicon
        },
        title: config.title,
        show: true,
        center: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        resizable: true,
        parent: mainWindow,
        titleBarStyle: isAppleSilicon ? 'hiddenInset' : 'default',
        vibrancy: isAppleSilicon ? 'dark' : undefined
    };

    interfaceWindows[interfaceName] = new BrowserWindow(windowOptions);
    const window = interfaceWindows[interfaceName];

    // Démarrer le service si nécessaire
    if (config.script && !pythonProcesses[interfaceName]) {
        startPythonService(interfaceName, config);
    }

    // Charger l'interface
    const req = http.get(`http://127.0.0.1:${config.port}`, (res) => {
        console.log(`✅ Interface ${interfaceName} accessible dans nouvelle fenêtre`);
        window.loadURL(`http://127.0.0.1:${config.port}`);
    });

    req.on('error', () => {
        console.log(`⚠️ Interface ${interfaceName} non accessible dans nouvelle fenêtre`);
        showWaitingPageInWindow(window, interfaceName, config);
    });

    req.setTimeout(3000, () => {
        req.destroy();
        showWaitingPageInWindow(window, interfaceName, config);
    });

    // Gérer la fermeture de la fenêtre
    window.on('closed', () => {
        delete interfaceWindows[interfaceName];
    });
}

function createMainMenuM4() {
    const template = [
        {
            label: '🤖 JARVIS M4',
            submenu: [
                {
                    label: '🏠 Dashboard Principal',
                    accelerator: 'CmdOrCtrl+H',
                    click: () => loadInterface('dashboard')
                },
                {
                    label: '🧠 Mémoire Thermique M4',
                    accelerator: 'CmdOrCtrl+M',
                    click: () => openInterfaceWindow('thermal_memory')
                },
                {
                    label: '🎨 Générateur Multimédia',
                    accelerator: 'CmdOrCtrl+G',
                    click: () => openInterfaceWindow('multimedia')
                },
                { type: 'separator' },
                {
                    label: '📊 Statistiques M4',
                    click: () => showM4Statistics()
                },
                {
                    label: '⚡ Optimisations M4',
                    click: () => showM4Optimizations()
                },
                { type: 'separator' },
                {
                    label: '🔄 Redémarrer Services',
                    click: () => restartAllServices()
                },
                {
                    label: '🛑 Quitter',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => app.quit()
                }
            ]
        },
        {
            label: '🧠 Mémoire & Formation',
            submenu: [
                {
                    label: '🧠 Mémoire Thermique',
                    click: () => openInterfaceWindow('thermal_memory')
                },
                {
                    label: '🎓 Formation JARVIS',
                    click: () => openInterfaceWindow('formation')
                },
                {
                    label: '🧬 Analyse Évolutive',
                    click: () => openInterfaceWindow('analysis')
                },
                { type: 'separator' },
                {
                    label: '🔍 Test Mémoire Complète',
                    click: () => runMemoryTest()
                },
                {
                    label: '📊 Rapport Formation',
                    click: () => generateFormationReport()
                }
            ]
        },
        {
            label: '🎨 Création & Multimédia',
            submenu: [
                {
                    label: '🎨 Générateur Multimédia',
                    click: () => openInterfaceWindow('multimedia')
                },
                {
                    label: '🖼️ Génération Images',
                    click: () => openImageGeneration()
                },
                {
                    label: '🎬 Génération Vidéos',
                    click: () => openVideoGeneration()
                },
                {
                    label: '🎵 Génération Musique',
                    click: () => openMusicGeneration()
                },
                { type: 'separator' },
                {
                    label: '📁 Ouvrir Dossier Créations',
                    click: () => shell.openPath(path.join(__dirname, 'jarvis_creations'))
                }
            ]
        },
        {
            label: '🤖 Multi-Agents',
            submenu: [
                {
                    label: '🤖 Interface Multi-Agents',
                    click: () => openInterfaceWindow('multiagents')
                },
                {
                    label: '💬 Communication',
                    click: () => openInterfaceWindow('communication')
                },
                { type: 'separator' },
                {
                    label: '🔄 Synchroniser Agents',
                    click: () => synchronizeAgents()
                },
                {
                    label: '⚡ Optimiser Agents',
                    click: () => optimizeAgents()
                }
            ]
        }
    ];

    // Menu spécifique Apple Silicon
    if (isAppleSilicon) {
        template.push({
            label: '🍎 Apple Silicon M4',
            submenu: [
                {
                    label: '📊 Statistiques M4',
                    click: () => showM4Statistics()
                },
                {
                    label: '⚡ Optimisations Actives',
                    click: () => showM4Optimizations()
                },
                {
                    label: '🧠 Neural Engine Status',
                    click: () => showNeuralEngineStatus()
                },
                {
                    label: '💾 Unified Memory Info',
                    click: () => showUnifiedMemoryInfo()
                },
                { type: 'separator' },
                {
                    label: '🚀 Mode Performance Max',
                    click: () => enableMaxPerformanceMode()
                },
                {
                    label: '🔋 Mode Efficacité',
                    click: () => enableEfficiencyMode()
                }
            ]
        });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);

    console.log('📋 Menu principal M4 créé');
}

function showJarvisConnectionPage() {
    console.log('🎨 Création page de connexion JARVIS avec support micro...');

    const connectionHTML = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>🤖 JARVIS M4 - Centre de Contrôle Complet</title>
        <meta charset="utf-8">
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                color: white;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .header {
                text-align: center;
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 30px;
                backdrop-filter: blur(10px);
            }
            .m4-stats {
                background: linear-gradient(45deg, #4CAF50, #8BC34A);
                padding: 25px;
                border-radius: 15px;
                margin: 20px 0;
                text-align: center;
            }
            .connection-card {
                background: rgba(255,255,255,0.1);
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                margin: 20px 0;
                backdrop-filter: blur(10px);
            }
            .connection-status {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
                font-weight: bold;
                font-size: 1.2em;
            }
            .connected { background: rgba(76, 175, 80, 0.4); }
            .connecting { background: rgba(255, 193, 7, 0.4); }
            .disconnected { background: rgba(244, 67, 54, 0.4); }
            .spinner {
                border: 4px solid rgba(255,255,255,0.3);
                border-top: 4px solid white;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin-right: 15px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .btn {
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                border: none;
                color: white;
                padding: 15px 30px;
                border-radius: 10px;
                font-size: 1.1em;
                cursor: pointer;
                margin: 10px;
                transition: transform 0.2s;
                font-weight: bold;
            }
            .btn:hover {
                transform: scale(1.05);
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            }
            .interfaces-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .interface-card {
                background: rgba(255,255,255,0.1);
                padding: 20px;
                border-radius: 12px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s;
                border: 2px solid transparent;
            }
            .interface-card:hover {
                transform: translateY(-5px);
                border-color: #feca57;
                box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 JARVIS M4 COMPLET</h1>
                <h2>Centre de Contrôle</h2>
                <p><strong>Jean-Luc Passave</strong> - Apple Silicon M4 Optimisé</p>
            </div>

            <div class="m4-stats">
                <h3>🍎 STATUT APPLE SILICON M4</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 15px;">
                    <div><strong>⚡ P-cores:</strong> 6 actifs</div>
                    <div><strong>🔋 E-cores:</strong> 4 actifs</div>
                    <div><strong>🧠 Neural Engine:</strong> ACTIF</div>
                    <div><strong>💾 Unified Memory:</strong> 16 GB</div>
                </div>
            </div>

            <div class="connection-card">
                <h3>🔗 Connexion JARVIS</h3>
                <div id="connection-status" class="connection-status connecting">
                    <div class="spinner"></div>
                    Recherche de JARVIS...
                </div>
                <button class="btn" onclick="checkConnection()">🔄 Vérifier Connexion</button>
                <button class="btn" onclick="openJarvisDirect()">🏠 JARVIS Direct</button>
                <button class="btn" onclick="location.reload()">🔄 Actualiser Page</button>
            </div>

            <div class="interfaces-grid" id="interfaces-grid">
                <!-- Les interfaces seront ajoutées ici -->
            </div>
        </div>

        <script>
            let connectionAttempts = 0;
            const maxAttempts = 20;
            let checkInterval;

            function checkConnection() {
                const statusEl = document.getElementById('connection-status');
                statusEl.className = 'connection-status connecting';
                statusEl.innerHTML = '<div class="spinner"></div>Vérification connexion JARVIS...';

                // Essayer de se connecter à JARVIS
                const img = new Image();
                img.onload = function() {
                    // JARVIS répond
                    statusEl.className = 'connection-status connected';
                    statusEl.innerHTML = '✅ JARVIS Connecté - Redirection automatique...';
                    setTimeout(() => {
                        window.location.href = 'http://localhost:7867';
                    }, 1500);
                };

                img.onerror = function() {
                    connectionAttempts++;
                    if (connectionAttempts < maxAttempts) {
                        statusEl.className = 'connection-status connecting';
                        statusEl.innerHTML = '<div class="spinner"></div>Tentative ' + connectionAttempts + '/' + maxAttempts + ' - JARVIS non trouvé';
                        setTimeout(checkConnection, 3000);
                    } else {
                        statusEl.className = 'connection-status disconnected';
                        statusEl.innerHTML = '❌ JARVIS non accessible<br><small>Démarrez votre programme JARVIS avec: python jarvis_architecture_multi_fenetres.py</small>';
                        // Reset après 15 secondes
                        setTimeout(() => {
                            connectionAttempts = 0;
                            checkConnection();
                        }, 15000);
                    }
                };

                // Tenter de charger une ressource de JARVIS
                img.src = 'http://localhost:7867/favicon.ico?' + Date.now();
            }

            function openJarvisDirect() {
                window.open('http://localhost:7867', '_blank');
            }

            function createInterfaceCards() {
                const interfaces = [
                    { name: 'Dashboard Principal', port: 7867, icon: '🏠', desc: 'Interface principale JARVIS' },
                    { name: 'Communication', port: 7866, icon: '💬', desc: 'Chat et communication' },
                    { name: 'Éditeur Code', port: 7868, icon: '💻', desc: 'Développement et code' },
                    { name: 'Pensées JARVIS', port: 7869, icon: '🧠', desc: 'Processus de réflexion' },
                    { name: 'Mémoire Thermique', port: 7874, icon: '💾', desc: 'Stockage intelligent' },
                    { name: 'Musique & Audio', port: 7876, icon: '🎵', desc: 'Génération audio' },
                    { name: 'Système', port: 7877, icon: '📊', desc: 'Monitoring système' },
                    { name: 'Multi-Agents', port: 7880, icon: '🤖', desc: 'Agents multiples' }
                ];

                const grid = document.getElementById('interfaces-grid');
                grid.innerHTML = '<h3 style="grid-column: 1/-1; text-align: center; margin-bottom: 20px;">🌐 Interfaces JARVIS Disponibles</h3>';

                interfaces.forEach(iface => {
                    const card = document.createElement('div');
                    card.className = 'interface-card';
                    card.innerHTML =
                        '<h4>' + iface.icon + ' ' + iface.name + '</h4>' +
                        '<p>' + iface.desc + '</p>' +
                        '<small>Port: ' + iface.port + '</small>';
                    card.onclick = () => window.open('http://localhost:' + iface.port, '_blank');
                    grid.appendChild(card);
                });
            }

            // Démarrer immédiatement
            console.log('🚀 Page de connexion JARVIS M4 chargée');
            checkConnection();
            createInterfaceCards();

            // Vérification automatique toutes les 10 secondes
            checkInterval = setInterval(() => {
                if (connectionAttempts < maxAttempts) {
                    checkConnection();
                }
            }, 10000);
        </script>
    </body>
    </html>
    `;

    try {
        mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(connectionHTML)}`);
        console.log('✅ Page de connexion JARVIS chargée');
    } catch (error) {
        console.error('❌ Erreur chargement page de connexion:', error);
    }
}

function showWaitingPageInWindow(window, interfaceName, config) {
    const waitingHTML = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>${config.title}</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                text-align: center;
            }
            .container {
                background: rgba(255,255,255,0.1);
                padding: 40px;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            }
            .spinner {
                border: 4px solid rgba(255,255,255,0.3);
                border-top: 4px solid white;
                border-radius: 50%;
                width: 50px;
                height: 50px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 ${config.title}</h1>
            <div class="spinner"></div>
            <p>Démarrage de l'interface...</p>
            <script>
                setTimeout(() => {
                    window.location.href = 'http://127.0.0.1:${config.port}';
                }, 5000);
            </script>
        </div>
    </body>
    </html>
    `;

    window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(waitingHTML)}`);
}

// Fonctions spécifiques Apple Silicon M4
function showM4Statistics() {
    const stats = {
        architecture: process.arch,
        platform: process.platform,
        cpuCores: os.cpus().length,
        totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024),
        freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024),
        ...m4Capabilities
    };

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '🍎 Statistiques Apple Silicon M4',
        message: 'Informations Système M4',
        detail: `
🍎 Architecture: ${stats.architecture}
💻 Plateforme: ${stats.platform}
🧠 CPU Total: ${stats.cpuCores} cœurs
⚡ P-cores: ${stats.performanceCores}
🔋 E-cores: ${stats.efficiencyCores}
🧠 Neural Engine: ${stats.neuralEngine ? 'Actif' : 'Inactif'}
💾 Mémoire Totale: ${stats.totalMemory} GB
💾 Mémoire Libre: ${stats.freeMemory} GB
🔗 Mémoire Unifiée: ${stats.unifiedMemory ? 'Oui' : 'Non'}

🚀 Services Python Actifs: ${Object.keys(pythonProcesses).length}
🪟 Fenêtres Ouvertes: ${Object.keys(interfaceWindows).length + 1}
        `,
        buttons: ['OK', '📊 Détails Avancés']
    }).then((result) => {
        if (result.response === 1) {
            showAdvancedM4Details();
        }
    });
}

function showM4Optimizations() {
    const optimizations = [
        '🍎 Détection automatique Apple Silicon',
        '⚡ Utilisation optimale P-cores/E-cores',
        '🧠 Intégration Neural Engine',
        '💾 Exploitation mémoire unifiée',
        '🚀 Accélération GPU intégrée',
        '🔧 Variables d\'environnement optimisées',
        '📊 Monitoring performance temps réel',
        '🎯 Allocation ressources intelligente'
    ];

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '⚡ Optimisations M4 Actives',
        message: 'Optimisations Apple Silicon Activées',
        detail: optimizations.join('\n'),
        buttons: ['OK']
    });
}

function showNeuralEngineStatus() {
    const status = m4Capabilities.neuralEngine ? 'Actif et Opérationnel' : 'Non Disponible';
    const details = m4Capabilities.neuralEngine ?
        'Le Neural Engine est utilisé pour:\n• Accélération IA/ML\n• Traitement mémoire thermique\n• Optimisation recherche intelligente\n• Génération multimédia' :
        'Neural Engine non disponible sur cette architecture';

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '🧠 Neural Engine Status',
        message: `Status: ${status}`,
        detail: details,
        buttons: ['OK']
    });
}

function showUnifiedMemoryInfo() {
    const memInfo = {
        total: Math.round(os.totalmem() / 1024 / 1024 / 1024),
        free: Math.round(os.freemem() / 1024 / 1024 / 1024),
        used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024 / 1024),
        percentage: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100)
    };

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '💾 Unified Memory Info',
        message: 'Informations Mémoire Unifiée',
        detail: `
💾 Mémoire Totale: ${memInfo.total} GB
✅ Mémoire Libre: ${memInfo.free} GB
🔥 Mémoire Utilisée: ${memInfo.used} GB
📊 Utilisation: ${memInfo.percentage}%

🍎 Avantages Mémoire Unifiée:
• Partage CPU/GPU sans copie
• Latence réduite
• Bande passante optimisée
• Efficacité énergétique
        `,
        buttons: ['OK']
    });
}

// Fonctions de gestion des services
function restartAllServices() {
    console.log('🔄 Redémarrage de tous les services...');

    // Arrêter tous les processus Python
    Object.entries(pythonProcesses).forEach(([serviceName, process]) => {
        if (process && !process.killed) {
            console.log(`🛑 Arrêt service ${serviceName}...`);
            process.kill('SIGTERM');
        }
    });

    pythonProcesses = {};

    // Redémarrer les services essentiels après un délai
    setTimeout(() => {
        startEssentialPythonServices();

        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: '🔄 Services Redémarrés',
            message: 'Tous les services ont été redémarrés avec succès',
            buttons: ['OK']
        });
    }, 2000);
}

function runMemoryTest() {
    console.log('🧠 Lancement test mémoire complète...');

    const testProcess = spawn('python3', ['test_code_complet_final.py'], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';

    testProcess.stdout.on('data', (data) => {
        output += data.toString();
    });

    testProcess.stderr.on('data', (data) => {
        output += data.toString();
    });

    testProcess.on('close', (code) => {
        dialog.showMessageBox(mainWindow, {
            type: code === 0 ? 'info' : 'error',
            title: '🧠 Test Mémoire Complète',
            message: `Test terminé avec code ${code}`,
            detail: output.slice(-1000), // Derniers 1000 caractères
            buttons: ['OK']
        });
    });
}

// Fonctions de génération multimédia
function openImageGeneration() {
    openInterfaceWindow('multimedia');
    setTimeout(() => {
        const window = interfaceWindows['multimedia'];
        if (window && !window.isDestroyed()) {
            window.webContents.executeJavaScript(`
                // Activer l'onglet génération d'images
                const imageTab = document.querySelector('[data-tab="images"]');
                if (imageTab) imageTab.click();
            `);
        }
    }, 2000);
}

function openVideoGeneration() {
    openInterfaceWindow('multimedia');
    setTimeout(() => {
        const window = interfaceWindows['multimedia'];
        if (window && !window.isDestroyed()) {
            window.webContents.executeJavaScript(`
                // Activer l'onglet génération de vidéos
                const videoTab = document.querySelector('[data-tab="videos"]');
                if (videoTab) videoTab.click();
            `);
        }
    }, 2000);
}

function openMusicGeneration() {
    openInterfaceWindow('multimedia');
    setTimeout(() => {
        const window = interfaceWindows['multimedia'];
        if (window && !window.isDestroyed()) {
            window.webContents.executeJavaScript(`
                // Activer l'onglet génération de musique
                const musicTab = document.querySelector('[data-tab="music"]');
                if (musicTab) musicTab.click();
            `);
        }
    }, 2000);
}

// Fonctions multi-agents
function synchronizeAgents() {
    console.log('🔄 Synchronisation des agents...');

    // Envoyer signal de synchronisation à tous les services
    Object.entries(pythonProcesses).forEach(([serviceName, process]) => {
        if (process && !process.killed) {
            // Envoyer signal SIGUSR1 pour synchronisation
            try {
                process.kill('SIGUSR1');
                console.log(`📡 Signal sync envoyé à ${serviceName}`);
            } catch (error) {
                console.error(`❌ Erreur sync ${serviceName}:`, error);
            }
        }
    });

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '🔄 Synchronisation Agents',
        message: 'Signal de synchronisation envoyé à tous les agents',
        buttons: ['OK']
    });
}

function optimizeAgents() {
    console.log('⚡ Optimisation des agents...');

    // Optimisations spécifiques M4
    if (isAppleSilicon) {
        // Répartir les agents sur P-cores et E-cores
        const agentOptimizations = {
            'thermal_memory': 'performance',  // P-cores
            'dashboard': 'performance',       // P-cores
            'multimedia': 'performance',      // P-cores
            'formation': 'efficiency',       // E-cores
            'analysis': 'efficiency',        // E-cores
            'communication': 'performance'   // P-cores
        };

        Object.entries(agentOptimizations).forEach(([agent, coreType]) => {
            console.log(`🎯 ${agent} → ${coreType} cores`);
        });
    }

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '⚡ Optimisation Agents',
        message: isAppleSilicon ?
            'Agents optimisés pour Apple Silicon M4\nRépartition P-cores/E-cores activée' :
            'Agents optimisés pour architecture standard',
        buttons: ['OK']
    });
}

function generateFormationReport() {
    console.log('📊 Génération rapport formation...');

    const reportProcess = spawn('python3', ['-c', `
from jarvis_formation_complete import get_formation_jarvis
formation = get_formation_jarvis()
status = formation.get_competences_status()
print(f"Niveau: {status['niveau_formation']}")
print(f"Compétences: {status['nombre_competences']}")
print(f"Formation complète: {status['formation_complete']}")
    `], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';

    reportProcess.stdout.on('data', (data) => {
        output += data.toString();
    });

    reportProcess.on('close', (code) => {
        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: '📊 Rapport Formation JARVIS',
            message: 'Statut Formation Actuel',
            detail: output || 'Rapport généré avec succès',
            buttons: ['OK', '🎓 Ouvrir Formation']
        }).then((result) => {
            if (result.response === 1) {
                openInterfaceWindow('formation');
            }
        });
    });
}

function enableMaxPerformanceMode() {
    console.log('🚀 Activation mode performance maximale...');

    if (isAppleSilicon) {
        // Optimisations M4 performance max
        Object.values(interfaceWindows).forEach(window => {
            if (window && !window.isDestroyed()) {
                window.webContents.executeJavaScript(`
                    console.log('🚀 Mode performance M4 activé');
                    if (window.performance) {
                        window.performance.mark('m4-max-performance-mode');
                    }
                `);
            }
        });
    }

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '🚀 Mode Performance Max',
        message: 'Mode performance maximale activé',
        detail: isAppleSilicon ?
            '🍎 Optimisations M4 activées:\n• P-cores prioritaires\n• GPU accéléré\n• Neural Engine optimisé' :
            'Optimisations standard activées',
        buttons: ['OK']
    });
}

function enableEfficiencyMode() {
    console.log('🔋 Activation mode efficacité...');

    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '🔋 Mode Efficacité',
        message: 'Mode efficacité activé',
        detail: isAppleSilicon ?
            '🍎 Optimisations M4 efficacité:\n• E-cores prioritaires\n• Consommation réduite\n• Performance équilibrée' :
            'Mode efficacité standard activé',
        buttons: ['OK']
    });
}

// Gestion des événements d'application
app.whenReady().then(() => {
    console.log('🚀 JARVIS ELECTRON M4 - Application prête');
    console.log(`🍎 Apple Silicon: ${isAppleSilicon ? 'OUI' : 'NON'}`);

    if (isAppleSilicon) {
        console.log(`⚡ P-cores: ${m4Capabilities.performanceCores}`);
        console.log(`🔋 E-cores: ${m4Capabilities.efficiencyCores}`);
        console.log(`🧠 Neural Engine: ${m4Capabilities.neuralEngine ? 'ACTIF' : 'INACTIF'}`);
        console.log(`💾 Unified Memory: ${m4Capabilities.totalMemory} GB`);
    }

    createMainWindow();

    // Optimisations macOS
    if (process.platform === 'darwin') {
        // app.dock.setIcon(path.join(__dirname, 'assets', 'jarvis-icon.png'));
        console.log('🍎 Optimisations macOS activées');
    }
});

app.on('window-all-closed', () => {
    console.log('🔄 Toutes les fenêtres fermées');

    // Arrêter tous les processus Python
    Object.values(pythonProcesses).forEach(process => {
        if (process && !process.killed) {
            process.kill('SIGTERM');
        }
    });

    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    }
});

// Gestion des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
});

// IPC pour communication avec les renderers
ipcMain.handle('get-m4-capabilities', () => {
    return m4Capabilities;
});

ipcMain.handle('get-python-services', () => {
    return Object.keys(pythonProcesses);
});

ipcMain.handle('get-jarvis-status', async () => {
    return new Promise((resolve) => {
        const req = http.get('http://127.0.0.1:7867', (res) => {
            resolve({ status: 'connected', port: 7867, message: 'JARVIS opérationnel' });
        });

        req.on('error', () => {
            resolve({ status: 'disconnected', port: 7867, message: 'JARVIS non accessible' });
        });

        req.setTimeout(2000, () => {
            req.destroy();
            resolve({ status: 'timeout', port: 7867, message: 'Timeout connexion JARVIS' });
        });
    });
});

ipcMain.handle('open-jarvis-interface', (event, interfaceName) => {
    if (JARVIS_INTERFACES[interfaceName]) {
        openInterfaceWindow(interfaceName);
        return true;
    }
    return false;
});

ipcMain.handle('restart-service', (event, serviceName) => {
    const config = JARVIS_INTERFACES[serviceName];
    if (config) {
        // Pour les interfaces JARVIS, on recharge juste la fenêtre
        if (interfaceWindows[serviceName] && !interfaceWindows[serviceName].isDestroyed()) {
            interfaceWindows[serviceName].reload();
        } else {
            openInterfaceWindow(serviceName);
        }
        return true;
    }
    return false;
});

console.log('✅ JARVIS ELECTRON M4 COMPLET - Initialisé');
console.log('🎯 Jean-Luc Passave - Application prête pour utilisation');

// Export pour tests
module.exports = {
    m4Capabilities,
    JARVIS_INTERFACES,
    isAppleSilicon
};