#!/usr/bin/env python3
"""
TEST MÉMOIRE THERMIQUE TURBO ADAPTATIF - JEAN-LUC PASSAVE
Teste le système turbo adaptatif et vérifie qu'il évite la saturation
"""

import time
import threading
from memoire_thermique_turbo_adaptatif import get_memoire_thermique

def test_turbo_adaptatif():
    """Test complet du système turbo adaptatif"""
    
    print("🚀 TEST MÉMOIRE THERMIQUE TURBO ADAPTATIF")
    print("=" * 60)
    print("Jean-Luc Passave - Test du code vivant adaptatif")
    print("=" * 60)
    
    # Obtenir l'instance de mémoire thermique
    memoire = get_memoire_thermique()
    
    print("\n📊 ÉTAT INITIAL:")
    stats = memoire.get_stats_performance()
    print(f"⚡ Facteur Turbo: {stats['facteur_turbo']:.2f}x")
    print(f"🖥️ CPU: {stats['cpu_usage']:.1f}%")
    print(f"💾 RAM: {stats['memory_usage']:.1f}%")
    print(f"🧠 Cache: {stats['cache_size']} éléments")
    print(f"📊 Neurones: {stats['total_neurones']}")
    
    # Test 1: Ajouter des données et vérifier l'adaptation
    print("\n🧪 TEST 1: Ajout de données avec adaptation automatique")
    for i in range(100):
        memoire.ajouter(
            cle=f"test_turbo_{i}",
            valeur=f"Données de test turbo adaptatif {i} - Jean-Luc Passave",
            keywords=["test", "turbo", "adaptatif"],
            zone_id="test_zone"
        )
        
        if i % 20 == 0:
            stats = memoire.get_stats_performance()
            print(f"  📈 Étape {i}: Turbo {stats['facteur_turbo']:.2f}x, CPU {stats['cpu_usage']:.1f}%, RAM {stats['memory_usage']:.1f}%")
    
    # Test 2: Recherche turbo
    print("\n🔍 TEST 2: Recherche turbo ultra-rapide")
    start_time = time.time()
    
    for i in range(50):
        result = memoire.rechercher_turbo(f"test_turbo_{i}")
        if result:
            print(f"  ✅ Trouvé: test_turbo_{i}")
        else:
            print(f"  ❌ Non trouvé: test_turbo_{i}")
    
    search_time = time.time() - start_time
    print(f"⏱️ Temps de recherche: {search_time:.3f}s pour 50 recherches")
    
    # Test 3: Recherche par mots-clés
    print("\n🏷️ TEST 3: Recherche par mots-clés")
    resultats_keywords = memoire.rechercher_par_keyword("turbo")
    print(f"  📊 Résultats pour 'turbo': {len(resultats_keywords)} éléments")
    
    # Test 4: Vérifier l'auto-adaptation
    print("\n🔄 TEST 4: Vérification auto-adaptation")
    stats_avant = memoire.get_stats_performance()
    
    # Simuler une charge
    print("  🔥 Simulation de charge...")
    for i in range(200, 300):
        memoire.ajouter(
            cle=f"charge_test_{i}",
            valeur=f"Données de charge {i}" * 100,  # Données plus volumineuses
            keywords=["charge", "test"],
            zone_id="charge_zone"
        )
    
    time.sleep(5)  # Laisser le temps à l'adaptation
    
    stats_apres = memoire.get_stats_performance()
    
    print(f"  📊 AVANT - Turbo: {stats_avant['facteur_turbo']:.2f}x, CPU: {stats_avant['cpu_usage']:.1f}%")
    print(f"  📊 APRÈS - Turbo: {stats_apres['facteur_turbo']:.2f}x, CPU: {stats_apres['cpu_usage']:.1f}%")
    
    if stats_apres['facteur_turbo'] != stats_avant['facteur_turbo']:
        print("  ✅ ADAPTATION DÉTECTÉE - Le turbo s'adapte automatiquement !")
    else:
        print("  ℹ️ Pas d'adaptation nécessaire - Ressources suffisantes")
    
    # Test 5: Notifications et calendrier
    print("\n📅 TEST 5: Notifications et calendrier")
    
    from datetime import datetime, timedelta
    
    # Ajouter une notification
    future_time = datetime.now() + timedelta(seconds=2)
    memoire.ajouter_notification(future_time, "Test notification turbo adaptatif")
    
    # Ajouter un événement calendrier
    today = datetime.now().date().isoformat()
    memoire.enregistrer_evenement_calendrier(today, "Test événement turbo")
    
    print("  📝 Notification ajoutée pour dans 2 secondes")
    print("  📅 Événement calendrier ajouté pour aujourd'hui")
    
    # Attendre et vérifier
    time.sleep(3)
    notifications = memoire.verifier_notifications()
    evenements = memoire.evenements_calendrier(today)
    
    print(f"  🛎️ Notifications reçues: {len(notifications)}")
    print(f"  📅 Événements aujourd'hui: {len(evenements)}")
    
    # Test 6: Résumé et archives
    print("\n📋 TEST 6: Résumé et archives")
    
    resume = memoire.generer_resume(today)
    print("  📄 Résumé généré:")
    print("  " + "\n  ".join(resume.split('\n')[:5]))  # Afficher les 5 premières lignes
    
    # Vérifier les archives
    archives = memoire.get_archive("test_turbo_1")
    print(f"  🗃️ Archives pour test_turbo_1: {len(archives)} versions")
    
    # Test 7: Statistiques finales
    print("\n📊 STATISTIQUES FINALES:")
    stats_finales = memoire.get_stats_performance()
    
    print(f"⚡ Facteur Turbo Final: {stats_finales['facteur_turbo']:.2f}x")
    print(f"🖥️ CPU Final: {stats_finales['cpu_usage']:.1f}%")
    print(f"💾 RAM Finale: {stats_finales['memory_usage']:.1f}%")
    print(f"🧠 Cache Final: {stats_finales['cache_size']} éléments")
    print(f"📊 Neurones Totaux: {stats_finales['total_neurones']}")
    print(f"🗃️ Archives Totales: {stats_finales['total_archives']}")
    
    # Vérification finale
    print("\n🎯 VÉRIFICATION FINALE:")
    
    if stats_finales['facteur_turbo'] > 0:
        print("✅ TURBO ADAPTATIF: FONCTIONNEL")
    else:
        print("❌ TURBO ADAPTATIF: PROBLÈME")
    
    if stats_finales['cpu_usage'] < 90:
        print("✅ ÉVITEMENT SATURATION CPU: RÉUSSI")
    else:
        print("⚠️ ÉVITEMENT SATURATION CPU: ATTENTION")
    
    if stats_finales['memory_usage'] < 90:
        print("✅ ÉVITEMENT SATURATION RAM: RÉUSSI")
    else:
        print("⚠️ ÉVITEMENT SATURATION RAM: ATTENTION")
    
    if stats_finales['total_neurones'] > 0:
        print("✅ MÉMOIRE THERMIQUE: ACTIVE")
    else:
        print("❌ MÉMOIRE THERMIQUE: PROBLÈME")
    
    print("\n🎉 TEST TERMINÉ - MÉMOIRE THERMIQUE TURBO ADAPTATIF VALIDÉE !")
    print("🚀 Code vivant qui s'adapte automatiquement à la machine ✅")
    
    return True

def test_monitoring_continu():
    """Test du monitoring continu en arrière-plan"""
    
    print("\n🔄 TEST MONITORING CONTINU (30 secondes)...")
    
    memoire = get_memoire_thermique()
    
    for i in range(6):  # 6 fois 5 secondes = 30 secondes
        stats = memoire.get_stats_performance()
        print(f"  📊 {i*5}s - Turbo: {stats['facteur_turbo']:.2f}x, CPU: {stats['cpu_usage']:.1f}%, RAM: {stats['memory_usage']:.1f}%")
        time.sleep(5)
    
    print("✅ Monitoring continu validé")

if __name__ == "__main__":
    try:
        # Test principal
        test_turbo_adaptatif()
        
        # Test monitoring continu
        test_monitoring_continu()
        
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("🚀 MÉMOIRE THERMIQUE TURBO ADAPTATIF PARFAITEMENT FONCTIONNELLE")
        
    except Exception as e:
        print(f"\n❌ ERREUR DURANT LES TESTS: {e}")
        import traceback
        traceback.print_exc()
