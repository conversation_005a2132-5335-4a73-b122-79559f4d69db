#!/usr/bin/env python3
"""
🤖 AGENT #2 TRADUCTEUR TURBO JARVIS
Module de traduction collaborative avec accélérateurs Kyber
Créé pour Jean-Luc Passave
"""

import requests
import json
import time
from datetime import datetime
import threading
import queue

class Agent2TraducteurTurbo:
    """Agent #2 avec turbo accélérateurs Kyber pour traduction"""
    
    def __init__(self):
        self.active = True
        self.queue_traduction = queue.Queue()
        self.cache_traductions = {}
        self.stats = {
            "traductions_total": 0,
            "cache_hits": 0,
            "turbo_accelerations": 0
        }
        
        # 🚀 TURBO ACCÉLÉRATEURS KYBER
        self.kyber_accelerators = {
            "cache_intelligent": True,
            "prediction_contextuelle": True,
            "optimisation_batch": True,
            "compression_semantique": True
        }
        
        print("🤖 AGENT #2 TRADUCTEUR TURBO initialisé")
        print("🚀 Accélérateurs Kyber activés")
        
    def demarrer_service_traduction(self):
        """Démarre le service de traduction en arrière-plan"""
        traduction_thread = threading.Thread(target=self._worker_traduction)
        traduction_thread.daemon = True
        traduction_thread.start()
        print("✅ Service traduction Agent #2 démarré")
    
    def _worker_traduction(self):
        """Worker de traduction en arrière-plan"""
        while self.active:
            try:
                if not self.queue_traduction.empty():
                    tache = self.queue_traduction.get(timeout=1)
                    resultat = self._traduire_avec_kyber(tache)
                    tache['callback'](resultat)
                else:
                    time.sleep(0.1)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Erreur worker traduction: {e}")
    
    def _traduire_avec_kyber(self, tache):
        """Traduction avec accélérateurs Kyber"""
        texte = tache['texte']
        langue_cible = tache.get('langue_cible', 'français')
        
        # 🚀 ACCÉLÉRATEUR KYBER #1: Cache intelligent
        if self.kyber_accelerators['cache_intelligent']:
            cache_key = f"{texte}_{langue_cible}"
            if cache_key in self.cache_traductions:
                self.stats['cache_hits'] += 1
                self.stats['turbo_accelerations'] += 1
                return self.cache_traductions[cache_key]
        
        # 🚀 ACCÉLÉRATEUR KYBER #2: Prédiction contextuelle
        if self.kyber_accelerators['prediction_contextuelle']:
            if self._est_deja_en_francais(texte):
                return texte  # Pas besoin de traduire
        
        # 🚀 ACCÉLÉRATEUR KYBER #3: Optimisation batch
        if self.kyber_accelerators['optimisation_batch']:
            texte = self._optimiser_pour_traduction(texte)
        
        # Traduction via DeepSeek R1 8B
        try:
            traduction = self._traduire_deepseek(texte, langue_cible)
            
            # Sauvegarder en cache
            if self.kyber_accelerators['cache_intelligent']:
                self.cache_traductions[cache_key] = traduction
            
            self.stats['traductions_total'] += 1
            return traduction
            
        except Exception as e:
            print(f"❌ Erreur traduction DeepSeek: {e}")
            return texte  # Retourner texte original si échec
    
    def _est_deja_en_francais(self, texte):
        """Détecte si le texte est déjà en français"""
        mots_francais = ['le', 'la', 'les', 'de', 'du', 'des', 'et', 'ou', 'est', 'sont', 'avec', 'pour', 'dans', 'sur', 'par']
        mots_texte = texte.lower().split()
        
        if len(mots_texte) < 3:
            return True  # Texte trop court, on assume français
        
        mots_francais_trouves = sum(1 for mot in mots_texte if mot in mots_francais)
        ratio = mots_francais_trouves / len(mots_texte)
        
        return ratio > 0.3  # Si plus de 30% de mots français
    
    def _optimiser_pour_traduction(self, texte):
        """Optimise le texte pour traduction plus rapide"""
        # Supprimer éléments techniques qui n'ont pas besoin de traduction
        if texte.startswith('🧠') or texte.startswith('💭'):
            return texte  # Garder les emojis et pensées
        
        # 🚀 ACCÉLÉRATEUR KYBER #4: Compression sémantique
        if self.kyber_accelerators['compression_semantique']:
            if len(texte) > 500:
                # Garder les parties importantes
                return texte[:400] + "..."
        
        return texte
    
    def _traduire_deepseek(self, texte, langue_cible):
        """Traduction via DeepSeek R1 8B"""
        try:
            payload = {
                "model": "deepseek-r1:8b-llama-distill-q4_K_M",
                "messages": [
                    {
                        "role": "system",
                        "content": f"Tu es un traducteur expert. Traduis le texte suivant en {langue_cible} de manière naturelle et fluide. Garde les emojis et la structure."
                    },
                    {
                        "role": "user",
                        "content": texte
                    }
                ],
                "stream": False,
                "options": {
                    "temperature": 0.3,  # Traduction plus précise
                    "num_predict": 200
                }
            }
            
            # 🚀 AGENT #2 SUR PORT SÉPARÉ - JEAN-LUC PASSAVE
            endpoints_agent2 = [
                "http://localhost:11434/v1/chat/completions",  # Ollama Agent #2
                "http://localhost:1234/v1/chat/completions",   # LM Studio Agent #2
                "http://localhost:8001/v1/chat/completions"    # VLLM Agent #2 (port différent)
            ]

            response = None
            for endpoint in endpoints_agent2:
                try:
                    response = requests.post(endpoint, json=payload, timeout=5)  # Plus rapide
                    if response.status_code == 200:
                        break
                except:
                    continue
            
            if response.status_code == 200:
                result = response.json()
                traduction = result["choices"][0]["message"]["content"]
                return traduction.strip()
            else:
                return texte
                
        except Exception as e:
            print(f"❌ Erreur DeepSeek traduction: {e}")
            return texte
    
    def traduire_async(self, texte, langue_cible='français', callback=None):
        """Traduction asynchrone"""
        if not callback:
            callback = lambda x: print(f"✅ Traduit: {x}")
        
        tache = {
            'texte': texte,
            'langue_cible': langue_cible,
            'callback': callback,
            'timestamp': datetime.now().isoformat()
        }
        
        self.queue_traduction.put(tache)
    
    def traduire_sync(self, texte, langue_cible='français'):
        """Traduction synchrone"""
        tache = {
            'texte': texte,
            'langue_cible': langue_cible
        }
        return self._traduire_avec_kyber(tache)
    
    def get_stats_turbo(self):
        """Statistiques des accélérateurs Kyber"""
        return {
            "agent": "Agent #2 Traducteur Turbo",
            "accelerateurs_kyber": self.kyber_accelerators,
            "stats": self.stats,
            "cache_size": len(self.cache_traductions),
            "queue_size": self.queue_traduction.qsize(),
            "performance": f"{self.stats['turbo_accelerations']} accélérations Kyber"
        }
    
    def vider_cache(self):
        """Vide le cache de traductions"""
        self.cache_traductions.clear()
        print("🗑️ Cache traductions vidé")
    
    def arreter_service(self):
        """Arrête le service de traduction"""
        self.active = False
        print("🛑 Agent #2 Traducteur Turbo arrêté")

# Instance globale Agent #2
agent2_traducteur = Agent2TraducteurTurbo()

def demarrer_agent2():
    """Démarre l'Agent #2 Traducteur Turbo"""
    agent2_traducteur.demarrer_service_traduction()
    return agent2_traducteur

def traduire_avec_agent2(texte, langue_cible='français'):
    """Interface simple pour traduction avec Agent #2"""
    return agent2_traducteur.traduire_sync(texte, langue_cible)

def traduire_async_agent2(texte, callback, langue_cible='français'):
    """Interface simple pour traduction asynchrone avec Agent #2"""
    agent2_traducteur.traduire_async(texte, langue_cible, callback)

if __name__ == "__main__":
    print("🤖 TEST AGENT #2 TRADUCTEUR TURBO")
    print("=" * 50)
    
    agent = demarrer_agent2()
    
    # Test traduction
    test_texte = "Hello, this is a test of the turbo translation system"
    resultat = agent.traduire_sync(test_texte)
    print(f"Original: {test_texte}")
    print(f"Traduit: {resultat}")
    
    # Stats
    stats = agent.get_stats_turbo()
    print(f"Stats: {stats}")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        agent.arreter_service()
