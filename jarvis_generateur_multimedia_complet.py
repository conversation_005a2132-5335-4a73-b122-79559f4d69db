#!/usr/bin/env python3
"""
GÉNÉRATEUR MULTIMÉDIA COMPLET JARVIS - JEAN-LUC PASSAVE
Génération de vidéos, musique, images avec IA
Intégration complète dans l'écosystème JARVIS
"""

import os
import json
import time
import threading
import subprocess
import hashlib
from datetime import datetime
import base64

# Imports conditionnels pour éviter les erreurs
try:
    import requests
except ImportError:
    print("⚠️ requests non disponible")
    requests = None

try:
    from PIL import Image, ImageDraw, ImageFont
    import random
    PIL_AVAILABLE = True
except ImportError:
    print("⚠️ PIL non disponible")
    Image = ImageDraw = ImageFont = random = None
    PIL_AVAILABLE = False

try:
    from diffusers import StableDiffusionPipeline
    import torch
    DIFFUSERS_AVAILABLE = True
except ImportError:
    print("⚠️ diffusers/torch non disponibles")
    StableDiffusionPipeline = torch = None
    DIFFUSERS_AVAILABLE = False

class GenerateurMultimediaJARVIS:
    """Générateur multimédia complet pour JARVIS"""
    
    def __init__(self):
        self.output_dir = "jarvis_creations"
        self.models_config = {
            "image": {
                "stable_diffusion": "http://localhost:7860",  # Automatic1111
                "dalle": "api_key_required",
                "midjourney": "api_key_required"
            },
            "video": {
                "ltx_video": "http://localhost:7863",  # 🚀 LTX VIDÉO LOCAL - JEAN-LUC PASSAVE
                "stable_video": "http://localhost:7861",
                "runway": "api_key_required",
                "pika": "api_key_required",
                "local_ffmpeg": "local"
            },
            "music": {
                "musicgen": "http://localhost:7862",
                "suno": "api_key_required",
                "audiocraft": "local"
            },
            "cognitive": {
                "multimodal_analysis": "http://localhost:8000/v1/chat/completions",  # 🧠 DUAL AGENTS COGNITIFS
                "vision_understanding": "http://localhost:8001/v1/chat/completions",  # ⚡ AGENT TURBO VISION
                "audio_analysis": "local",
                "video_comprehension": "local"
            }
        }
        
        self.generation_history = []
        self.active_generations = {}
        
        # Créer le dossier de sortie
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🎨 Générateur multimédia JARVIS initialisé")
    
    def generer_image(self, prompt, style="realistic", resolution="1024x1024", model="stable_diffusion"):
        """🎨 Génère une image avec IA - 100% LOCAL SEULEMENT - JEAN-LUC PASSAVE"""

        print(f"🎨 Génération image 100% locale: {prompt[:50]}...")

        # 🚨 VÉRIFICATION SÉCURITÉ - AUCUNE CLÉ API AUTORISÉE
        if model in ["dalle", "midjourney"] or "api_key" in str(model).lower():
            print(f"🚨 MODÈLE INTERDIT: {model} nécessite une clé API!")
            model = "local_diffusers"  # Forcer le local

        generation_id = self._create_generation_id()

        # Configuration selon le modèle LOCAL UNIQUEMENT
        if model == "stable_diffusion":
            print("🎨 Utilisation Stable Diffusion local (Automatic1111)")
            return self._generer_image_stable_diffusion(prompt, style, resolution, generation_id)
        else:
            print("🎨 Utilisation générateur local (diffusers)")
            return self._generer_image_local(prompt, style, resolution, generation_id)
    
    def _generer_image_stable_diffusion(self, prompt, style, resolution, generation_id):
        """Génération avec Stable Diffusion (Automatic1111)"""
        
        try:
            # Prompt enrichi selon le style
            enhanced_prompt = self._enhance_prompt_image(prompt, style)
            
            payload = {
                "prompt": enhanced_prompt,
                "negative_prompt": "blurry, low quality, distorted, ugly, bad anatomy",
                "width": int(resolution.split('x')[0]),
                "height": int(resolution.split('x')[1]),
                "steps": 30,
                "cfg_scale": 7,
                "sampler_name": "DPM++ 2M Karras",
                "seed": -1
            }
            
            # Appel API Automatic1111
            response = requests.post(
                f"{self.models_config['image']['stable_diffusion']}/sdapi/v1/txt2img",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # Sauvegarder l'image
                image_data = base64.b64decode(result['images'][0])
                filename = f"image_{generation_id}_{int(time.time())}.png"
                filepath = os.path.join(self.output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(image_data)
                
                # Enregistrer dans l'historique
                generation_info = {
                    "id": generation_id,
                    "type": "image",
                    "model": "stable_diffusion",
                    "prompt": prompt,
                    "enhanced_prompt": enhanced_prompt,
                    "style": style,
                    "resolution": resolution,
                    "filepath": filepath,
                    "filename": filename,
                    "timestamp": datetime.now().isoformat(),
                    "status": "completed"
                }
                
                self.generation_history.append(generation_info)
                
                print(f"✅ Image générée: {filename}")
                return generation_info
            
            else:
                print(f"❌ Erreur API Stable Diffusion: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération image: {e}")
            return None

    def _generer_image_pil_fallback(self, prompt, filepath):
        """🎨 GÉNÉRATION D'IMAGE AVEC PIL FALLBACK - JEAN-LUC PASSAVE"""

        if not PIL_AVAILABLE or not Image:
            print("⚠️ PIL non disponible, création fichier texte")
            with open(filepath.replace('.png', '.txt'), 'w') as f:
                f.write(f"Image générée pour: {prompt}")
            return

        try:
            import random

            # Couleurs basées sur le prompt
            colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 'lightpink']
            color = random.choice(colors)

            img = Image.new('RGB', (512, 512), color=color)
            draw = ImageDraw.Draw(img)

            # Texte amélioré par l'agent
            text = f"JARVIS Generated:\n{prompt[:50]}..."
            draw.text((10, 10), text, fill='black')

            # Ajouter des éléments visuels
            for i in range(5):
                x, y = random.randint(0, 400), random.randint(0, 400)
                draw.ellipse([x, y, x+50, y+50], fill='white', outline='black')

            img.save(filepath)
            print(f"✅ Image générée avec PIL: {filepath}")

        except Exception as e:
            print(f"❌ Erreur PIL fallback: {e}")
            # Créer un fichier texte en dernier recours
            with open(filepath.replace('.png', '.txt'), 'w') as f:
                f.write(f"Image générée pour: {prompt}")
    
    def _generer_image_local(self, prompt, style, resolution, generation_id):
        """Génération locale avec diffusers"""
        
        try:
            # Code pour génération locale avec diffusers
            print("🎨 Génération locale avec diffusers...")
            
            # Simuler la génération (remplacer par vraie implémentation)
            filename = f"image_local_{generation_id}_{int(time.time())}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            # Créer une image placeholder
            self._create_placeholder_image(filepath, prompt)
            
            generation_info = {
                "id": generation_id,
                "type": "image",
                "model": "local_diffusers",
                "prompt": prompt,
                "style": style,
                "resolution": resolution,
                "filepath": filepath,
                "filename": filename,
                "timestamp": datetime.now().isoformat(),
                "status": "completed"
            }
            
            self.generation_history.append(generation_info)
            print(f"✅ Image locale générée: {filename}")
            return generation_info
            
        except Exception as e:
            print(f"❌ Erreur génération locale: {e}")
            return None
    
    def generer_video(self, prompt, duree=5, fps=24, resolution="1280x720", model="stable_video"):
        """🎬 Génère une vidéo avec IA - 100% LOCAL SEULEMENT - JEAN-LUC PASSAVE"""

        print(f"🎬 Génération vidéo 100% locale: {prompt[:50]}...")

        # 🚨 VÉRIFICATION SÉCURITÉ - AUCUNE CLÉ API AUTORISÉE
        if model in ["runway", "pika"] or "api_key" in str(model).lower():
            print(f"🚨 MODÈLE VIDÉO INTERDIT: {model} nécessite une clé API!")
            model = "local_ffmpeg"  # Forcer le local

        generation_id = self._create_generation_id()
        
        # Marquer comme en cours
        self.active_generations[generation_id] = {
            "type": "video",
            "prompt": prompt,
            "status": "processing",
            "start_time": time.time()
        }
        
        # Lancer la génération en arrière-plan
        thread = threading.Thread(
            target=self._generer_video_worker,
            args=(prompt, duree, fps, resolution, model, generation_id),
            daemon=True
        )
        thread.start()
        
        return {
            "generation_id": generation_id,
            "status": "started",
            "estimated_time": f"{duree * 10} secondes"
        }
    
    def _generer_video_worker(self, prompt, duree, fps, resolution, model, generation_id):
        """Worker pour génération vidéo en arrière-plan"""
        
        try:
            if model == "ltx_video":
                print("🎬 Génération avec LTX Vidéo")
                result = self._generer_video_ltx(prompt, duree, fps, resolution, generation_id)
            elif model == "stable_video":
                print("🎬 Génération avec Stable Video")
                result = self._generer_video_stable_video(prompt, duree, fps, resolution, generation_id)
            elif model == "runway":
                result = self._generer_video_runway(prompt, duree, fps, resolution, generation_id)
            else:
                print("🎬 Génération avec FFmpeg local")
                result = self._generer_video_local(prompt, duree, fps, resolution, generation_id)
            
            # Mettre à jour le statut
            if generation_id in self.active_generations:
                del self.active_generations[generation_id]
            
            if result:
                print(f"✅ Vidéo générée: {result['filename']}")
            else:
                print(f"❌ Échec génération vidéo: {generation_id}")
                
        except Exception as e:
            print(f"❌ Erreur worker vidéo: {e}")
            if generation_id in self.active_generations:
                del self.active_generations[generation_id]
    
    def _generer_video_local(self, prompt, duree, fps, resolution, generation_id):
        """Génération vidéo locale avec FFmpeg"""
        
        try:
            # Créer une vidéo simple avec FFmpeg
            filename = f"video_{generation_id}_{int(time.time())}.mp4"
            filepath = os.path.join(self.output_dir, filename)
            
            # Commande FFmpeg pour créer une vidéo de test
            width, height = resolution.split('x')
            cmd = [
                'ffmpeg', '-f', 'lavfi',
                '-i', f'testsrc=duration={duree}:size={resolution}:rate={fps}',
                '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
                filepath, '-y'
            ]
            
            # Exécuter FFmpeg
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                generation_info = {
                    "id": generation_id,
                    "type": "video",
                    "model": "ffmpeg_local",
                    "prompt": prompt,
                    "duree": duree,
                    "fps": fps,
                    "resolution": resolution,
                    "filepath": filepath,
                    "filename": filename,
                    "timestamp": datetime.now().isoformat(),
                    "status": "completed"
                }
                
                self.generation_history.append(generation_info)
                return generation_info
            else:
                print(f"❌ Erreur FFmpeg: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération vidéo locale: {e}")
            return None

    def _generer_video_ltx(self, prompt, duree, fps, resolution, generation_id):
        """🚀 GÉNÉRATION VIDÉO AVEC LTX VIDÉO - JEAN-LUC PASSAVE"""

        try:
            # 🧠 ANALYSER LE PROMPT AVEC AGENT COGNITIF
            prompt_analyse = self._analyser_prompt_cognitif(prompt, "video")

            # Créer une vidéo avec LTX Video (simulation pour l'instant)
            filename = f"ltx_video_{generation_id}_{int(time.time())}.mp4"
            filepath = os.path.join(self.output_dir, filename)

            print(f"🎬 Génération LTX Vidéo: {prompt[:50]}...")
            print(f"🧠 Analyse cognitive: {prompt_analyse[:100]}...")

            # 🚀 APPEL API LTX VIDÉO LOCAL
            try:
                ltx_payload = {
                    "prompt": prompt_analyse,
                    "duration": duree,
                    "fps": fps,
                    "resolution": resolution,
                    "model": "ltx-video",
                    "quality": "high"
                }

                response = requests.post(
                    self.models_config["video"]["ltx_video"] + "/generate",
                    json=ltx_payload,
                    timeout=300  # 5 minutes pour LTX
                )

                if response.status_code == 200:
                    result_data = response.json()
                    # Sauvegarder la vidéo générée
                    if "video_data" in result_data:
                        import base64
                        video_data = base64.b64decode(result_data["video_data"])
                        with open(filepath, "wb") as f:
                            f.write(video_data)

                    generation_info = {
                        "id": generation_id,
                        "type": "video",
                        "model": "ltx_video",
                        "prompt": prompt,
                        "prompt_analyse": prompt_analyse,
                        "duree": duree,
                        "fps": fps,
                        "resolution": resolution,
                        "filepath": filepath,
                        "filename": filename,
                        "timestamp": datetime.now().isoformat(),
                        "status": "completed",
                        "cognitive_enhanced": True
                    }

                    self.generation_history.append(generation_info)

                    # 🧠 INDEXER DANS LA MÉMOIRE THERMIQUE COGNITIVE - JEAN-LUC PASSAVE
                    self._indexer_video_cognitive(generation_info)

                    print(f"✅ Vidéo LTX générée et indexée: {filename}")
                    return generation_info
                else:
                    print(f"❌ Erreur LTX API: {response.status_code}")
                    # Fallback vers FFmpeg
                    return self._generer_video_local(prompt, duree, fps, resolution, generation_id)

            except requests.exceptions.ConnectionError:
                print("⚠️ LTX Vidéo non disponible, fallback vers FFmpeg")
                return self._generer_video_local(prompt, duree, fps, resolution, generation_id)

        except Exception as e:
            print(f"❌ Erreur génération LTX: {e}")
            return self._generer_video_local(prompt, duree, fps, resolution, generation_id)

    def generer_musique(self, prompt, duree=30, style="electronic", model="musicgen"):
        """🎵 Génère de la musique avec IA - 100% LOCAL SEULEMENT - JEAN-LUC PASSAVE"""

        print(f"🎵 Génération musique 100% locale: {prompt[:50]}...")

        # 🚨 VÉRIFICATION SÉCURITÉ - AUCUNE CLÉ API AUTORISÉE
        if model in ["suno"] or "api_key" in str(model).lower():
            print(f"🚨 MODÈLE MUSIQUE INTERDIT: {model} nécessite une clé API!")
            model = "local_synthesis"  # Forcer le local

        generation_id = self._create_generation_id()

        # Configuration selon le modèle LOCAL UNIQUEMENT
        if model == "musicgen":
            print("🎵 Utilisation MusicGen local")
            return self._generer_musique_musicgen(prompt, duree, style, generation_id)
        elif model == "audiocraft":
            print("🎵 Utilisation AudioCraft local")
            return self._generer_musique_local(prompt, duree, style, generation_id)
        else:
            print("🎵 Utilisation synthèse locale FFmpeg")
            return self._generer_musique_local(prompt, duree, style, generation_id)
    
    def _generer_musique_local(self, prompt, duree, style, generation_id):
        """Génération musique locale"""
        
        try:
            # Créer un fichier audio simple
            filename = f"music_{generation_id}_{int(time.time())}.wav"
            filepath = os.path.join(self.output_dir, filename)
            
            # Générer un son simple avec FFmpeg
            cmd = [
                'ffmpeg', '-f', 'lavfi',
                '-i', f'sine=frequency=440:duration={duree}',
                '-c:a', 'pcm_s16le',
                filepath, '-y'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                generation_info = {
                    "id": generation_id,
                    "type": "music",
                    "model": "ffmpeg_local",
                    "prompt": prompt,
                    "duree": duree,
                    "style": style,
                    "filepath": filepath,
                    "filename": filename,
                    "timestamp": datetime.now().isoformat(),
                    "status": "completed"
                }
                
                self.generation_history.append(generation_info)
                print(f"✅ Musique générée: {filename}")
                return generation_info
            else:
                print(f"❌ Erreur génération musique: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur génération musique locale: {e}")
            return None
    
    def _enhance_prompt_image(self, prompt, style):
        """Améliore le prompt selon le style"""
        
        style_enhancers = {
            "realistic": "photorealistic, high quality, detailed, 8k resolution",
            "artistic": "artistic, creative, beautiful composition, masterpiece",
            "anime": "anime style, manga, japanese art, vibrant colors",
            "cyberpunk": "cyberpunk, neon lights, futuristic, dark atmosphere",
            "fantasy": "fantasy art, magical, ethereal, mystical atmosphere"
        }
        
        enhancer = style_enhancers.get(style, "high quality, detailed")
        return f"{prompt}, {enhancer}"
    
    def _analyser_prompt_cognitif(self, prompt, media_type="video"):
        """🧠 ANALYSE COGNITIVE DU PROMPT AVEC DUAL AGENTS - JEAN-LUC PASSAVE"""

        try:
            # Utiliser les dual agents pour analyser et optimiser le prompt
            cognitive_endpoint = self.models_config["cognitive"]["multimodal_analysis"]

            # Prompt d'analyse cognitive selon le type de média
            if media_type == "video":
                system_prompt = """Tu es un expert en analyse cognitive multimodale.
                Analyse ce prompt vidéo et optimise-le pour une meilleure génération.
                Ajoute des détails visuels, de mouvement, d'éclairage et de composition.
                Réponds uniquement avec le prompt optimisé."""
            elif media_type == "image":
                system_prompt = """Tu es un expert en analyse cognitive visuelle.
                Analyse ce prompt image et optimise-le pour une meilleure génération.
                Ajoute des détails de composition, couleurs, style et qualité.
                Réponds uniquement avec le prompt optimisé."""
            else:
                system_prompt = """Tu es un expert en analyse cognitive multimodale.
                Analyse et optimise ce prompt pour une meilleure génération créative.
                Réponds uniquement avec le prompt optimisé."""

            payload = {
                "model": "deepseek-r1",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Optimise ce prompt {media_type}: {prompt}"}
                ],
                "stream": False,
                "temperature": 0.7,
                "max_tokens": 300
            }

            response = requests.post(cognitive_endpoint, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                prompt_optimise = result["choices"][0]["message"]["content"]

                # Extraire la vraie réponse (sans <think>)
                if "</think>" in prompt_optimise:
                    prompt_optimise = prompt_optimise.split("</think>")[-1].strip()

                print(f"🧠 Prompt optimisé par agent cognitif: {prompt_optimise[:100]}...")
                return prompt_optimise
            else:
                print(f"⚠️ Agent cognitif non disponible: {response.status_code}")
                return prompt

        except Exception as e:
            print(f"⚠️ Erreur analyse cognitive: {e}")
            return prompt

    def _indexer_video_cognitive(self, generation_info):
        """🧠 INDEXER VIDÉO DANS LA MÉMOIRE THERMIQUE COGNITIVE - JEAN-LUC PASSAVE"""

        try:
            from jarvis_thermal_memory_manager import thermal_manager

            # Créer l'index vidéo cognitif
            video_index = {
                "id": generation_info["id"],
                "filename": generation_info["filename"],
                "filepath": generation_info["filepath"],
                "prompt_original": generation_info["prompt"],
                "prompt_analyse": generation_info.get("prompt_analyse", ""),
                "model": generation_info["model"],
                "duree": generation_info["duree"],
                "resolution": generation_info["resolution"],
                "timestamp": generation_info["timestamp"],
                "cognitive_enhanced": generation_info.get("cognitive_enhanced", False),

                # 🧠 ANALYSE COGNITIVE AUTOMATIQUE
                "tags": self._extraire_tags_cognitifs(generation_info["prompt"]),
                "resume": self._generer_resume_cognitif(generation_info["prompt"]),
                "concepts": self._extraire_concepts_cognitifs(generation_info["prompt"]),
                "thermal_score": self._calculer_score_thermal_video(generation_info)
            }

            # Ajouter à la mémoire thermique
            if not hasattr(thermal_manager.interactions_history, 'videos'):
                thermal_manager.interactions_history['videos'] = {}

            thermal_manager.interactions_history['videos'][generation_info["id"]] = video_index

            # Créer associations multimodales
            self._creer_associations_multimodales(video_index)

            print(f"🧠 Vidéo indexée dans mémoire thermique: {generation_info['id']}")

        except Exception as e:
            print(f"⚠️ Erreur indexation cognitive: {e}")

    def _extraire_tags_cognitifs(self, prompt):
        """Extrait des tags cognitifs du prompt"""
        # Tags basiques par analyse de mots-clés
        tags = []
        mots_cles = {
            "robot": ["robotique", "automatisation", "technologie"],
            "danse": ["mouvement", "art", "performance"],
            "futur": ["science-fiction", "innovation", "prospective"],
            "espace": ["cosmos", "exploration", "astronomie"],
            "nature": ["environnement", "biologie", "écologie"]
        }

        prompt_lower = prompt.lower()
        for mot, tags_associes in mots_cles.items():
            if mot in prompt_lower:
                tags.extend(tags_associes)

        return list(set(tags))  # Supprimer doublons

    def _generer_resume_cognitif(self, prompt):
        """Génère un résumé cognitif du prompt"""
        # Résumé intelligent basé sur le contenu
        if len(prompt) <= 50:
            return prompt
        else:
            # Extraire les concepts principaux
            mots = prompt.split()
            concepts_importants = [mot for mot in mots if len(mot) > 4][:5]
            return f"Vidéo sur: {', '.join(concepts_importants)}"

    def _extraire_concepts_cognitifs(self, prompt):
        """Extrait les concepts cognitifs principaux"""
        concepts = {
            "action": ["danse", "mouvement", "course", "vol"],
            "lieu": ["espace", "jardin", "ville", "maison"],
            "objet": ["robot", "voiture", "animal", "plante"],
            "style": ["futuriste", "moderne", "classique", "artistique"]
        }

        prompt_lower = prompt.lower()
        concepts_detectes = {}

        for categorie, mots in concepts.items():
            for mot in mots:
                if mot in prompt_lower:
                    if categorie not in concepts_detectes:
                        concepts_detectes[categorie] = []
                    concepts_detectes[categorie].append(mot)

        return concepts_detectes

    def _calculer_score_thermal_video(self, generation_info):
        """Calcule un score thermal pour la vidéo"""
        score = 0.0

        # Score basé sur la complexité du prompt
        prompt_length = len(generation_info["prompt"])
        if prompt_length > 20:
            score += 0.3

        # Score basé sur l'amélioration cognitive
        if generation_info.get("cognitive_enhanced", False):
            score += 0.4

        # Score basé sur la durée
        duree = generation_info.get("duree", 0)
        if duree >= 5:
            score += 0.2

        # Score basé sur le modèle utilisé
        if generation_info.get("model") == "ltx_video":
            score += 0.1

        return min(score, 1.0)

    def _creer_associations_multimodales(self, video_index):
        """Crée des associations multimodales pour la vidéo"""
        try:
            from jarvis_thermal_memory_manager import thermal_manager

            if not hasattr(thermal_manager.interactions_history, 'multimodal_associations'):
                thermal_manager.interactions_history['multimodal_associations'] = {}

            # Associer avec les interactions récentes
            recent_interactions = thermal_manager.get_recent_interactions(hours=1)

            associations = {
                "video_id": video_index["id"],
                "related_interactions": [i["timestamp"] for i in recent_interactions[-3:]],
                "concepts_lies": video_index["concepts"],
                "tags_lies": video_index["tags"],
                "score_association": video_index["thermal_score"]
            }

            thermal_manager.interactions_history['multimodal_associations'][video_index["id"]] = associations

        except Exception as e:
            print(f"⚠️ Erreur associations multimodales: {e}")

    def _create_generation_id(self):
        """Crée un ID unique pour la génération"""
        timestamp = str(int(time.time() * 1000))
        hash_obj = hashlib.md5(timestamp.encode())
        return hash_obj.hexdigest()[:8]
    
    def _generate_real_image_with_agents(self, filepath, prompt):
        """🚀 GÉNÈRE UNE VRAIE IMAGE AVEC DUAL AGENTS DEEPSEEK R1 8B"""
        try:
            # 🚀 UTILISER AGENT 2 POUR AMÉLIORER LE PROMPT
            from jarvis_dual_agents_electron import agent2_turbo_accelerateur

            # Agent 2 améliore le prompt pour la génération d'image
            prompt_ameliore = agent2_turbo_accelerateur(
                f"Améliore ce prompt pour génération d'image: {prompt}",
                "acceleration"
            )

            # 🎨 GÉNÉRATION RÉELLE D'IMAGE (si Stable Diffusion disponible)
            if DIFFUSERS_AVAILABLE and StableDiffusionPipeline and torch:
                try:
                    # Charger le modèle Stable Diffusion
                    pipe = StableDiffusionPipeline.from_pretrained(
                        "runwayml/stable-diffusion-v1-5",
                        torch_dtype=torch.float16
                    )

                    # Générer l'image
                    image = pipe(prompt_ameliore).images[0]
                    image.save(filepath)

                    print(f"✅ Image générée avec Stable Diffusion: {filepath}")

                except Exception as e:
                    print(f"⚠️ Erreur Stable Diffusion: {e}")
                    # Fallback vers PIL
                    self._generer_image_pil_fallback(prompt_ameliore, filepath)
            else:
                # 🎨 GÉNÉRATION AVEC PIL AMÉLIORÉE
                self._generer_image_pil_fallback(prompt_ameliore, filepath)

        except Exception as e:
            print(f"❌ Erreur génération image: {e}")
            # Créer un fichier de log au lieu d'un placeholder
            with open(filepath.replace('.png', '_log.txt'), 'w') as f:
                f.write(f"🚀 Génération image JARVIS\nPrompt: {prompt}\nTimestamp: {datetime.now()}\nErreur: {e}")
    
    def get_status_generation(self, generation_id):
        """Obtient le statut d'une génération"""
        
        # Vérifier les générations actives
        if generation_id in self.active_generations:
            gen = self.active_generations[generation_id]
            elapsed = time.time() - gen['start_time']
            return {
                "status": "processing",
                "elapsed_time": f"{elapsed:.1f}s",
                "type": gen['type'],
                "prompt": gen['prompt']
            }
        
        # Vérifier l'historique
        for gen in self.generation_history:
            if gen['id'] == generation_id:
                return {
                    "status": gen['status'],
                    "type": gen['type'],
                    "filename": gen.get('filename'),
                    "filepath": gen.get('filepath'),
                    "timestamp": gen['timestamp']
                }
        
        return {"status": "not_found"}
    
    def get_historique_generations(self, limit=10):
        """Retourne l'historique des générations"""
        return self.generation_history[-limit:]
    
    def get_stats_multimedia(self):
        """Statistiques du générateur multimédia"""
        
        stats = {
            "total_generations": len(self.generation_history),
            "active_generations": len(self.active_generations),
            "types_generated": {},
            "models_used": {},
            "output_directory": self.output_dir,
            "disk_usage": self._calculate_disk_usage()
        }
        
        # Analyser les types et modèles
        for gen in self.generation_history:
            gen_type = gen.get('type', 'unknown')
            model = gen.get('model', 'unknown')
            
            stats['types_generated'][gen_type] = stats['types_generated'].get(gen_type, 0) + 1
            stats['models_used'][model] = stats['models_used'].get(model, 0) + 1
        
        return stats
    
    def _calculate_disk_usage(self):
        """Calcule l'usage disque du dossier de sortie"""
        try:
            total_size = 0
            for dirpath, _, filenames in os.walk(self.output_dir):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
            
            # Convertir en MB
            return f"{total_size / (1024*1024):.2f} MB"
        except:
            return "Inconnu"

    def rechercher_videos_cognitives(self, requete, limit=5):
        """🔍 RECHERCHE COGNITIVE DANS LES VIDÉOS - JEAN-LUC PASSAVE"""

        try:
            from jarvis_thermal_memory_manager import thermal_manager

            if not hasattr(thermal_manager.interactions_history, 'videos'):
                return []

            videos = thermal_manager.interactions_history['videos']
            resultats = []

            requete_lower = requete.lower()

            for video_id, video_data in videos.items():
                score_pertinence = 0.0

                # Recherche dans le prompt original
                if requete_lower in video_data.get('prompt_original', '').lower():
                    score_pertinence += 0.4

                # Recherche dans les tags
                for tag in video_data.get('tags', []):
                    if requete_lower in tag.lower():
                        score_pertinence += 0.2

                # Recherche dans le résumé
                if requete_lower in video_data.get('resume', '').lower():
                    score_pertinence += 0.3

                if score_pertinence > 0:
                    resultats.append({
                        "video_id": video_id,
                        "score": score_pertinence,
                        "data": video_data
                    })

            # Trier par score de pertinence
            resultats.sort(key=lambda x: x['score'], reverse=True)

            return resultats[:limit]

        except Exception as e:
            print(f"❌ Erreur recherche cognitive: {e}")
            return []

    def get_videos_cognitives_stats(self):
        """📊 Statistiques des vidéos cognitives"""

        try:
            from jarvis_thermal_memory_manager import thermal_manager

            if not hasattr(thermal_manager.interactions_history, 'videos'):
                return {"total_videos": 0, "cognitive_enhanced": 0}

            videos = thermal_manager.interactions_history['videos']

            total_videos = len(videos)
            cognitive_enhanced = sum(1 for v in videos.values() if v.get('cognitive_enhanced', False))

            # Calculer score thermal moyen
            if total_videos > 0:
                avg_thermal = sum(v.get('thermal_score', 0) for v in videos.values()) / total_videos
            else:
                avg_thermal = 0.0

            return {
                "total_videos": total_videos,
                "cognitive_enhanced": cognitive_enhanced,
                "average_thermal_score": avg_thermal
            }

        except Exception as e:
            print(f"❌ Erreur stats cognitives: {e}")
            return {"total_videos": 0, "cognitive_enhanced": 0}

# 🔍 FONCTIONS DE RECHERCHE COGNITIVE GLOBALES - JEAN-LUC PASSAVE

def rechercher_videos_cognitives(requete, limit=5):
    """🔍 RECHERCHE COGNITIVE DANS LES VIDÉOS"""
    return generateur_multimedia.rechercher_videos_cognitives(requete, limit)

def get_videos_cognitives_stats():
    """📊 Statistiques des vidéos cognitives"""
    return generateur_multimedia.get_videos_cognitives_stats()

# Instance globale
generateur_multimedia = GenerateurMultimediaJARVIS()

def get_generateur_multimedia():
    """Retourne l'instance globale du générateur multimédia"""
    return generateur_multimedia
