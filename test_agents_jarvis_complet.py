#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Agents JARVIS Complet
Jean-Luc Passave - 2025
Test de tous les agents JARVIS pour validation complète
"""

import gradio as gr
import requests
import json
import time
from datetime import datetime

def test_agent_dialogue():
    """Test de l'agent de dialogue principal"""
    try:
        response = requests.post(
            'http://localhost:7866/api/chat',
            json={
                'message': 'Bonjour JARVIS, peux-tu me parler de tes capacités actuelles ?',
                'user': '<PERSON><PERSON><PERSON> Passave',
                'timestamp': datetime.now().isoformat()
            },
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            return f"✅ Agent Dialogue: {data.get('response', 'Réponse reçue')}"
        else:
            return f"⚠️ Agent Dialogue: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Agent Dialogue: {str(e)}"

def test_agent_multi():
    """Test du système multi-agents"""
    try:
        response = requests.post(
            'http://localhost:7880/api/multiagent',
            json={
                'message': 'Déclenche une analyse multi-agents de mes projets',
                'user': '<PERSON>-<PERSON> Passave'
            },
            timeout=15
        )
        
        if response.status_code == 200:
            return "✅ Système Multi-Agents: Opérationnel"
        else:
            return f"⚠️ Système Multi-Agents: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Système Multi-Agents: {str(e)}"

def test_agent_outils():
    """Test de l'agent avec outils"""
    try:
        # Test d'accès aux outils
        response = requests.get(
            'http://localhost:7880',
            timeout=5
        )
        
        if response.status_code == 200:
            return "✅ Agent Outils: Interface accessible"
        else:
            return f"⚠️ Agent Outils: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Agent Outils: {str(e)}"

def test_agent_deepseek():
    """Test de l'agent DeepSeek R1"""
    try:
        response = requests.post(
            'http://localhost:8000/v1/chat/completions',
            json={
                'model': 'deepseek-r1',
                'messages': [
                    {
                        'role': 'user',
                        'content': 'Test de connexion DeepSeek R1 pour Jean-Luc Passave'
                    }
                ],
                'max_tokens': 100
            },
            timeout=10
        )
        
        if response.status_code == 200:
            return "✅ Agent DeepSeek R1: Connecté"
        else:
            return f"⚠️ Agent DeepSeek R1: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Agent DeepSeek R1: {str(e)}"

def test_memoire_thermique():
    """Test de la mémoire thermique"""
    try:
        response = requests.post(
            'http://localhost:7866/api/memory',
            json={
                'action': 'test',
                'user': 'Jean-Luc Passave'
            },
            timeout=5
        )
        
        if response.status_code == 200:
            return "✅ Mémoire Thermique: Active"
        else:
            return f"⚠️ Mémoire Thermique: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Mémoire Thermique: {str(e)}"

def test_optimisations_m4():
    """Test des optimisations Apple Silicon M4"""
    import platform
    import os
    
    results = []
    
    # Architecture
    arch = platform.machine()
    if arch == 'arm64':
        results.append("✅ Architecture: ARM64 (Apple Silicon)")
    else:
        results.append(f"⚠️ Architecture: {arch}")
    
    # Processeur
    try:
        import psutil
        cpu_count = psutil.cpu_count()
        results.append(f"✅ CPU Cores: {cpu_count}")
        
        memory = psutil.virtual_memory()
        memory_gb = round(memory.total / (1024**3))
        results.append(f"✅ RAM: {memory_gb} GB")
        
    except ImportError:
        results.append("⚠️ psutil non disponible")
    
    # Variables d'environnement M4
    if os.environ.get('APPLE_SILICON_OPTIMIZED') == '1':
        results.append("✅ Optimisations M4: Activées")
    else:
        results.append("⚠️ Optimisations M4: Non définies")
    
    return "\n".join(results)

def run_complete_test():
    """Lance tous les tests"""
    results = []
    
    print("🧪 DÉMARRAGE TESTS COMPLETS JARVIS")
    print("=" * 40)
    
    # Test 1: Agent Dialogue
    print("🔍 Test Agent Dialogue...")
    result1 = test_agent_dialogue()
    results.append(result1)
    print(result1)
    
    time.sleep(1)
    
    # Test 2: Système Multi-Agents
    print("🔍 Test Système Multi-Agents...")
    result2 = test_agent_multi()
    results.append(result2)
    print(result2)
    
    time.sleep(1)
    
    # Test 3: Agent Outils
    print("🔍 Test Agent Outils...")
    result3 = test_agent_outils()
    results.append(result3)
    print(result3)
    
    time.sleep(1)
    
    # Test 4: Agent DeepSeek
    print("🔍 Test Agent DeepSeek R1...")
    result4 = test_agent_deepseek()
    results.append(result4)
    print(result4)
    
    time.sleep(1)
    
    # Test 5: Mémoire Thermique
    print("🔍 Test Mémoire Thermique...")
    result5 = test_memoire_thermique()
    results.append(result5)
    print(result5)
    
    time.sleep(1)
    
    # Test 6: Optimisations M4
    print("🔍 Test Optimisations M4...")
    result6 = test_optimisations_m4()
    results.append(result6)
    print(result6)
    
    # Résumé
    success_count = sum(1 for r in results if r.startswith("✅"))
    total_tests = len(results)
    
    print("\n" + "=" * 40)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 40)
    print(f"✅ Tests réussis: {success_count}/{total_tests}")
    print(f"📈 Taux de réussite: {(success_count/total_tests)*100:.1f}%")
    
    if success_count >= 4:
        print("\n🎉 EXCELLENT ! Système JARVIS opérationnel")
    elif success_count >= 2:
        print("\n👍 BON ! Quelques services à démarrer")
    else:
        print("\n⚠️ ATTENTION ! Plusieurs services non accessibles")
    
    return "\n".join(results)

def create_test_interface():
    """Interface de test des agents"""
    
    with gr.Blocks(
        title="🧪 Test Agents JARVIS Complet",
        theme=gr.themes.Soft()
    ) as test_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #1e3c72, #2a5298); color: white; padding: 25px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.2em;">🧪 TEST AGENTS JARVIS COMPLET</h1>
            <p style="margin: 10px 0; font-size: 1.1em;">Validation de tous les agents et systèmes</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 1em;">👤 Jean-Luc Passave | 🤖 4 Agents | 🧠 Mémoire Thermique | 🍎 M4 Optimisé</p>
            </div>
        </div>
        """)

        with gr.Row():
            with gr.Column():
                gr.HTML("""
                <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h3>🎯 Tests Disponibles:</h3>
                    <ul style='text-align: left; margin: 10px 0; line-height: 1.8;'>
                        <li><strong>💬 Agent Dialogue</strong> - Communication principale</li>
                        <li><strong>🤖 Système Multi-Agents</strong> - Coordination agents</li>
                        <li><strong>🔧 Agent Outils</strong> - Accès aux outils</li>
                        <li><strong>🧠 Agent DeepSeek R1</strong> - IA avancée</li>
                        <li><strong>💾 Mémoire Thermique</strong> - Stockage persistant</li>
                        <li><strong>🍎 Optimisations M4</strong> - Apple Silicon</li>
                    </ul>
                </div>
                """)
                
                test_all_btn = gr.Button(
                    "🚀 LANCER TOUS LES TESTS",
                    variant="primary",
                    size="lg"
                )
            
            with gr.Column():
                gr.HTML("""
                <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h3>📋 Instructions:</h3>
                    <ol style='text-align: left; margin: 10px 0; line-height: 1.8;'>
                        <li>Assurez-vous que JARVIS fonctionne</li>
                        <li>Cliquez sur "LANCER TOUS LES TESTS"</li>
                        <li>Attendez les résultats de validation</li>
                        <li>Vérifiez que tous les agents sont ✅</li>
                    </ol>
                </div>
                """)

        # Résultats
        results_output = gr.Textbox(
            label="Résultats des Tests",
            lines=15,
            interactive=False
        )

        # Tests individuels
        gr.HTML("<hr style='margin: 30px 0;'>")
        gr.HTML("<h3 style='text-align: center;'>🔍 Tests Individuels</h3>")

        with gr.Row():
            test_dialogue_btn = gr.Button("💬 Test Agent Dialogue")
            test_multi_btn = gr.Button("🤖 Test Multi-Agents")
            test_outils_btn = gr.Button("🔧 Test Agent Outils")

        with gr.Row():
            test_deepseek_btn = gr.Button("🧠 Test DeepSeek R1")
            test_memory_btn = gr.Button("💾 Test Mémoire")
            test_m4_btn = gr.Button("🍎 Test M4")

        # Connexions
        test_all_btn.click(
            fn=run_complete_test,
            outputs=[results_output]
        )
        
        test_dialogue_btn.click(
            fn=test_agent_dialogue,
            outputs=[results_output]
        )
        
        test_multi_btn.click(
            fn=test_agent_multi,
            outputs=[results_output]
        )
        
        test_outils_btn.click(
            fn=test_agent_outils,
            outputs=[results_output]
        )
        
        test_deepseek_btn.click(
            fn=test_agent_deepseek,
            outputs=[results_output]
        )
        
        test_memory_btn.click(
            fn=test_memoire_thermique,
            outputs=[results_output]
        )
        
        test_m4_btn.click(
            fn=test_optimisations_m4,
            outputs=[results_output]
        )

    return test_interface

if __name__ == "__main__":
    print("🧪 DÉMARRAGE INTERFACE TEST AGENTS JARVIS")
    print("========================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Validation complète de tous les agents")
    print("")
    
    # Créer et lancer l'interface
    test_app = create_test_interface()
    
    print("✅ Interface de test créée")
    print("🌐 Lancement sur http://localhost:7893")
    
    test_app.launch(
        server_name="127.0.0.1",
        server_port=7893,
        share=False,
        show_error=True,
        quiet=False
    )
