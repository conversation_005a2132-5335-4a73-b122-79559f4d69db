#!/usr/bin/env python3
"""
TEST TOKENS ADAPTATIFS THERMIQUES - JEAN-LUC PASSAVE
"""

import json
import os

MEMORY_FILE = "thermal_memory_persistent.json"

def calculate_thermal_level():
    """CALCUL DU NIVEAU THERMIQUE POUR TOKENS ADAPTATIFS"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return 0.3
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            memory = json.load(f)
        
        # Calculer le niveau thermique basé sur la mémoire
        neuron_count = len(memory.get('neuron_memories', []))
        memory_size = len(json.dumps(memory)) / (1024 * 1024)  # MB
        
        # Niveau thermique entre 0.1 et 1.0
        thermal_level = min(1.0, (neuron_count / 100) + (memory_size / 10))
        return max(0.1, thermal_level)
        
    except Exception as e:
        print(f"❌ Erreur calcul thermique: {e}")
        return 0.3

def get_adaptive_temperature():
    """TEMPÉRATURE ADAPTATIVE BASÉE SUR LE NIVEAU THERMIQUE"""
    try:
        thermal_level = calculate_thermal_level()
        adaptive_temp = 0.2 + (thermal_level * 0.8)
        return max(0.1, min(1.0, adaptive_temp))
    except Exception as e:
        return 0.7  # Valeur par défaut

def get_adaptive_max_tokens():
    """NOMBRE DE TOKENS ADAPTATIF BASÉ SUR LE NIVEAU THERMIQUE"""
    try:
        thermal_level = calculate_thermal_level()
        adaptive_tokens = int(150 + (thermal_level * 650))
        return max(100, min(1000, adaptive_tokens))
    except Exception as e:
        return 400  # Valeur par défaut

def test_tokens_adaptatifs():
    """TEST COMPLET DES TOKENS ADAPTATIFS"""
    print('🔥 TEST TOKENS ADAPTATIFS THERMIQUES')
    print('=' * 50)
    
    try:
        # Test des fonctions
        thermal_level = calculate_thermal_level()
        adaptive_temp = get_adaptive_temperature()
        adaptive_tokens = get_adaptive_max_tokens()
        
        print(f'✅ Niveau thermique: {thermal_level:.3f}')
        print(f'✅ Température adaptative: {adaptive_temp:.3f}')
        print(f'✅ Tokens adaptatifs: {adaptive_tokens}')
        
        # Calculer les stats de la mémoire thermique
        with open(MEMORY_FILE, 'r') as f:
            memory = json.load(f)
        
        neuron_count = len(memory.get('neuron_memories', []))
        memory_size = len(json.dumps(memory)) / (1024 * 1024)
        
        print(f'\n📊 STATS MÉMOIRE THERMIQUE:')
        print(f'   • Neurones: {neuron_count}')
        print(f'   • Taille: {memory_size:.2f} MB')
        print(f'   • Niveau thermique calculé: {thermal_level:.3f}')
        
        print(f'\n🎯 TOKENS ADAPTATIFS ACTIVÉS:')
        print(f'   • Base: 150 tokens')
        print(f'   • Bonus thermique: {adaptive_tokens - 150} tokens')
        print(f'   • Total: {adaptive_tokens} tokens')
        
        print(f'\n🌡️ TEMPÉRATURE ADAPTATIVE:')
        print(f'   • Base: 0.2')
        print(f'   • Bonus thermique: {adaptive_temp - 0.2:.3f}')
        print(f'   • Total: {adaptive_temp:.3f}')
        
        print(f'\n✅ TOKENS ADAPTATIFS THERMIQUES: FONCTIONNELS')
        
        # Test avec différents niveaux
        print(f'\n🧪 SIMULATION DIFFÉRENTS NIVEAUX:')
        for test_level in [0.1, 0.3, 0.5, 0.7, 1.0]:
            test_temp = 0.2 + (test_level * 0.8)
            test_tokens = int(150 + (test_level * 650))
            print(f'   Niveau {test_level:.1f}: {test_tokens} tokens, temp {test_temp:.2f}')
        
        return True
        
    except Exception as e:
        print(f'❌ Erreur: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tokens_adaptatifs()
