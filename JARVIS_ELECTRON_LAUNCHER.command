#!/bin/bash

# 🚀 LANCEUR ELECTRON JARVIS - JEAN-LUC PASSAVE
# Double-clic pour démarrer l'application

echo "🤖 JARVIS ELECTRON LAUNCHER"
echo "=========================="

# Aller dans le bon répertoire
cd "$(dirname "$0")"

echo "📁 Répertoire: $(pwd)"

# Vérifier les fichiers
if [ -f "jarvis_electron_final_complet.js" ]; then
    echo "✅ Fichier principal trouvé"
else
    echo "❌ Fichier principal manquant"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Vérifier Electron
if [ -f "node_modules/.bin/electron" ]; then
    echo "✅ Electron installé"
else
    echo "🔧 Installation d'Electron..."
    npm install
fi

echo ""
echo "🚀 DÉMARRAGE JARVIS ELECTRON..."
echo "🎯 Interface complète avec LTX Vidéo"
echo "🧠 Dual Agents + Mémoire Thermique"
echo ""

# Démarrer l'application
./node_modules/.bin/electron jarvis_electron_final_complet.js

echo ""
echo "🔄 Application fermée"
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
