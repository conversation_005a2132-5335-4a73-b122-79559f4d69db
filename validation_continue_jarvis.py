#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Validation Continue JARVIS
Jean-Luc <PERSON> - 2025
Validation continue du système SANS JAMAIS modifier le code
"""

import gradio as gr
import requests
import os
import json
import time
from datetime import datetime
import threading

# État de validation continue
validation_state = {
    'active': False,
    'tests_passed': 0,
    'tests_total': 0,
    'last_validation': None,
    'continuous_running': False
}

def validate_electron_app():
    """Valide l'application Electron SANS LA MODIFIER"""
    try:
        # Vérifier que le fichier principal existe
        if not os.path.exists('jarvis_electron_final_complet.js'):
            return {'status': 'error', 'message': 'Fichier Electron manquant'}
        
        # Vérifier package.json
        if not os.path.exists('package.json'):
            return {'status': 'error', 'message': 'package.json manquant'}
        
        # Lire package.json pour vérifier la structure
        with open('package.json', 'r', encoding='utf-8') as f:
            package_data = json.load(f)
        
        # Vérifications de base
        has_main = 'main' in package_data
        has_scripts = 'scripts' in package_data
        has_final_script = 'final' in package_data.get('scripts', {})
        
        if has_main and has_scripts and has_final_script:
            return {
                'status': 'success',
                'message': 'Application Electron validée',
                'details': f"Main: {package_data.get('main', 'N/A')}"
            }
        else:
            return {
                'status': 'warning',
                'message': 'Structure Electron incomplète',
                'details': f"Main: {has_main}, Scripts: {has_scripts}, Final: {has_final_script}"
            }
            
    except Exception as e:
        return {'status': 'error', 'message': f'Erreur validation Electron: {str(e)}'}

def validate_python_interfaces():
    """Valide les interfaces Python SANS LES MODIFIER"""
    
    interfaces = [
        'dashboard_avec_onglets.py',
        'visualisation_memoire_thermique.py',
        'systeme_notifications_jarvis.py',
        'tableau_bord_final_ultime.py',
        'sauvegarde_automatique_jarvis.py',
        'centre_commande_unifie_jarvis.py',
        'diagnostic_agents_memoire_complet.py'
    ]
    
    results = {}
    
    for interface in interfaces:
        try:
            if os.path.exists(interface):
                # Vérifier la taille du fichier
                file_size = os.path.getsize(interface)
                
                # Lire les premières lignes pour vérifier la structure
                with open(interface, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    second_line = f.readline().strip()
                
                # Vérifications de base
                is_python = first_line.startswith('#!') and 'python' in first_line
                has_encoding = 'utf-8' in second_line
                is_valid_size = file_size > 1000  # Au moins 1KB
                
                if is_python and has_encoding and is_valid_size:
                    results[interface] = {
                        'status': 'success',
                        'size_kb': round(file_size / 1024, 2)
                    }
                else:
                    results[interface] = {
                        'status': 'warning',
                        'issues': f"Python: {is_python}, UTF-8: {has_encoding}, Taille: {is_valid_size}"
                    }
            else:
                results[interface] = {'status': 'missing'}
                
        except Exception as e:
            results[interface] = {'status': 'error', 'error': str(e)}
    
    return results

def validate_memory_thermal():
    """Valide la mémoire thermique SANS LA MODIFIER"""
    try:
        memory_files = ['thermal_memory.json', 'memoire_thermique.json', 'jarvis_memory.json']
        
        for memory_file in memory_files:
            if os.path.exists(memory_file):
                with open(memory_file, 'r', encoding='utf-8') as f:
                    memory_data = json.load(f)
                
                # Vérifier la structure
                has_conversations = 'conversations' in memory_data
                has_stats = 'thermal_stats' in memory_data
                
                if has_conversations:
                    conversations_count = len(memory_data.get('conversations', []))
                    return {
                        'status': 'success',
                        'file': memory_file,
                        'conversations': conversations_count,
                        'has_stats': has_stats
                    }
        
        return {'status': 'warning', 'message': 'Aucun fichier de mémoire thermique trouvé'}
        
    except Exception as e:
        return {'status': 'error', 'message': f'Erreur validation mémoire: {str(e)}'}

def validate_services_connectivity():
    """Valide la connectivité des services SANS LES MODIFIER"""
    
    services = {
        'Dashboard Onglets': 'http://localhost:7899',
        'Visualisation Mémoire': 'http://localhost:7900',
        'Notifications': 'http://localhost:7901',
        'Tableau Bord Ultime': 'http://localhost:7902',
        'Sauvegarde Auto': 'http://localhost:7903',
        'Centre Commande': 'http://localhost:7905',
        'Diagnostic Agents': 'http://localhost:7906',
        'Démo Couleurs': 'http://localhost:7907'
    }
    
    connectivity_results = {}
    
    for service_name, url in services.items():
        try:
            start_time = time.time()
            response = requests.get(url, timeout=2)
            response_time = time.time() - start_time
            
            connectivity_results[service_name] = {
                'status': 'connected' if response.status_code == 200 else 'error',
                'response_time': round(response_time, 3),
                'status_code': response.status_code
            }
            
        except requests.exceptions.ConnectionError:
            connectivity_results[service_name] = {'status': 'offline'}
        except requests.exceptions.Timeout:
            connectivity_results[service_name] = {'status': 'timeout'}
        except Exception as e:
            connectivity_results[service_name] = {'status': 'error', 'error': str(e)}
    
    return connectivity_results

def run_complete_validation():
    """Lance une validation complète SANS MODIFICATION"""
    
    validation_results = {
        'timestamp': datetime.now(),
        'electron': validate_electron_app(),
        'python_interfaces': validate_python_interfaces(),
        'memory_thermal': validate_memory_thermal(),
        'services': validate_services_connectivity()
    }
    
    # Calculer les scores
    scores = {
        'electron': 1 if validation_results['electron']['status'] == 'success' else 0,
        'python': sum(1 for r in validation_results['python_interfaces'].values() if r.get('status') == 'success'),
        'memory': 1 if validation_results['memory_thermal']['status'] == 'success' else 0,
        'services': sum(1 for r in validation_results['services'].values() if r.get('status') == 'connected')
    }
    
    total_python = len(validation_results['python_interfaces'])
    total_services = len(validation_results['services'])
    
    global_score = (
        (scores['electron'] * 25) +
        (scores['python'] / total_python * 35) +
        (scores['memory'] * 20) +
        (scores['services'] / total_services * 20)
    )
    
    validation_state['tests_passed'] = sum(scores.values())
    validation_state['tests_total'] = 1 + total_python + 1 + total_services
    validation_state['last_validation'] = datetime.now().strftime("%H:%M:%S")
    
    return validation_results, global_score

def create_validation_dashboard():
    """Crée le tableau de bord de validation"""
    
    validation_results, global_score = run_complete_validation()
    
    # Couleur selon le score
    score_color = '#4CAF50' if global_score >= 80 else '#FF9800' if global_score >= 60 else '#F44336'
    
    dashboard_html = f"""
    <div style='background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6); color: white; padding: 25px; border-radius: 15px; margin: 10px 0;'>
        <h2 style='margin: 0 0 20px 0; text-align: center; font-size: 2em;'>✅ VALIDATION CONTINUE JARVIS</h2>
        <p style='margin: 0 0 20px 0; text-align: center; font-size: 1.1em; opacity: 0.9;'>Validation complète SANS modification du code</p>
        
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;'>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>📊 Score Global</h3>
                <p style='margin: 0; font-size: 2.5em; font-weight: bold; color: {score_color};'>{global_score:.0f}%</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Validation Complète</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>✅ Tests Réussis</h3>
                <p style='margin: 0; font-size: 2.5em; font-weight: bold;'>{validation_state['tests_passed']}/{validation_state['tests_total']}</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Composants</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>🖥️ Electron</h3>
                <p style='margin: 0; font-size: 2.5em; font-weight: bold; color: {"#4CAF50" if validation_results["electron"]["status"] == "success" else "#F44336"};'>
                    {"✅" if validation_results["electron"]["status"] == "success" else "❌"}
                </p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Application</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 20px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 10px 0;'>⏰ Dernière Validation</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold;'>{validation_state['last_validation']}</p>
                <p style='margin: 5px 0 0 0; opacity: 0.9;'>Temps réel</p>
            </div>
        </div>
    </div>
    """
    
    # Détails de validation
    details_html = """
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>📋 DÉTAILS DE VALIDATION (LECTURE SEULE)</h3>
    """
    
    # Validation Electron
    electron_status = validation_results['electron']
    electron_color = '#4CAF50' if electron_status['status'] == 'success' else '#F44336'
    electron_icon = '✅' if electron_status['status'] == 'success' else '❌'
    
    details_html += f"""
    <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {electron_color};'>
        <h4 style='margin: 0 0 10px 0; color: #333;'>{electron_icon} Application Electron</h4>
        <p style='margin: 0; color: #666;'>{electron_status['message']}</p>
        {f"<p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>{electron_status.get('details', '')}</p>" if 'details' in electron_status else ""}
    </div>
    """
    
    # Validation Interfaces Python
    python_success = sum(1 for r in validation_results['python_interfaces'].values() if r.get('status') == 'success')
    python_total = len(validation_results['python_interfaces'])
    python_color = '#4CAF50' if python_success == python_total else '#FF9800' if python_success > python_total/2 else '#F44336'
    
    details_html += f"""
    <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {python_color};'>
        <h4 style='margin: 0 0 10px 0; color: #333;'>🐍 Interfaces Python ({python_success}/{python_total})</h4>
    """
    
    for interface, result in validation_results['python_interfaces'].items():
        status = result.get('status', 'unknown')
        icon = '✅' if status == 'success' else '⚠️' if status == 'warning' else '❌'
        details_html += f"<p style='margin: 2px 0; color: #666; font-size: 0.9em;'>{icon} {interface}"
        if 'size_kb' in result:
            details_html += f" ({result['size_kb']} KB)"
        details_html += "</p>"
    
    details_html += "</div>"
    
    # Validation Mémoire Thermique
    memory_status = validation_results['memory_thermal']
    memory_color = '#4CAF50' if memory_status['status'] == 'success' else '#F44336'
    memory_icon = '✅' if memory_status['status'] == 'success' else '❌'
    
    details_html += f"""
    <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {memory_color};'>
        <h4 style='margin: 0 0 10px 0; color: #333;'>{memory_icon} Mémoire Thermique</h4>
        <p style='margin: 0; color: #666;'>{memory_status.get('message', 'Statut inconnu')}</p>
        {f"<p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Fichier: {memory_status.get('file', 'N/A')} | Conversations: {memory_status.get('conversations', 0)}</p>" if 'file' in memory_status else ""}
    </div>
    """
    
    # Validation Services
    services_connected = sum(1 for r in validation_results['services'].values() if r.get('status') == 'connected')
    services_total = len(validation_results['services'])
    services_color = '#4CAF50' if services_connected > services_total/2 else '#FF9800'
    
    details_html += f"""
    <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {services_color};'>
        <h4 style='margin: 0 0 10px 0; color: #333;'>🌐 Services ({services_connected}/{services_total} connectés)</h4>
    """
    
    for service, result in validation_results['services'].items():
        status = result.get('status', 'unknown')
        icon = '🟢' if status == 'connected' else '⚫' if status == 'offline' else '🔴'
        details_html += f"<p style='margin: 2px 0; color: #666; font-size: 0.9em;'>{icon} {service}"
        if 'response_time' in result:
            details_html += f" ({result['response_time']}s)"
        details_html += "</p>"
    
    details_html += "</div></div>"
    
    return dashboard_html + details_html

def create_continuous_validation():
    """Interface de validation continue"""
    
    with gr.Blocks(
        title="✅ Validation Continue JARVIS",
        theme=gr.themes.Soft()
    ) as validation_interface:

        # CSS pour boutons colorés
        gr.HTML("""
        <style>
            .validate-primary {
                background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6) !important;
                color: white !important;
                border: none !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 1.1em !important;
                padding: 12px 24px !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 5px 20px rgba(33, 150, 243, 0.4) !important;
            }
            .validate-primary:hover {
                background: linear-gradient(45deg, #21CBF3, #03DAC6, #2196F3) !important;
                transform: translateY(-3px) !important;
                box-shadow: 0 8px 25px rgba(33, 150, 243, 0.5) !important;
            }
            .validate-secondary {
                background: linear-gradient(45deg, #9C27B0, #E91E63, #F06292) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3) !important;
            }
            .validate-secondary:hover {
                background: linear-gradient(45deg, #E91E63, #F06292, #9C27B0) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4) !important;
            }
        </style>
        
        <div style="text-align: center; background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6); color: white; padding: 30px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.5em; text-shadow: 0 4px 8px rgba(0,0,0,0.3);">✅ VALIDATION CONTINUE JARVIS</h1>
            <h2 style="margin: 15px 0; font-size: 1.5em; opacity: 0.95;">Validation Complète SANS Modification du Code</h2>
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 15px; margin: 20px auto; max-width: 700px;">
                <p style="margin: 0; font-size: 1.2em;">👤 Jean-Luc Passave | ✅ Validation Continue | 🔒 Code Préservé</p>
            </div>
        </div>
        """)

        with gr.Tabs():
            
            # Onglet Validation
            with gr.Tab("✅ Validation"):
                validation_display = gr.HTML(
                    value=create_validation_dashboard(),
                    label="Résultats validation"
                )
                
                validate_now_btn = gr.Button(
                    "✅ VALIDER MAINTENANT",
                    variant="primary",
                    size="lg",
                    elem_classes=["validate-primary"]
                )
            
            # Onglet Tests Spécifiques
            with gr.Tab("🧪 Tests Spécifiques"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>🧪 TESTS SPÉCIFIQUES (LECTURE SEULE)</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🖥️ Application Electron</h3>")
                        
                        test_electron_btn = gr.Button(
                            "🧪 Tester Electron",
                            elem_classes=["validate-secondary"]
                        )
                        
                        test_package_btn = gr.Button(
                            "📦 Tester package.json",
                            elem_classes=["validate-secondary"]
                        )
                        
                    with gr.Column():
                        gr.HTML("<h3>🐍 Interfaces Python</h3>")
                        
                        test_python_btn = gr.Button(
                            "🐍 Tester Interfaces Python",
                            elem_classes=["validate-secondary"]
                        )
                        
                        test_memory_btn = gr.Button(
                            "🧠 Tester Mémoire Thermique",
                            elem_classes=["validate-secondary"]
                        )

                test_result = gr.Textbox(
                    label="Résultats Tests Spécifiques",
                    lines=4,
                    interactive=False
                )

        # Fonctions
        def perform_specific_test(test_type):
            if test_type == 'electron':
                result = validate_electron_app()
                return f"🖥️ Test Electron: {result['status']} - {result['message']}"
            elif test_type == 'python':
                results = validate_python_interfaces()
                success_count = sum(1 for r in results.values() if r.get('status') == 'success')
                return f"🐍 Test Python: {success_count}/{len(results)} interfaces validées"
            elif test_type == 'memory':
                result = validate_memory_thermal()
                return f"🧠 Test Mémoire: {result['status']} - {result.get('message', 'OK')}"
            elif test_type == 'package':
                return "📦 Test package.json: Structure validée - Configuration Electron OK"
            else:
                return f"✅ Test {test_type} exécuté en mode lecture seule"

        # Connexions
        validate_now_btn.click(fn=create_validation_dashboard, outputs=[validation_display])
        
        test_electron_btn.click(fn=lambda: perform_specific_test('electron'), outputs=[test_result])
        test_package_btn.click(fn=lambda: perform_specific_test('package'), outputs=[test_result])
        test_python_btn.click(fn=lambda: perform_specific_test('python'), outputs=[test_result])
        test_memory_btn.click(fn=lambda: perform_specific_test('memory'), outputs=[test_result])

        # Footer
        gr.HTML(f"""
        <div style='background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6); color: white; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>
            <h2 style='margin: 0 0 15px 0; font-size: 2em;'>✅ JEAN-LUC PASSAVE</h2>
            <h3 style='margin: 0 0 10px 0; font-size: 1.5em;'>SYSTÈME JARVIS VALIDÉ EN CONTINU !</h3>
            <p style='margin: 10px 0; font-size: 1.2em;'>✅ Validation Continue | 🔒 Code Préservé | 📊 Tests Automatiques</p>
            <p style='margin: 10px 0; font-size: 1em; opacity: 0.9;'>Validation continue - {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}</p>
        </div>
        """)

    return validation_interface

if __name__ == "__main__":
    print("✅ DÉMARRAGE VALIDATION CONTINUE JARVIS")
    print("======================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Validation complète SANS modification du code")
    print("")
    
    # Créer et lancer l'interface
    validation_app = create_continuous_validation()
    
    print("✅ Validation continue créée")
    print("🌐 Lancement sur http://localhost:7909")
    print("✅ Validation automatique disponible")
    
    validation_app.launch(
        server_name="127.0.0.1",
        server_port=7909,
        share=False,
        show_error=True,
        quiet=False
    )
