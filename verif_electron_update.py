#!/usr/bin/env python3
"""
🔍 VÉRIFICATION MISE À JOUR APPLICATION ELECTRON
==============================================

Script de vérification que toutes les mises à jour sont bien intégrées
dans l'application Electron JARVIS.

Auteur: <PERSON><PERSON><PERSON> + Claude
Date: 2025-06-21
"""

import json
import re
from pathlib import Path

def verifier_electron():
    """Vérifier l'application Electron"""
    print("🔍 VÉRIFICATION APPLICATION ELECTRON")
    print("=" * 50)
    
    base_dir = Path(__file__).parent
    electron_file = base_dir / "jarvis_electron_final_complet.js"
    
    if not electron_file.exists():
        print("❌ Fichier Electron introuvable")
        return
        
    with open(electron_file, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    # Éléments à vérifier
    elements = {
        "Boutons": [
            "🚀 JARVIS V2 PRO",
            "💼 Donner du Travail", 
            "🤖 Collaboration ChatGPT",
            "🏠 IA Locale Autonome",
            "⚡ Accélérateurs Turbo"
        ],
        "Fonctions": [
            "openV2ProDashboard",
            "assignWorkToAgent",
            "collaborateWithChatGPT", 
            "testIALocaleAutonome",
            "testAccelerateursTurbo"
        ],
        "Endpoints": [
            "/agent/chat",
            "/collaboration-chatgpt",
            "/ia-locale-chat",
            "/ia-turbo-chat"
        ]
    }
    
    resultats = {}
    
    for categorie, items in elements.items():
        print(f"\n🔍 {categorie}:")
        resultats[categorie] = {}
        
        for item in items:
            if item in contenu:
                print(f"✅ {item}")
                resultats[categorie][item] = True
            else:
                print(f"❌ {item}")
                resultats[categorie][item] = False
    
    # Calcul du score
    total_items = sum(len(items) for items in elements.values())
    items_ok = sum(sum(1 for ok in cat.values() if ok) for cat in resultats.values())
    score = (items_ok / total_items) * 100
    
    print(f"\n📊 SCORE GLOBAL: {score:.1f}%")
    
    if score >= 95:
        print("🎉 APPLICATION ELECTRON PARFAITEMENT MISE À JOUR !")
    elif score >= 80:
        print("👍 Application bien mise à jour, quelques détails à vérifier")
    else:
        print("⚠️ Mise à jour incomplète")
    
    # Vérifier package.json
    print(f"\n🔍 Package.json:")
    package_file = base_dir / "package.json"
    
    if package_file.exists():
        with open(package_file, 'r', encoding='utf-8') as f:
            package_data = json.load(f)
            
        version = package_data.get("version", "")
        print(f"📦 Version: {version}")
        
        scripts_nouveaux = ["turbo", "ia-locale", "turbo-complet"]
        scripts = package_data.get("scripts", {})
        
        for script in scripts_nouveaux:
            if script in scripts:
                print(f"✅ Script: {script}")
            else:
                print(f"❌ Script manquant: {script}")
    else:
        print("❌ package.json introuvable")
    
    return score

def main():
    score = verifier_electron()
    
    print(f"\n🎯 RÉSUMÉ:")
    print(f"📈 Score de mise à jour: {score:.1f}%")
    
    if score >= 95:
        print("✅ Toutes les fonctionnalités sont intégrées !")
        print("🚀 Vous pouvez utiliser l'application Electron mise à jour")
    else:
        print("⚠️ Certains éléments nécessitent une vérification")

if __name__ == "__main__":
    main()
