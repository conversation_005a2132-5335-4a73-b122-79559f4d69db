
import os
import json
import base64
from flask import Flask, request, jsonify
import tempfile

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "healthy", "model": "ltx-video"})

@app.route('/generate', methods=['POST'])
def generate_video():
    try:
        data = request.json
        prompt = data.get('prompt', '')
        duration = data.get('duration', 5)
        fps = data.get('fps', 24)
        resolution = data.get('resolution', '1280x720')
        
        print(f"🎬 Génération LTX: {prompt[:50]}...")
        
        # Simulation de génération (remplacer par vraie implémentation LTX)
        # TODO: Intégrer le vrai modèle LTX-Video ici
        
        # Créer une vidéo de test avec FFmpeg
        import subprocess
        import time
        
        output_file = f"/tmp/ltx_video_{int(time.time())}.mp4"
        width, height = resolution.split('x')
        
        cmd = [
            'ffmpeg', '-f', 'lavfi',
            '-i', f'testsrc=duration={duration}:size={resolution}:rate={fps}',
            '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
            '-y', output_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(output_file):
            # Encoder en base64 pour retour
            with open(output_file, 'rb') as f:
                video_data = base64.b64encode(f.read()).decode()
            
            os.remove(output_file)  # Nettoyer
            
            return jsonify({
                "status": "success",
                "video_data": video_data,
                "prompt": prompt,
                "duration": duration,
                "fps": fps,
                "resolution": resolution,
                "model": "ltx-video-local"
            })
        else:
            return jsonify({
                "status": "error",
                "message": f"Erreur FFmpeg: {result.stderr}"
            }), 500
            
    except Exception as e:
        return jsonify({
            "status": "error", 
            "message": str(e)
        }), 500

if __name__ == '__main__':
    print("🎬 Serveur LTX-Video démarré sur http://localhost:7863")
    app.run(host='0.0.0.0', port=7863, debug=False)
