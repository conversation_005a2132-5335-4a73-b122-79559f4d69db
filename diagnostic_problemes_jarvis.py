#!/usr/bin/env python3
"""
🔧 DIAGNOSTIC COMPLET DES PROBLÈMES JARVIS
==========================================

Identifie et corrige les problèmes bizarres dans:
- Détecteur de simulations
- Accélérateurs mémoire
- Mémoire thermique

Auteur: <PERSON><PERSON><PERSON> - FAMILLE IA
Date: 2025-01-21
"""

import os
import re
import json
import time
from pathlib import Path

class DiagnosticJarvis:
    def __init__(self):
        self.problemes_detectes = []
        self.corrections_proposees = []
        
    def diagnostic_detecteur_simulations(self):
        """Diagnostique le détecteur de simulations"""
        print("🔍 DIAGNOSTIC DÉTECTEUR DE SIMULATIONS")
        print("=" * 50)
        
        try:
            # Lire le fichier principal
            with open('jarvis_architecture_multi_fenetres.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Problème 1: Le détecteur se détecte lui-même
            if 'detect_simulated_code' in content and 'simulation' in content:
                probleme = {
                    "type": "AUTO_DETECTION",
                    "description": "Le détecteur se détecte lui-même comme simulation",
                    "gravite": "CRITIQUE",
                    "solution": "Exclure les commentaires et noms de fonctions du scan"
                }
                self.problemes_detectes.append(probleme)
                print("❌ PROBLÈME: Auto-détection du détecteur")
            
            # Problème 2: Patterns trop agressifs
            patterns_agressifs = [
                r"Bonjour.*JARVIS",
                r"Salut.*JARVIS", 
                r"Comment.*ça.*va"
            ]
            
            for pattern in patterns_agressifs:
                if re.search(pattern, content, re.IGNORECASE):
                    probleme = {
                        "type": "PATTERN_AGRESSIF",
                        "description": f"Pattern trop agressif: {pattern}",
                        "gravite": "MOYENNE",
                        "solution": "Affiner les patterns pour éviter les faux positifs"
                    }
                    self.problemes_detectes.append(probleme)
                    print(f"⚠️ Pattern agressif détecté: {pattern}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur diagnostic détecteur: {e}")
            return False
    
    def diagnostic_accelerateurs_memoire(self):
        """Diagnostique les accélérateurs mémoire"""
        print("\n🚀 DIAGNOSTIC ACCÉLÉRATEURS MÉMOIRE")
        print("=" * 50)
        
        accelerateurs_files = [
            'jarvis_accelerateurs_turbo.py',
            'accelerateurs_jarvis_complet.py',
            'jarvis_memory_compression_accelerators.py'
        ]
        
        for file_path in accelerateurs_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path} trouvé")
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Vérifier les classes principales
                    classes_attendues = {
                        'jarvis_accelerateurs_turbo.py': ['AccelerateursJarvisTurbo'],
                        'accelerateurs_jarvis_complet.py': ['AccelerateurMemoire'],
                        'jarvis_memory_compression_accelerators.py': ['MemoryAccelerator']
                    }
                    
                    for classe in classes_attendues.get(file_path, []):
                        if f"class {classe}" in content:
                            print(f"  ✅ Classe {classe} présente")
                        else:
                            probleme = {
                                "type": "CLASSE_MANQUANTE",
                                "description": f"Classe {classe} manquante dans {file_path}",
                                "gravite": "CRITIQUE",
                                "solution": f"Vérifier la définition de la classe {classe}"
                            }
                            self.problemes_detectes.append(probleme)
                            print(f"  ❌ Classe {classe} manquante")
                    
                    # Vérifier les imports
                    imports_requis = ['time', 'json', 'os']
                    for imp in imports_requis:
                        if f"import {imp}" in content:
                            print(f"  ✅ Import {imp} présent")
                        else:
                            print(f"  ⚠️ Import {imp} manquant")
                    
                except Exception as e:
                    probleme = {
                        "type": "ERREUR_LECTURE",
                        "description": f"Impossible de lire {file_path}: {e}",
                        "gravite": "CRITIQUE",
                        "solution": "Vérifier les permissions et l'encodage du fichier"
                    }
                    self.problemes_detectes.append(probleme)
                    print(f"  ❌ Erreur lecture: {e}")
            else:
                probleme = {
                    "type": "FICHIER_MANQUANT",
                    "description": f"Fichier {file_path} manquant",
                    "gravite": "MOYENNE",
                    "solution": f"Créer ou restaurer le fichier {file_path}"
                }
                self.problemes_detectes.append(probleme)
                print(f"❌ {file_path} manquant")
    
    def diagnostic_memoire_thermique(self):
        """Diagnostique la mémoire thermique"""
        print("\n🧠 DIAGNOSTIC MÉMOIRE THERMIQUE")
        print("=" * 50)
        
        # Vérifier le fichier de mémoire thermique
        memory_files = [
            'thermal_memory_persistent.json',
            'jarvis_thermal_memory.json',
            'memory_thermal.json'
        ]
        
        memory_found = False
        for memory_file in memory_files:
            if os.path.exists(memory_file):
                print(f"✅ Fichier mémoire trouvé: {memory_file}")
                memory_found = True
                
                try:
                    with open(memory_file, 'r', encoding='utf-8') as f:
                        memory_data = json.load(f)
                    
                    # Vérifier la structure
                    if isinstance(memory_data, dict):
                        print(f"  ✅ Structure JSON valide")
                        print(f"  📊 Clés principales: {list(memory_data.keys())}")
                        
                        # Vérifier la taille
                        file_size = os.path.getsize(memory_file) / (1024 * 1024)  # MB
                        print(f"  💾 Taille: {file_size:.2f} MB")
                        
                        if file_size > 100:  # Plus de 100 MB
                            probleme = {
                                "type": "MEMOIRE_TROP_GROSSE",
                                "description": f"Fichier mémoire trop volumineux: {file_size:.2f} MB",
                                "gravite": "MOYENNE",
                                "solution": "Nettoyer ou compresser la mémoire thermique"
                            }
                            self.problemes_detectes.append(probleme)
                            print(f"  ⚠️ Fichier volumineux: {file_size:.2f} MB")
                    else:
                        probleme = {
                            "type": "STRUCTURE_INVALIDE",
                            "description": f"Structure JSON invalide dans {memory_file}",
                            "gravite": "CRITIQUE",
                            "solution": "Recréer le fichier de mémoire thermique"
                        }
                        self.problemes_detectes.append(probleme)
                        print(f"  ❌ Structure invalide")
                        
                except Exception as e:
                    probleme = {
                        "type": "MEMOIRE_CORROMPUE",
                        "description": f"Fichier mémoire corrompu: {e}",
                        "gravite": "CRITIQUE",
                        "solution": "Restaurer depuis une sauvegarde ou recréer"
                    }
                    self.problemes_detectes.append(probleme)
                    print(f"  ❌ Fichier corrompu: {e}")
        
        if not memory_found:
            probleme = {
                "type": "MEMOIRE_MANQUANTE",
                "description": "Aucun fichier de mémoire thermique trouvé",
                "gravite": "CRITIQUE",
                "solution": "Initialiser la mémoire thermique"
            }
            self.problemes_detectes.append(probleme)
            print("❌ Aucun fichier de mémoire thermique trouvé")
    
    def generer_corrections(self):
        """Génère les corrections automatiques"""
        print("\n🔧 GÉNÉRATION DES CORRECTIONS")
        print("=" * 50)
        
        for probleme in self.problemes_detectes:
            if probleme["type"] == "AUTO_DETECTION":
                correction = self.corriger_detecteur_simulations()
                self.corrections_proposees.append(correction)
            
            elif probleme["type"] == "CLASSE_MANQUANTE":
                correction = self.corriger_classe_manquante(probleme)
                self.corrections_proposees.append(correction)
            
            elif probleme["type"] == "MEMOIRE_MANQUANTE":
                correction = self.corriger_memoire_manquante()
                self.corrections_proposees.append(correction)
    
    def corriger_detecteur_simulations(self):
        """Corrige le détecteur de simulations"""
        return {
            "type": "CORRECTION_DETECTEUR",
            "description": "Améliorer les patterns du détecteur",
            "code": """
# Patterns améliorés pour éviter l'auto-détection
simulation_patterns = [
    r"(?<!detect_)(?<!détect)simulé(?!_code)",  # Évite detect_simulated_code
    r"(?<!test.*de.*la.*|détecteur.*de.*|fonction.*de.*)simulation",
    r"fake.*data|mock.*response|placeholder.*content",
    r"test.*message.*exemple|conversation.*factice",
    r"15:42.*Exécution.*réussie|99\.8%.*Disponibilité"  # Données spécifiques
]
            """,
            "fichier": "jarvis_architecture_multi_fenetres.py"
        }
    
    def corriger_classe_manquante(self, probleme):
        """Corrige une classe manquante"""
        return {
            "type": "CORRECTION_CLASSE",
            "description": f"Restaurer la classe manquante",
            "probleme": probleme["description"],
            "action": "Vérifier l'import et la définition de classe"
        }
    
    def corriger_memoire_manquante(self):
        """Corrige la mémoire thermique manquante"""
        return {
            "type": "CORRECTION_MEMOIRE",
            "description": "Initialiser la mémoire thermique",
            "code": """
{
    "conversations": [],
    "thermal_stats": {
        "total_entries": 0,
        "memory_size_mb": 0,
        "last_cleanup": "",
        "thermal_level": 0.3
    },
    "metadata": {
        "created": "2025-01-21",
        "version": "2.0",
        "owner": "Jean-Luc Passave"
    }
}
            """,
            "fichier": "thermal_memory_persistent.json"
        }
    
    def rapport_diagnostic(self):
        """Génère le rapport final"""
        print("\n📊 RAPPORT DIAGNOSTIC FINAL")
        print("=" * 60)
        
        print(f"🔍 Problèmes détectés: {len(self.problemes_detectes)}")
        print(f"🔧 Corrections proposées: {len(self.corrections_proposees)}")
        
        # Grouper par gravité
        critiques = [p for p in self.problemes_detectes if p["gravite"] == "CRITIQUE"]
        moyens = [p for p in self.problemes_detectes if p["gravite"] == "MOYENNE"]
        
        print(f"\n🚨 Problèmes CRITIQUES: {len(critiques)}")
        for p in critiques:
            print(f"  • {p['description']}")
        
        print(f"\n⚠️ Problèmes MOYENS: {len(moyens)}")
        for p in moyens:
            print(f"  • {p['description']}")
        
        print(f"\n💡 RECOMMANDATIONS:")
        print("1. Corriger d'abord les problèmes critiques")
        print("2. Tester chaque correction individuellement")
        print("3. Sauvegarder avant d'appliquer les corrections")
        print("4. Redémarrer JARVIS après les corrections")
        
        return {
            "total_problemes": len(self.problemes_detectes),
            "critiques": len(critiques),
            "moyens": len(moyens),
            "corrections_disponibles": len(self.corrections_proposees)
        }

def main():
    """Fonction principale de diagnostic"""
    print("🔧 DIAGNOSTIC COMPLET JARVIS - PROBLÈMES BIZARRES")
    print("=" * 60)
    print("Analyse des problèmes dans:")
    print("• Détecteur de simulations")
    print("• Accélérateurs mémoire") 
    print("• Mémoire thermique")
    print("=" * 60)
    
    diagnostic = DiagnosticJarvis()
    
    # Exécuter tous les diagnostics
    diagnostic.diagnostic_detecteur_simulations()
    diagnostic.diagnostic_accelerateurs_memoire()
    diagnostic.diagnostic_memoire_thermique()
    
    # Générer les corrections
    diagnostic.generer_corrections()
    
    # Rapport final
    rapport = diagnostic.rapport_diagnostic()
    
    print(f"\n✅ DIAGNOSTIC TERMINÉ")
    print(f"📋 {rapport['total_problemes']} problèmes identifiés")
    print(f"🔧 {rapport['corrections_disponibles']} corrections prêtes")

if __name__ == "__main__":
    main()
