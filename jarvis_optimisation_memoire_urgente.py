#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚨 JARVIS - OPTIMISATION MÉMOIRE URGENTE
Correction immédiate des problèmes de mémoire et performance
Créé en urgence par Claude pour Jean-Luc Pass<PERSON>
"""

import gc
import os
import sys
import psutil
import threading
import time
from datetime import datetime

# ============================================================================
# NETTOYAGE MÉMOIRE AGRESSIF
# ============================================================================

class JarvisMemoryOptimizer:
    """Optimiseur mémoire agressif pour JARVIS"""
    
    def __init__(self):
        self.memory_threshold = 85.0  # Seuil critique à 85%
        self.cleanup_running = False
        
    def get_memory_usage(self):
        """Obtenir l'utilisation mémoire actuelle"""
        memory = psutil.virtual_memory()
            "percent": memory.percent,
            "used_gb": memory.used / (1024**3),
            "available_gb": memory.available / (1024**3),
            "total_gb": memory.total / (1024**3)
        }
    
    def emergency_memory_cleanup(self):
        """Nettoyage mémoire d'urgence"""
        print("🚨 NETTOYAGE MÉMOIRE D'URGENCE EN COURS...")
        
        # 1. Garbage collection Python agressif
        print("🗑️ Garbage collection Python...")
        for i in range(3):
            collected = gc.collect()
            print(f"   Cycle {i+1}: {collected} objets supprimés")
        
        # 2. Vider les caches Python
        print("💾 Vidage des caches...")
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()
        
        # 3. Forcer la libération mémoire
        print("🔄 Libération forcée mémoire...")
        try:
            import ctypes
            libc = ctypes.CDLL("libc.so.6")
            libc.malloc_trim(0)
        except:
            pass
        
        # 4. Vérifier le résultat
        memory_after = self.get_memory_usage()
        print(f"✅ Mémoire après nettoyage: {memory_after['percent']:.1f}%")
        
        return memory_after
    
    def kill_heavy_processes(self):
        """Tuer les processus lourds non essentiels"""
        print("🔪 Recherche des processus lourds...")
        
        # Processus à éviter de tuer
        protected_processes = [
            'python', 'jarvis', 'electron', 'node', 'system', 'kernel'
        ]
        
        heavy_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_percent']):
            try:
                if proc.info['memory_percent'] > 5.0:  # Plus de 5% de RAM
                    process_name = proc.info['name'].lower()
                    if not any(protected in process_name for protected in protected_processes):
                        heavy_processes.append(proc.info)
            except:
                continue
        
        print(f"🎯 {len(heavy_processes)} processus lourds trouvés")
        
        # Afficher les processus lourds (sans les tuer automatiquement)
        for proc in heavy_processes[:5]:  # Top 5
            print(f"   📊 {proc['name']}: {proc['memory_percent']:.1f}% RAM")
        
        return heavy_processes
    
    def optimize_jarvis_memory(self):
        """Optimiser spécifiquement la mémoire de JARVIS"""
        print("🤖 Optimisation mémoire JARVIS...")
        
        # Variables globales à nettoyer
        cleanup_vars = [
            'JARVIS_BRAIN', 'JARVIS_MULTIMEDIA', 'JARVIS_ADVANCED_SYSTEMS',
            'thermal_memory_data', 'conversation_history', 'cached_responses'
        ]
        
        # Nettoyer les variables globales si elles existent
        for var_name in cleanup_vars:
            if var_name in globals():
                try:
                    del globals()[var_name]
                    print(f"   🗑️ Variable {var_name} supprimée")
                except:
                    pass
        
        # Forcer le garbage collection
        gc.collect()
        
        print("✅ Optimisation JARVIS terminée")
    
    def start_memory_monitor(self):
        """Démarrer la surveillance mémoire continue"""
        if self.cleanup_running:
            return
        
        self.cleanup_running = True
        
        def monitor_loop():
            while self.cleanup_running:
                try:
                    memory = self.get_memory_usage()
                    
                    if memory["percent"] > 95:  # Seuil augmenté pour réduire les alertes
                        print(f"🚨 ALERTE MÉMOIRE CRITIQUE: {memory['percent']:.1f}%")
                        self.emergency_memory_cleanup()
                        self.optimize_jarvis_memory()
                    
                    time.sleep(30)  # Vérifier toutes les 30 secondes
                    
                except Exception as e:
                    print(f"Erreur monitoring mémoire: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        print("📊 Surveillance mémoire démarrée")
    
    def stop_memory_monitor(self):
        """Arrêter la surveillance mémoire"""
        self.cleanup_running = False
        print("⏹️ Surveillance mémoire arrêtée")

# ============================================================================
# OPTIMISATION SYSTÈME COMPLÈTE
# ============================================================================

def optimize_system_performance():
    """Optimisation complète des performances système"""
    print("🚀 OPTIMISATION SYSTÈME COMPLÈTE...")
    
    optimizer = JarvisMemoryOptimizer()
    
    # 1. État initial
    initial_memory = optimizer.get_memory_usage()
    print(f"📊 Mémoire initiale: {initial_memory['percent']:.1f}%")
    
    # 2. Nettoyage d'urgence si nécessaire
    if initial_memory["percent"] > 85:
        print("🚨 MÉMOIRE CRITIQUE - NETTOYAGE D'URGENCE")
        optimizer.emergency_memory_cleanup()
        optimizer.optimize_jarvis_memory()
    
    # 3. Analyser les processus lourds
    heavy_processes = optimizer.kill_heavy_processes()
    
    # 4. Démarrer la surveillance continue
    optimizer.start_memory_monitor()
    
    # 5. État final
    final_memory = optimizer.get_memory_usage()
    improvement = initial_memory["percent"] - final_memory["percent"]
    
    print(f"✅ OPTIMISATION TERMINÉE")
    print(f"📊 Mémoire finale: {final_memory['percent']:.1f}%")
    print(f"📈 Amélioration: {improvement:.1f}% de RAM libérée")
    
    return optimizer

# ============================================================================
# CORRECTION COULEURS INTERFACE
# ============================================================================

def fix_interface_colors():
    """Corriger les couleurs de l'interface pour visibilité parfaite"""
    print("🎨 CORRECTION COULEURS INTERFACE...")
    
    # CSS avec couleurs haute visibilité
    high_contrast_css = """
    /* CORRECTION COULEURS HAUTE VISIBILITÉ */
    }
    
    /* Texte principal */
    p, span, div, label {
    }
    
    /* Titres */
    h1, h2, h3, h4, h5, h6 {
    }
    
    /* Boutons */
    button, .btn {
    }
    
    /* Conteneurs */
    .container, .card, .panel {
    }
    
    /* Alertes */
    .alert, .warning {
    }
    
    /* Statut */
    }
    
    /* Liens */
    }
    
    /* Inputs */
    input, textarea, select {
    }
    """
    
    # Sauvegarder le CSS de correction
    css_file = "jarvis_high_contrast_fix.css"
    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(high_contrast_css)
    
    print(f"✅ CSS haute visibilité sauvegardé: {css_file}")
    
    return css_file

# ============================================================================
# SCRIPT PRINCIPAL D'URGENCE
# ============================================================================

def main():
    """Script principal d'optimisation d'urgence"""
    print("🚨" * 20)
    print("🚨 JARVIS - OPTIMISATION D'URGENCE")
    print("🚨 Correction mémoire + couleurs")
    print("🚨" * 20)
    
    try:
        # 1. Optimisation mémoire
        optimizer = optimize_system_performance()
        
        # 2. Correction couleurs
        css_file = fix_interface_colors()
        
        # 3. Recommandations
        print("\n📋 RECOMMANDATIONS URGENTES:")
        print("1. 🔄 Redémarrer JARVIS pour appliquer les optimisations")
        print("2. 🎨 Appliquer le CSS haute visibilité aux interfaces")
        print("3. 📊 Surveiller la mémoire en continu")
        print("4. 🗑️ Fermer les applications non essentielles")
        
        # 4. État final du système
        final_memory = optimizer.get_memory_usage()
        print(f"\n📊 ÉTAT FINAL SYSTÈME:")
        print(f"💾 Mémoire: {final_memory['percent']:.1f}%")
        print(f"💽 RAM libre: {final_memory['available_gb']:.1f} GB")
        
        if final_memory["percent"] < 80:
            print("✅ SYSTÈME OPTIMISÉ - MÉMOIRE SOUS CONTRÔLE")
        else:
            print("⚠️ MÉMOIRE ENCORE ÉLEVÉE - REDÉMARRAGE RECOMMANDÉ")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR OPTIMISATION: {e}")
        return False

if __name__ == "__main__":
    main()
