jean-luc         77012   2.8  0.1 413025600  23648 s023  S+    3:06PM   0:35.87 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python jarvis_architecture_multi_fenetres.py
jean-luc         66189   0.0 27.6 416245392 4627632 s020  S+    2:13PM   2:54.21 llama-server --model /Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf --host 0.0.0.0 --port 8000 --n-gpu-layers 36 --ctx-size 2048 --batch-size 256 --ubatch-size 128 --threads 6 --threads-batch 6 --flash-attn --cont-batching --parallel 1 --cache-type-k f16 --cache-type-v f16 --mlock --no-warmup --verbose
jean-luc         79573   0.0  0.0 410059184    272   ??  R     3:19PM   0:00.00 grep -E (llama-server|python.*jarvis)
jean-luc         78972   0.0  0.0 411018032   4656 s010  S+    3:16PM   0:00.13 /opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Python.app/Contents/MacOS/Python -c \012import sys\012sys.path.append('.')\012\012print('^AM-yM-` REDÉMARRAGE CERVEAU AUTONOME JARVIS')\012print('=' * 50)\012\012try:\012    from jarvis_cerveau_autonome_integre import demarrer_jarvis_cerveau_integre\012    \012    if demarrer_jarvis_cerveau_integre():\012        print('-'^E Cerveau autonome redémarré')\012        print('^AM-yM-` Génération de nouvelles pensées en cours...')\012        \012        import time\012        time.sleep(200)  # Attendre 3+ minutes pour nouvelles pensées\012        \012        # Vérifier nouvelles pensées\012        import json\012        import os\012        from datetime import datetime\012        \012        if os.path.exists('jarvis_pensees_autonomes.json'):\012            with open('jarvis_pensees_autonomes.json', 'r', encoding='utf-8') as f:\012                data = json.load(f)\012            \012            pensees = data.get('pensees_autonomes', [])\012            print(f'\n📊 PENSÉES TOTALES: {len(pensees)}')\012            \012            # Vérifier pensées récentes (dernières 10 minutes)\012            maintenant = datetime.now()\012            pensees_recentes = []\012            \012            for pensee in pensees:\012                timestamp_str = pensee.get('timestamp', '')\012                try:\012                    timestamp = datetime.fromisoformat(timestamp_str)\012                    diff = (maintenant - timestamp).total_seconds()\012                    if diff < 600:  # 10 minutes\012                        pensees_recentes.append(pensee)\012                except:\012                    pass\012            \012            print(f'^AM-yM-` PENSÉES RÉCENTES (10 min): {len(pensees_recentes)}')\012            \012            if pensees_recentes:\012                for pensee in pensees_recentes[-3:]:\012                    print(f'   • {pensee.get("sujet", "")}')\012                    print(f'     �� {pensee.get("timestamp", "")}')\012                \012                print(f'\n-'^E CERVEAU AUTONOME: NOUVELLES PENSÉES GÉNÉRÉES')\012            else:\012                print(f'-&\240️ Aucune nouvelle pensée générée')\012        else:\012            print(f'-'-L Fichier pensées non trouvé')\012    else:\012        print('-'-L Erreur redémarrage cerveau autonome')\012        \012except Exception as e:\012    print(f'-'-L Erreur: {e}')\012    import traceback\012    traceback.print_exc()\012
