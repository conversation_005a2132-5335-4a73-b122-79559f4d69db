#!/bin/bash

echo "🔄 RESTAURATION JARVIS FONCTIONNEL"
echo "=================================="

# Copier les fichiers
cp jarvis_interface_propre.py ../
cp demarrer_deepseek_optimise.sh ../
cp thermal_memory_persistent.json ../

# Rendre exécutable
chmod +x ../demarrer_deepseek_optimise.sh

echo "✅ Fichiers restaurés"
echo ""
echo "🚀 POUR REDÉMARRER JARVIS :"
echo "1. cd /Volumes/seagate/Louna_Electron_Latest"
echo "2. ./demarrer_deepseek_optimise.sh"
echo "3. Dans un autre terminal:"
echo "   source venv_deepseek/bin/activate"
echo "   python jarvis_interface_propre.py"
echo ""
echo "🌐 Interface: http://localhost:7863"
echo "🤖 Serveur: http://localhost:8000"
