#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Monitoring Santé JARVIS <PERSON><PERSON><PERSON> - 2025
Surveillance complète de l'écosystème JARVIS sans modifier le code existant
"""

import gradio as gr
import requests
import psutil
import subprocess
import os
import time
from datetime import datetime
import threading

# État global du monitoring
monitoring_state = {
    'active': False,
    'last_check': None,
    'alerts': [],
    'metrics_history': []
}

def check_service_health(url, timeout=3):
    """Vérifie la santé d'un service sans le modifier"""
    try:
        response = requests.get(url, timeout=timeout)
        return {
            'status': 'healthy' if response.status_code == 200 else 'warning',
            'response_time': response.elapsed.total_seconds(),
            'status_code': response.status_code
        }
    except requests.exceptions.ConnectionError:
        return {'status': 'down', 'response_time': None, 'status_code': None}
    except requests.exceptions.Timeout:
        return {'status': 'timeout', 'response_time': None, 'status_code': None}
    except Exception as e:
        return {'status': 'error', 'response_time': None, 'status_code': None, 'error': str(e)}

def get_system_health():
    """Récupère la santé complète du système"""
    
    # Services JARVIS à surveiller
    services = {
        'Dashboard Onglets': 'http://localhost:7899',
        'Communication': 'http://localhost:7866',
        'Visualisation Mémoire': 'http://localhost:7900',
        'Notifications': 'http://localhost:7901',
        'Tableau Bord Ultime': 'http://localhost:7902',
        'Sauvegarde Auto': 'http://localhost:7903',
        'Test Neurones': 'http://localhost:7898',
        'Centre Contrôle': 'http://localhost:7897',
        'Monitoring': 'http://localhost:7894',
        'Test Agents': 'http://localhost:7893'
    }
    
    # Vérifier chaque service
    service_health = {}
    for name, url in services.items():
        service_health[name] = check_service_health(url)
    
    # Métriques système
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # Processus Electron
    electron_running = any('electron' in p.name().lower() for p in psutil.process_iter(['name']))
    
    # Calculer score de santé global
    healthy_services = sum(1 for s in service_health.values() if s['status'] == 'healthy')
    total_services = len(service_health)
    health_score = (healthy_services / total_services) * 100 if total_services > 0 else 0
    
    return {
        'services': service_health,
        'system': {
            'cpu': cpu_percent,
            'memory': memory.percent,
            'disk': disk.percent,
            'memory_gb': round(memory.total / (1024**3), 1)
        },
        'electron': electron_running,
        'health_score': health_score,
        'timestamp': datetime.now()
    }

def create_health_dashboard():
    """Crée le tableau de bord de santé"""
    
    health = get_system_health()
    
    # Score de santé global
    score_color = '#4CAF50' if health['health_score'] >= 80 else '#FF9800' if health['health_score'] >= 60 else '#F44336'
    
    health_html = f"""
    <div style='background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 15px; margin: 10px 0;'>
        <h2 style='margin: 0 0 20px 0; text-align: center;'>🏥 SANTÉ ÉCOSYSTÈME JARVIS</h2>
        <div style='text-align: center; margin: 20px 0;'>
            <div style='background: rgba(255,255,255,0.2); padding: 20px; border-radius: 15px; display: inline-block;'>
                <h3 style='margin: 0 0 10px 0;'>📊 Score de Santé Global</h3>
                <p style='margin: 0; font-size: 3em; font-weight: bold; color: {score_color};'>{health['health_score']:.0f}%</p>
                <p style='margin: 10px 0 0 0; opacity: 0.9;'>
                    {len([s for s in health['services'].values() if s['status'] == 'healthy'])}/{len(health['services'])} services actifs
                </p>
            </div>
        </div>
    </div>
    """
    
    # Services status
    services_html = """
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>🌐 STATUT SERVICES JARVIS</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;'>
    """
    
    status_colors = {
        'healthy': '#4CAF50',
        'warning': '#FF9800',
        'down': '#F44336',
        'timeout': '#9E9E9E',
        'error': '#F44336'
    }
    
    status_icons = {
        'healthy': '🟢',
        'warning': '🟡',
        'down': '🔴',
        'timeout': '⏱️',
        'error': '❌'
    }
    
    for service_name, health_data in health['services'].items():
        status = health_data['status']
        color = status_colors.get(status, '#9E9E9E')
        icon = status_icons.get(status, '❓')
        
        response_time = f"{health_data['response_time']:.3f}s" if health_data['response_time'] else "N/A"
        
        services_html += f"""
        <div style='background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid {color};'>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <div>
                    <h4 style='margin: 0; color: #333;'>{icon} {service_name}</h4>
                    <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>Temps: {response_time}</p>
                </div>
                <span style='color: {color}; font-weight: bold; font-size: 0.9em;'>{status.upper()}</span>
            </div>
        </div>
        """
    
    services_html += "</div></div>"
    
    # Métriques système
    cpu_color = '#4CAF50' if health['system']['cpu'] < 70 else '#FF9800' if health['system']['cpu'] < 90 else '#F44336'
    memory_color = '#4CAF50' if health['system']['memory'] < 70 else '#FF9800' if health['system']['memory'] < 90 else '#F44336'
    
    system_html = f"""
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>🍎 MÉTRIQUES APPLE SILICON M4</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;'>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid {cpu_color};'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>⚡ CPU</h4>
                <p style='margin: 0; font-size: 2em; font-weight: bold; color: {cpu_color};'>{health['system']['cpu']:.1f}%</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.8em;'>6P + 4E cores</p>
            </div>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid {memory_color};'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>💾 RAM</h4>
                <p style='margin: 0; font-size: 2em; font-weight: bold; color: {memory_color};'>{health['system']['memory']:.1f}%</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.8em;'>{health['system']['memory_gb']} GB</p>
            </div>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #4CAF50;'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>🖥️ Electron</h4>
                <p style='margin: 0; font-size: 2em; font-weight: bold; color: {"#4CAF50" if health["electron"] else "#F44336"};'>
                    {"✅" if health["electron"] else "❌"}
                </p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.8em;'>
                    {"Actif" if health["electron"] else "Inactif"}
                </p>
            </div>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #2196F3;'>
                <h4 style='margin: 0 0 10px 0; color: #333;'>🕒 Dernière Vérif</h4>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold; color: #2196F3;'>
                    {health['timestamp'].strftime('%H:%M:%S')}
                </p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.8em;'>Temps réel</p>
            </div>
        </div>
    </div>
    """
    
    return health_html + services_html + system_html

def generate_alerts():
    """Génère des alertes basées sur la santé du système"""
    
    health = get_system_health()
    alerts = []
    
    # Alertes services
    for service_name, health_data in health['services'].items():
        if health_data['status'] == 'down':
            alerts.append({
                'type': 'error',
                'title': f'🔴 Service Inactif',
                'message': f'{service_name} ne répond pas',
                'action': 'Redémarrer le service'
            })
        elif health_data['status'] == 'timeout':
            alerts.append({
                'type': 'warning',
                'title': f'⏱️ Service Lent',
                'message': f'{service_name} répond lentement',
                'action': 'Vérifier la charge'
            })
    
    # Alertes système
    if health['system']['cpu'] > 90:
        alerts.append({
            'type': 'error',
            'title': '🔥 CPU Surchargé',
            'message': f'CPU à {health["system"]["cpu"]:.1f}%',
            'action': 'Fermer des applications'
        })
    elif health['system']['cpu'] > 70:
        alerts.append({
            'type': 'warning',
            'title': '⚠️ CPU Élevé',
            'message': f'CPU à {health["system"]["cpu"]:.1f}%',
            'action': 'Surveiller la charge'
        })
    
    if health['system']['memory'] > 90:
        alerts.append({
            'type': 'error',
            'title': '💾 Mémoire Critique',
            'message': f'RAM à {health["system"]["memory"]:.1f}%',
            'action': 'Libérer de la mémoire'
        })
    
    if not health['electron']:
        alerts.append({
            'type': 'info',
            'title': '🖥️ Electron Inactif',
            'message': 'Application Electron non démarrée',
            'action': 'Lancer npm run final'
        })
    
    # Alertes positives
    if health['health_score'] >= 95:
        alerts.append({
            'type': 'success',
            'title': '🎉 Système Optimal',
            'message': f'Score de santé: {health["health_score"]:.0f}%',
            'action': 'Continuer le bon travail'
        })
    
    return alerts

def create_alerts_display():
    """Crée l'affichage des alertes"""
    
    alerts = generate_alerts()
    
    if not alerts:
        return """
        <div style='background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center; margin: 10px 0;'>
            <h3 style='margin: 0; color: #2e7d32;'>✅ Aucune Alerte</h3>
            <p style='margin: 10px 0 0 0; color: #666;'>Tous les systèmes fonctionnent normalement</p>
        </div>
        """
    
    alerts_html = """
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>🚨 ALERTES SYSTÈME</h3>
    """
    
    type_styles = {
        'error': {'bg': '#ffebee', 'border': '#f44336', 'icon': '🔴'},
        'warning': {'bg': '#fff3e0', 'border': '#ff9800', 'icon': '⚠️'},
        'info': {'bg': '#e3f2fd', 'border': '#2196f3', 'icon': 'ℹ️'},
        'success': {'bg': '#e8f5e8', 'border': '#4caf50', 'icon': '✅'}
    }
    
    for alert in alerts:
        style = type_styles.get(alert['type'], type_styles['info'])
        
        alerts_html += f"""
        <div style='background: {style["bg"]}; padding: 15px; border-radius: 10px; margin: 10px 0; border-left: 4px solid {style["border"]};'>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <div>
                    <h4 style='margin: 0; color: #333;'>{style["icon"]} {alert["title"]}</h4>
                    <p style='margin: 5px 0; color: #666;'>{alert["message"]}</p>
                </div>
                <button style='background: {style["border"]}; color: white; border: none; padding: 8px 12px; border-radius: 6px; font-size: 0.8em; cursor: pointer;'>
                    {alert["action"]}
                </button>
            </div>
        </div>
        """
    
    alerts_html += "</div>"
    
    return alerts_html

def create_health_monitoring_interface():
    """Interface de monitoring de santé"""
    
    with gr.Blocks(
        title="🏥 Monitoring Santé JARVIS",
        theme=gr.themes.Soft()
    ) as health_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1); color: white; padding: 25px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.2em;">🏥 MONITORING SANTÉ JARVIS</h1>
            <p style="margin: 10px 0; font-size: 1.1em;">Surveillance complète de votre écosystème</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 1em;">👤 Jean-Luc Passave | 🏥 Santé Temps Réel | 📊 Monitoring Avancé</p>
            </div>
        </div>
        """)

        with gr.Tabs():
            
            # Onglet Vue d'ensemble
            with gr.Tab("📊 Vue d'ensemble"):
                health_display = gr.HTML(
                    value=create_health_dashboard(),
                    label="Santé système"
                )
                
                refresh_health_btn = gr.Button(
                    "🔄 Actualiser Santé",
                    variant="primary",
                    size="lg"
                )
            
            # Onglet Alertes
            with gr.Tab("🚨 Alertes"):
                alerts_display = gr.HTML(
                    value=create_alerts_display(),
                    label="Alertes système"
                )
                
                refresh_alerts_btn = gr.Button(
                    "🔄 Actualiser Alertes",
                    variant="secondary"
                )
            
            # Onglet Actions Rapides
            with gr.Tab("⚡ Actions Rapides"):
                gr.HTML("<h2 style='text-align: center; color: #333;'>⚡ ACTIONS DE MAINTENANCE RAPIDE</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🚀 Redémarrages</h3>")
                        
                        restart_dashboard_btn = gr.Button("🔄 Redémarrer Dashboard", size="sm")
                        restart_electron_btn = gr.Button("🔄 Redémarrer Electron", size="sm")
                        restart_all_btn = gr.Button("🔄 Redémarrer Tout", size="sm", variant="secondary")
                        
                    with gr.Column():
                        gr.HTML("<h3>🧹 Nettoyage</h3>")
                        
                        clear_cache_btn = gr.Button("🧹 Vider Cache", size="sm")
                        optimize_memory_btn = gr.Button("💾 Optimiser Mémoire", size="sm")
                        cleanup_logs_btn = gr.Button("📋 Nettoyer Logs", size="sm")

                action_result = gr.Textbox(
                    label="Résultats Actions",
                    lines=3,
                    interactive=False
                )

        # Fonctions
        def perform_action(action_type):
            if action_type == "restart_electron":
                return "🔄 Commande redémarrage Electron envoyée"
            elif action_type == "clear_cache":
                return "🧹 Cache système vidé"
            elif action_type == "optimize_memory":
                return "💾 Optimisation mémoire effectuée"
            else:
                return f"✅ Action {action_type} exécutée"

        # Connexions
        refresh_health_btn.click(fn=create_health_dashboard, outputs=[health_display])
        refresh_alerts_btn.click(fn=create_alerts_display, outputs=[alerts_display])
        
        restart_dashboard_btn.click(fn=lambda: perform_action("restart_dashboard"), outputs=[action_result])
        restart_electron_btn.click(fn=lambda: perform_action("restart_electron"), outputs=[action_result])
        clear_cache_btn.click(fn=lambda: perform_action("clear_cache"), outputs=[action_result])
        optimize_memory_btn.click(fn=lambda: perform_action("optimize_memory"), outputs=[action_result])

        # Footer
        gr.HTML(f"""
        <div style='background: linear-gradient(45deg, #4CAF50, #8BC34A); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>
            <h3 style='margin: 0 0 10px 0;'>🏥 JEAN-LUC PASSAVE</h3>
            <p style='margin: 0; font-size: 1.1em;'>Votre écosystème JARVIS est maintenant surveillé en temps réel !</p>
            <p style='margin: 10px 0 0 0; font-size: 0.9em; opacity: 0.9;'>Santé optimale garantie - Dernière vérification: {datetime.now().strftime("%H:%M:%S")}</p>
        </div>
        """)

    return health_interface

if __name__ == "__main__":
    print("🏥 DÉMARRAGE MONITORING SANTÉ JARVIS")
    print("===================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Surveillance complète écosystème")
    print("")
    
    # Créer et lancer l'interface
    health_app = create_health_monitoring_interface()
    
    print("✅ Monitoring santé créé")
    print("🌐 Lancement sur http://localhost:7904")
    print("🏥 Surveillance temps réel disponible")
    
    health_app.launch(
        server_name="127.0.0.1",
        server_port=7904,
        share=False,
        show_error=True,
        quiet=False
    )
