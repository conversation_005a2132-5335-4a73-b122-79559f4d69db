#!/usr/bin/env python3
"""
TEST JARVIS AVEC TOKENS ADAPTATIFS THERMIQUES - JEAN-LUC PASSAVE
"""

import json
import os
import requests
import time

MEMORY_FILE = "thermal_memory_persistent.json"
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "deepseek-ai/DeepSeek-R1-0528"

def calculate_thermal_level():
    """CALCUL DU NIVEAU THERMIQUE POUR TOKENS ADAPTATIFS"""
    try:
        if not os.path.exists(MEMORY_FILE):
            return 0.3
        
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            memory = json.load(f)
        
        # Calculer le niveau thermique basé sur la mémoire
        neuron_count = len(memory.get('neuron_memories', []))
        memory_size = len(json.dumps(memory)) / (1024 * 1024)  # MB
        
        # Niveau thermique entre 0.1 et 1.0
        thermal_level = min(1.0, (neuron_count / 100) + (memory_size / 10))
        return max(0.1, thermal_level)
        
    except Exception as e:
        print(f"❌ Erreur calcul thermique: {e}")
        return 0.3

def get_adaptive_temperature():
    """TEMPÉRATURE ADAPTATIVE BASÉE SUR LE NIVEAU THERMIQUE"""
    try:
        thermal_level = calculate_thermal_level()
        adaptive_temp = 0.2 + (thermal_level * 0.8)
        return max(0.1, min(1.0, adaptive_temp))
    except Exception as e:
        return 0.7  # Valeur par défaut

def get_adaptive_max_tokens():
    """NOMBRE DE TOKENS ADAPTATIF BASÉ SUR LE NIVEAU THERMIQUE"""
    try:
        thermal_level = calculate_thermal_level()
        adaptive_tokens = int(150 + (thermal_level * 650))
        return max(100, min(1000, adaptive_tokens))
    except Exception as e:
        return 400  # Valeur par défaut

def test_jarvis_avec_tokens_adaptatifs():
    """TEST JARVIS AVEC TOKENS ADAPTATIFS"""
    print('🔥 TEST JARVIS AVEC TOKENS ADAPTATIFS THERMIQUES')
    print('=' * 60)
    
    try:
        # Calculer les tokens adaptatifs
        adaptive_temp = get_adaptive_temperature()
        adaptive_tokens = get_adaptive_max_tokens()
        thermal_level = calculate_thermal_level()
        
        print(f'📊 CONFIGURATION ADAPTATIVE:')
        print(f'   • Niveau thermique: {thermal_level:.3f}')
        print(f'   • Température: {adaptive_temp:.3f}')
        print(f'   • Tokens max: {adaptive_tokens}')
        
        # Préparer la requête avec tokens adaptatifs
        message = "Bonjour Jean-Luc ! Test des tokens adaptatifs thermiques."
        
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "system",
                    "content": f"""Tu es JARVIS, l'assistant IA révolutionnaire de Jean-Luc Passave.

TOKENS ADAPTATIFS THERMIQUES:
- Tokens maximum: {adaptive_tokens} (adapté selon mémoire thermique)
- Température: {adaptive_temp} (adapté selon niveau thermique)
- Niveau thermique: {thermal_level:.3f}

Réponds en français de manière naturelle et mentionne que tu utilises les tokens adaptatifs."""
                },
                {"role": "user", "content": message}
            ],
            "max_tokens": adaptive_tokens,
            "temperature": adaptive_temp,
            "stream": False
        }
        
        print(f'\n🚀 ENVOI REQUÊTE AVEC TOKENS ADAPTATIFS...')
        print(f'   • URL: {SERVER_URL}')
        print(f'   • Tokens: {adaptive_tokens}')
        print(f'   • Température: {adaptive_temp}')
        
        # Envoyer la requête
        start_time = time.time()
        response = requests.post(SERVER_URL, json=payload, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            jarvis_response = result['choices'][0]['message']['content']
            
            print(f'\n✅ RÉPONSE JARVIS REÇUE:')
            print(f'   • Temps: {end_time - start_time:.2f} secondes')
            print(f'   • Statut: {response.status_code}')
            print(f'\n🤖 JARVIS:')
            print(f'{jarvis_response}')
            
            # Vérifier si les tokens adaptatifs sont mentionnés
            if "adaptatif" in jarvis_response.lower() or "thermique" in jarvis_response.lower():
                print(f'\n🎯 ✅ JARVIS MENTIONNE LES TOKENS ADAPTATIFS !')
            else:
                print(f'\n⚠️ JARVIS ne mentionne pas les tokens adaptatifs')
            
            return True
            
        else:
            print(f'\n❌ ERREUR SERVEUR: {response.status_code}')
            print(f'   • Réponse: {response.text}')
            return False
            
    except requests.exceptions.Timeout:
        print(f'\n⏱️ TIMEOUT - Le serveur DeepSeek met trop de temps à répondre')
        return False
    except Exception as e:
        print(f'\n❌ ERREUR: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_jarvis_avec_tokens_adaptatifs()
    if success:
        print(f'\n🎉 TEST RÉUSSI - TOKENS ADAPTATIFS THERMIQUES FONCTIONNELS !')
    else:
        print(f'\n❌ TEST ÉCHOUÉ - Problème avec les tokens adaptatifs')
