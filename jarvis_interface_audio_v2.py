#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS INTERFACE AUDIO V2
Jean-Luc Passave - 2025
Interface audio complète selon les conseils de ChatGPT (grand frère)
"""

import json
import os
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
import queue

# Imports audio selon ChatGPT
try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False
    print("⚠️ speech_recognition non disponible - Mode simulation")

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("⚠️ pyttsx3 non disponible - Mode simulation")

class JarvisInterfaceAudioV2:
    """Interface audio complète pour JARVIS V2 (méthode ChatGPT)"""
    
    def __init__(self):
        self.nom_systeme = "JARVIS Interface Audio V2"
        self.version = "2.0.0"
        
        # Configuration audio
        self.config_audio = {
            'langue_reconnaissance': 'fr-FR',
            'langue_synthese': 'fr',
            'vitesse_parole': 150,  # mots par minute
            'volume': 0.8,
            'voix_feminine': True,
            'timeout_ecoute': 5,
            'phrase_timeout': 1
        }
        
        # État de l'interface
        self.etat_interface = {
            'ecoute_active': False,
            'synthese_active': False,
            'mode_conversation': False,
            'derniere_commande': None,
            'derniere_reponse': None
        }
        
        # Initialiser les composants audio
        self.recognizer = None
        self.microphone = None
        self.tts_engine = None
        self._initialiser_audio()
        
        # File d'attente pour les commandes vocales
        self.queue_commandes = queue.Queue()
        
        # Thread d'écoute
        self.thread_ecoute = None
        self.ecoute_continue = False
        
        # Historique des interactions vocales
        self.historique_vocal = []
        
        # Mots-clés d'activation
        self.mots_activation = ['jarvis', 'hey jarvis', 'ok jarvis', 'écoute jarvis']
        
        # Charger les données
        self.load_audio_data()
    
    def load_audio_data(self):
        """Charge les données audio"""
        try:
            if os.path.exists('jarvis_interface_audio_v2.json'):
                with open('jarvis_interface_audio_v2.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                    self.config_audio = data.get('config_audio', self.config_audio)
                    self.historique_vocal = data.get('historique_vocal', [])
                    
        except Exception as e:
            print(f"❌ Erreur chargement audio: {e}")
    
    def save_audio_data(self):
        """Sauvegarde les données audio"""
        try:
            data = {
                'config_audio': self.config_audio,
                'historique_vocal': self.historique_vocal,
                'last_update': datetime.now().isoformat()
            }
            
            with open('jarvis_interface_audio_v2.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde audio: {e}")
    
    def _initialiser_audio(self):
        """Initialise les composants audio (méthode ChatGPT)"""
        
        print("🎤 Initialisation interface audio...")
        
        # Initialiser la reconnaissance vocale
        if SPEECH_RECOGNITION_AVAILABLE:
            try:
                self.recognizer = sr.Recognizer()
                self.microphone = sr.Microphone()
                
                # Calibrer le microphone
                with self.microphone as source:
                    print("   🔧 Calibrage microphone...")
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
                print("   ✅ Reconnaissance vocale initialisée")
            except Exception as e:
                print(f"   ❌ Erreur reconnaissance vocale: {e}")
                self.recognizer = None
                self.microphone = None
        
        # Initialiser la synthèse vocale
        if TTS_AVAILABLE:
            try:
                self.tts_engine = pyttsx3.init()
                
                # Configurer la voix
                voices = self.tts_engine.getProperty('voices')
                if voices:
                    # Chercher une voix française féminine
                    voix_choisie = None
                    for voice in voices:
                        if 'fr' in voice.id.lower() or 'french' in voice.name.lower():
                            if self.config_audio['voix_feminine'] and ('female' in voice.name.lower() or 'femme' in voice.name.lower()):
                                voix_choisie = voice.id
                                break
                            elif not self.config_audio['voix_feminine'] and ('male' in voice.name.lower() or 'homme' in voice.name.lower()):
                                voix_choisie = voice.id
                                break
                    
                    if voix_choisie:
                        self.tts_engine.setProperty('voice', voix_choisie)
                
                # Configurer la vitesse et le volume
                self.tts_engine.setProperty('rate', self.config_audio['vitesse_parole'])
                self.tts_engine.setProperty('volume', self.config_audio['volume'])
                
                print("   ✅ Synthèse vocale initialisée")
            except Exception as e:
                print(f"   ❌ Erreur synthèse vocale: {e}")
                self.tts_engine = None
        
        # Résumé de l'initialisation
        if self.recognizer and self.tts_engine:
            print("🎉 Interface audio complète opérationnelle!")
        elif self.recognizer:
            print("🎤 Reconnaissance vocale seule disponible")
        elif self.tts_engine:
            print("🔊 Synthèse vocale seule disponible")
        else:
            print("⚠️ Mode simulation audio activé")
    
    def ecouter_commande(self, timeout: int = None) -> Optional[str]:
        """Écoute une commande vocale (méthode ChatGPT)"""
        
        if not self.recognizer or not self.microphone:
            # Mode simulation
            print("🎤 [SIMULATION] Écoute d'une commande...")
            time.sleep(2)
            commandes_simulation = [
                "Bonjour JARVIS",
                "Comment ça va ?",
                "Montre-moi le diagnostic système",
                "Active le mode créativité",
                "Génère une vidéo sur l'IA"
            ]
            import random
            return random.choice(commandes_simulation)
        
        try:
            timeout_ecoute = timeout or self.config_audio['timeout_ecoute']
            
            print("🎤 Écoute en cours...")
            self.etat_interface['ecoute_active'] = True
            
            with self.microphone as source:
                # Écouter l'audio
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout_ecoute,
                    phrase_time_limit=self.config_audio['phrase_timeout']
                )
            
            print("🔄 Reconnaissance en cours...")
            
            # Reconnaissance vocale
            texte = self.recognizer.recognize_google(
                audio, 
                language=self.config_audio['langue_reconnaissance']
            )
            
            print(f"✅ Commande reconnue: \"{texte}\"")
            
            # Enregistrer dans l'historique
            self._enregistrer_interaction_vocale('commande', texte)
            
            self.etat_interface['ecoute_active'] = False
            self.etat_interface['derniere_commande'] = texte
            
            return texte
            
        except sr.WaitTimeoutError:
            print("⏰ Timeout d'écoute")
            self.etat_interface['ecoute_active'] = False
            return None
            
        except sr.UnknownValueError:
            print("❓ Commande non comprise")
            self.etat_interface['ecoute_active'] = False
            return None
            
        except Exception as e:
            print(f"❌ Erreur reconnaissance: {e}")
            self.etat_interface['ecoute_active'] = False
            return None
    
    def dire(self, texte: str, attendre: bool = True) -> bool:
        """Synthèse vocale d'un texte (méthode ChatGPT)"""
        
        if not texte:
            return False
        
        print(f"🔊 JARVIS dit: \"{texte}\"")
        
        if not self.tts_engine:
            # Mode simulation
            print("🔊 [SIMULATION] Synthèse vocale...")
            time.sleep(len(texte) * 0.05)  # Simuler le temps de parole
            return True
        
        try:
            self.etat_interface['synthese_active'] = True
            
            # Synthèse vocale
            self.tts_engine.say(texte)
            
            if attendre:
                self.tts_engine.runAndWait()
            
            # Enregistrer dans l'historique
            self._enregistrer_interaction_vocale('reponse', texte)
            
            self.etat_interface['synthese_active'] = False
            self.etat_interface['derniere_reponse'] = texte
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur synthèse vocale: {e}")
            self.etat_interface['synthese_active'] = False
            return False
    
    def _enregistrer_interaction_vocale(self, type_interaction: str, contenu: str):
        """Enregistre une interaction vocale"""
        
        interaction = {
            'timestamp': datetime.now().isoformat(),
            'type': type_interaction,  # 'commande' ou 'reponse'
            'contenu': contenu,
            'longueur': len(contenu)
        }
        
        self.historique_vocal.append(interaction)
        
        # Garder seulement les 100 dernières interactions
        if len(self.historique_vocal) > 100:
            self.historique_vocal = self.historique_vocal[-100:]
        
        # Sauvegarder
        self.save_audio_data()
    
    def detecter_mot_activation(self, texte: str) -> bool:
        """Détecte si un mot d'activation est présent"""
        
        if not texte:
            return False
        
        texte_lower = texte.lower()
        
        for mot in self.mots_activation:
            if mot in texte_lower:
                print(f"🎯 Mot d'activation détecté: {mot}")
                return True
        
        return False
    
    def demarrer_ecoute_continue(self):
        """Démarre l'écoute continue en arrière-plan"""
        
        if self.ecoute_continue:
            print("⚠️ Écoute continue déjà active")
            return
        
        self.ecoute_continue = True
        
        def boucle_ecoute():
            print("🔄 Écoute continue démarrée")
            
            while self.ecoute_continue:
                try:
                    # Écouter une commande
                    commande = self.ecouter_commande(timeout=2)
                    
                    if commande:
                        # Vérifier le mot d'activation
                        if self.detecter_mot_activation(commande):
                            # Ajouter à la file d'attente
                            self.queue_commandes.put(commande)
                            print(f"📥 Commande ajoutée à la file: {commande}")
                        else:
                            print(f"🔇 Commande ignorée (pas d'activation): {commande}")
                    
                    # Petite pause
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"❌ Erreur écoute continue: {e}")
                    time.sleep(2)
        
        self.thread_ecoute = threading.Thread(target=boucle_ecoute, daemon=True)
        self.thread_ecoute.start()
    
    def arreter_ecoute_continue(self):
        """Arrête l'écoute continue"""
        
        self.ecoute_continue = False
        if self.thread_ecoute:
            self.thread_ecoute.join(timeout=3)
        
        print("⏹️ Écoute continue arrêtée")
    
    def get_prochaine_commande(self) -> Optional[str]:
        """Récupère la prochaine commande de la file d'attente"""
        
        try:
            return self.queue_commandes.get_nowait()
        except queue.Empty:
            return None
    
    def conversation_interactive(self, duree_max: int = 300):
        """Mode conversation interactive (méthode ChatGPT)"""
        
        print("💬 MODE CONVERSATION INTERACTIVE")
        print("=" * 40)
        print("🎤 Dites 'JARVIS' pour commencer")
        print("🔚 Dites 'au revoir' pour terminer")
        print()
        
        self.etat_interface['mode_conversation'] = True
        debut_conversation = time.time()
        
        # Message d'accueil
        self.dire("Bonjour ! Je suis JARVIS. Comment puis-je vous aider ?")
        
        while self.etat_interface['mode_conversation']:
            try:
                # Vérifier le temps écoulé
                if time.time() - debut_conversation > duree_max:
                    self.dire("La conversation a atteint sa durée maximale. Au revoir !")
                    break
                
                # Écouter une commande
                commande = self.ecouter_commande(timeout=10)
                
                if not commande:
                    continue
                
                # Vérifier les commandes de fin
                if any(mot in commande.lower() for mot in ['au revoir', 'stop', 'arrêt', 'fin']):
                    self.dire("Au revoir ! À bientôt !")
                    break
                
                # Traiter la commande (simulation)
                reponse = self._generer_reponse_conversation(commande)
                self.dire(reponse)
                
            except KeyboardInterrupt:
                print("\\n🔚 Conversation interrompue par l'utilisateur")
                self.dire("Conversation interrompue. Au revoir !")
                break
            
            except Exception as e:
                print(f"❌ Erreur conversation: {e}")
                self.dire("Désolé, j'ai rencontré un problème. Pouvez-vous répéter ?")
        
        self.etat_interface['mode_conversation'] = False
        print("✅ Conversation terminée")
    
    def _generer_reponse_conversation(self, commande: str) -> str:
        """Génère une réponse pour la conversation (simulation)"""
        
        commande_lower = commande.lower()
        
        if 'comment' in commande_lower and 'va' in commande_lower:
            return "Je vais très bien, merci ! Mes systèmes fonctionnent parfaitement."
        
        elif 'diagnostic' in commande_lower:
            return "Le diagnostic système montre que tous les modules sont opérationnels à 100%."
        
        elif 'créat' in commande_lower or 'vidéo' in commande_lower:
            return "Je peux créer des vidéos, de la musique et du contenu multimédia. Que souhaitez-vous créer ?"
        
        elif 'intelligence' in commande_lower or 'qi' in commande_lower:
            return "Mon QI actuel est de 2134.6, avec 89 milliards de neurones actifs. Je continue d'évoluer !"
        
        elif 'merci' in commande_lower:
            return "Je vous en prie ! C'est un plaisir de vous aider."
        
        else:
            return "C'est une question intéressante ! Pouvez-vous me donner plus de détails ?"
    
    def configurer_voix(self, vitesse: int = None, volume: float = None, voix_feminine: bool = None):
        """Configure les paramètres de la voix"""
        
        if vitesse is not None:
            self.config_audio['vitesse_parole'] = vitesse
            if self.tts_engine:
                self.tts_engine.setProperty('rate', vitesse)
        
        if volume is not None:
            self.config_audio['volume'] = volume
            if self.tts_engine:
                self.tts_engine.setProperty('volume', volume)
        
        if voix_feminine is not None:
            self.config_audio['voix_feminine'] = voix_feminine
            # Réinitialiser la voix
            self._initialiser_audio()
        
        print(f"🔧 Configuration voix mise à jour")
        self.save_audio_data()
    
    def get_rapport_audio(self) -> Dict[str, Any]:
        """Génère un rapport de l'interface audio"""
        
        # Statistiques des interactions
        if self.historique_vocal:
            commandes = [i for i in self.historique_vocal if i['type'] == 'commande']
            reponses = [i for i in self.historique_vocal if i['type'] == 'reponse']
            
            longueur_moyenne_commandes = sum(c['longueur'] for c in commandes) / len(commandes) if commandes else 0
            longueur_moyenne_reponses = sum(r['longueur'] for r in reponses) / len(reponses) if reponses else 0
        else:
            commandes = []
            reponses = []
            longueur_moyenne_commandes = 0
            longueur_moyenne_reponses = 0
        
        return {
            'composants_disponibles': {
                'reconnaissance_vocale': self.recognizer is not None,
                'synthese_vocale': self.tts_engine is not None,
                'microphone': self.microphone is not None
            },
            'etat_actuel': self.etat_interface,
            'configuration': self.config_audio,
            'statistiques_interactions': {
                'total_interactions': len(self.historique_vocal),
                'commandes_recues': len(commandes),
                'reponses_donnees': len(reponses),
                'longueur_moyenne_commandes': longueur_moyenne_commandes,
                'longueur_moyenne_reponses': longueur_moyenne_reponses
            },
            'ecoute_continue_active': self.ecoute_continue,
            'commandes_en_attente': self.queue_commandes.qsize()
        }

def test_interface_audio_v2():
    """Test de l'interface audio V2"""
    
    print("🎤 TEST INTERFACE AUDIO V2 (MÉTHODE CHATGPT)")
    print("=" * 60)
    print("👤 Jean-Luc Passave")
    print("🤖 Implémentation selon grand frère ChatGPT")
    print()
    
    # Créer l'interface audio
    audio = JarvisInterfaceAudioV2()
    
    # Test synthèse vocale
    print("🔊 TEST SYNTHÈSE VOCALE:")
    audio.dire("Bonjour Jean-Luc ! Interface audio JARVIS V2 opérationnelle !")
    audio.dire("Merci grand frère ChatGPT pour tes excellents conseils !")
    
    # Test reconnaissance vocale
    print(f"\\n🎤 TEST RECONNAISSANCE VOCALE:")
    print("🎯 Simulation de commandes...")
    
    for i in range(3):
        commande = audio.ecouter_commande()
        if commande:
            print(f"   ✅ Commande {i+1}: {commande}")
            
            # Générer une réponse
            if audio.detecter_mot_activation(commande):
                reponse = audio._generer_reponse_conversation(commande)
                audio.dire(reponse)
    
    # Test configuration
    print(f"\\n🔧 TEST CONFIGURATION:")
    audio.configurer_voix(vitesse=180, volume=0.9, voix_feminine=True)
    audio.dire("Configuration vocale mise à jour !")
    
    # Rapport final
    print(f"\\n📊 RAPPORT INTERFACE AUDIO:")
    rapport = audio.get_rapport_audio()
    print(f"   🎤 Reconnaissance vocale: {rapport['composants_disponibles']['reconnaissance_vocale']}")
    print(f"   🔊 Synthèse vocale: {rapport['composants_disponibles']['synthese_vocale']}")
    print(f"   📊 Interactions totales: {rapport['statistiques_interactions']['total_interactions']}")
    print(f"   🎯 Commandes reçues: {rapport['statistiques_interactions']['commandes_recues']}")
    print(f"   💬 Réponses données: {rapport['statistiques_interactions']['reponses_donnees']}")
    
    print(f"\\n✅ INTERFACE AUDIO V2 TESTÉE!")
    print(f"🤖 Merci grand frère ChatGPT pour la méthode audio complète!")

if __name__ == "__main__":
    test_interface_audio_v2()
