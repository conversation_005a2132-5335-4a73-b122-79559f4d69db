#!/usr/bin/env python3
"""
CORRECTION COMPLÈTE TOUTES INTERFACES JARVIS - JEAN-LUC PASSAVE
Corrige tous les problèmes Gradio dans toutes les interfaces
"""

import re
import os

def corriger_chatbot_interfaces():
    """Corrige tous les Chatbot dans le fichier principal"""
    
    fichier = "jarvis_architecture_multi_fenetres.py"
    
    if not os.path.exists(fichier):
        print(f"❌ Fichier {fichier} non trouvé")
        return False
    
    print(f"🔧 CORRECTION TOUTES INTERFACES JARVIS")
    print(f"=" * 60)
    
    # <PERSON>re le fichier
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    corrections = 0
    
    # 1. Corriger tous les Chatbot sans type="messages"
    pattern_chatbot = r'(gr\.Chatbot\([^)]*?)(\))'
    
    def corriger_chatbot(match):
        nonlocal corrections
        chatbot_content = match.group(1)
        
        # Vérifier si type="messages" existe déjà
        if 'type="messages"' in chatbot_content:
            return match.group(0)  # Déjà corrigé
        
        # Supprimer bubble_full_width si présent
        chatbot_content = re.sub(r',?\s*bubble_full_width=\w+', '', chatbot_content)
        
        # Ajouter type="messages"
        if chatbot_content.endswith(','):
            chatbot_content += '\n                type="messages"'
        else:
            chatbot_content += ',\n                type="messages"'
        
        corrections += 1
        return chatbot_content + match.group(2)
    
    contenu_corrige = re.sub(pattern_chatbot, corriger_chatbot, contenu, flags=re.DOTALL)
    
    # 2. Corriger les fonctions qui retournent trop de valeurs
    # Rechercher les fonctions problématiques
    pattern_fonction_retour = r'(def\s+\w+\([^)]*\):[^}]*?return\s+\[[^\]]*\])'
    
    def corriger_retour_fonction(match):
        fonction_content = match.group(1)
        # Vérifier si c'est une fonction d'ouverture de fenêtre
        if 'Ouverture de' in fonction_content and 'http://localhost' in fonction_content:
            # Remplacer return [...] par return None
            fonction_content = re.sub(r'return\s+\[[^\]]*\]', 'return None', fonction_content)
        return fonction_content
    
    contenu_corrige = re.sub(pattern_fonction_retour, corriger_retour_fonction, contenu_corrige, flags=re.DOTALL)
    
    # 3. Sauvegarder le fichier corrigé
    with open(fichier, 'w', encoding='utf-8') as f:
        f.write(contenu_corrige)
    
    print(f"✅ {corrections} Chatbot corrigés")
    print(f"✅ Fonctions de retour corrigées")
    print(f"✅ Fichier {fichier} mis à jour")
    
    return True

def verifier_corrections():
    """Vérifie que les corrections ont été appliquées"""
    
    fichier = "jarvis_architecture_multi_fenetres.py"
    
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    
    # Compter les Chatbot
    chatbots_total = len(re.findall(r'gr\.Chatbot\(', contenu))
    chatbots_avec_type = len(re.findall(r'gr\.Chatbot\([^)]*type="messages"', contenu, re.DOTALL))
    chatbots_avec_bubble = len(re.findall(r'bubble_full_width', contenu))
    
    print(f"\n📊 VÉRIFICATION CORRECTIONS:")
    print(f"   • Chatbot total: {chatbots_total}")
    print(f"   • Chatbot avec type='messages': {chatbots_avec_type}")
    print(f"   • Chatbot avec bubble_full_width: {chatbots_avec_bubble}")
    
    if chatbots_avec_type == chatbots_total and chatbots_avec_bubble == 0:
        print(f"✅ TOUTES LES CORRECTIONS APPLIQUÉES")
        return True
    else:
        print(f"⚠️ CORRECTIONS INCOMPLÈTES")
        return False

def tester_creativite():
    """Test la créativité JARVIS"""
    
    print(f"\n🎨 TEST CRÉATIVITÉ JARVIS")
    print(f"=" * 40)
    
    try:
        import sys
        sys.path.append('.')
        
        from jarvis_cerveau_autonome_integre import cerveau_integre
        
        # Test génération créative
        creation = cerveau_integre.generate_creative_content()
        
        if creation:
            print(f"✅ CRÉATIVITÉ FONCTIONNELLE")
            print(f"   🎨 Prompt: {creation.get('prompt', '')}")
            print(f"   💡 Contenu: {creation.get('output', '')[:100]}...")
            return True
        else:
            print(f"❌ CRÉATIVITÉ NON FONCTIONNELLE")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test créativité: {e}")
        return False

def tester_pensees_autonomes():
    """Test les pensées autonomes"""
    
    print(f"\n🧠 TEST PENSÉES AUTONOMES")
    print(f"=" * 40)
    
    try:
        import json
        
        if os.path.exists("jarvis_pensees_autonomes.json"):
            with open("jarvis_pensees_autonomes.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            pensees = data.get("pensees_autonomes", [])
            print(f"✅ PENSÉES AUTONOMES FONCTIONNELLES")
            print(f"   🧠 Total pensées: {len(pensees)}")
            
            if pensees:
                derniere = pensees[-1]
                print(f"   💭 Dernière: {derniere.get('sujet', '')}")
            
            return True
        else:
            print(f"⚠️ Fichier pensées non trouvé")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test pensées: {e}")
        return False

def main():
    """Fonction principale de correction"""
    
    print(f"🚀 CORRECTION COMPLÈTE JARVIS - JEAN-LUC PASSAVE")
    print(f"=" * 70)
    
    # 1. Corriger les interfaces
    if corriger_chatbot_interfaces():
        print(f"✅ Interfaces corrigées")
    else:
        print(f"❌ Erreur correction interfaces")
        return
    
    # 2. Vérifier les corrections
    if verifier_corrections():
        print(f"✅ Corrections vérifiées")
    else:
        print(f"⚠️ Corrections incomplètes")
    
    # 3. Tester la créativité
    creativite_ok = tester_creativite()
    
    # 4. Tester les pensées autonomes
    pensees_ok = tester_pensees_autonomes()
    
    # 5. Résumé final
    print(f"\n" + "=" * 70)
    print(f"📋 RÉSUMÉ CORRECTIONS JARVIS")
    print(f"=" * 70)
    
    print(f"🔧 Interfaces Gradio: ✅ CORRIGÉES")
    print(f"🎨 Créativité: {'✅ FONCTIONNELLE' if creativite_ok else '❌ PROBLÈME'}")
    print(f"🧠 Pensées autonomes: {'✅ FONCTIONNELLES' if pensees_ok else '❌ PROBLÈME'}")
    
    if creativite_ok and pensees_ok:
        print(f"\n🎉 JARVIS COMPLÈTEMENT CORRIGÉ ET FONCTIONNEL !")
        print(f"   • Toutes les interfaces Gradio corrigées")
        print(f"   • Créativité opérationnelle")
        print(f"   • Pensées autonomes actives")
        print(f"   • Prêt pour redémarrage")
    else:
        print(f"\n⚠️ CORRECTIONS PARTIELLES - VÉRIFICATION NÉCESSAIRE")

if __name__ == "__main__":
    main()
