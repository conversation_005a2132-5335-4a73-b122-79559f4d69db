#!/bin/bash

echo "🚀 DEEPSEEK R1 8B - ULTRA TURBO POUR JARVIS"
echo "============================================"
echo "Configuration spéciale pour réponses RAPIDES"
echo ""

# Configuration optimisée pour JARVIS
MODEL_PATH="/Volumes/seagate/CLAUDE_THERMAL_MEMORY_SYSTEM/DeepSeek-R1-0528-Qwen3-8B-Q3_K_L.gguf"
PORT=8000

# Tuer les processus existants
echo "🔄 Nettoyage des processus existants..."
lsof -ti:$PORT | xargs kill -9 2>/dev/null || true
sleep 3

echo "⚡ CONFIGURATION ULTRA-TURBO:"
echo "  • GPU Layers: 36/36 (TOUT sur GPU)"
echo "  • Context: 2048 (optimisé pour vitesse)"
echo "  • Batch: 256 (rapide)"
echo "  • Threads: 6 (P-cores M4)"
echo "  • Timeout: DÉSACTIVÉ"
echo "  • Slots: 1 (dédi<PERSON>)"
echo ""

# Lancement ULTRA-TURBO
llama-server \
    --model "$MODEL_PATH" \
    --host 0.0.0.0 \
    --port $PORT \
    --n-gpu-layers 36 \
    --ctx-size 2048 \
    --batch-size 256 \
    --ubatch-size 128 \
    --threads 6 \
    --threads-batch 6 \
    --flash-attn \
    --cont-batching \
    --parallel 1 \
    --cache-type-k f16 \
    --cache-type-v f16 \
    --mlock \
    --no-warmup \
    --verbose
