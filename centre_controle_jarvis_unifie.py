#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Centre de Contrôle JARVIS Unifié
<PERSON> - 2025
Centre de contrôle principal pour tout l'écosystème JARVIS
"""

import gradio as gr
import subprocess
import requests
import psutil
import os
import webbrowser
from datetime import datetime

# État global du système
system_state = {
    'jarvis_running': False,
    'electron_running': False,
    'deepseek_running': False,
    'last_check': None
}

def check_system_status():
    """Vérifie l'état de tous les systèmes JARVIS"""
    global system_state
    
    status = {
        'jarvis_main': False,
        'electron_app': False,
        'deepseek_server': False,
        'interfaces': {},
        'system_metrics': {}
    }
    
    # Vérifier JARVIS principal
    try:
        response = requests.get('http://localhost:7867', timeout=3)
        status['jarvis_main'] = response.status_code == 200
    except:
        status['jarvis_main'] = False
    
    # Vérifier application Electron
    electron_processes = [p for p in psutil.process_iter(['name']) if 'electron' in p.info['name'].lower()]
    status['electron_app'] = len(electron_processes) > 0
    
    # Vérifier serveur DeepSeek
    try:
        response = requests.get('http://localhost:8000/health', timeout=3)
        status['deepseek_server'] = response.status_code == 200
    except:
        status['deepseek_server'] = False
    
    # Vérifier interfaces
    interfaces = [
        ('Communication', 7866),
        ('Dashboard', 7867),
        ('Éditeur', 7868),
        ('Pensées', 7869),
        ('Vocale', 7879),
        ('Multi-Agents', 7880)
    ]
    
    for name, port in interfaces:
        try:
            response = requests.get(f'http://localhost:{port}', timeout=2)
            status['interfaces'][name] = response.status_code == 200
        except:
            status['interfaces'][name] = False
    
    # Métriques système
    status['system_metrics'] = {
        'cpu_percent': psutil.cpu_percent(),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_percent': psutil.disk_usage('/').percent
    }
    
    system_state.update(status)
    system_state['last_check'] = datetime.now().strftime("%H:%M:%S")
    
    return status

def launch_jarvis_main():
    """Lance JARVIS principal"""
    try:
        cmd = ["python3", "jarvis_architecture_multi_fenetres.py"]
        process = subprocess.Popen(
            cmd, 
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        return f"✅ JARVIS principal lancé (PID: {process.pid})"
    except Exception as e:
        return f"❌ Erreur lancement JARVIS: {str(e)}"

def launch_electron_app():
    """Lance l'application Electron"""
    try:
        cmd = ["npm", "run", "final"]
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        return f"✅ Application Electron lancée (PID: {process.pid})"
    except Exception as e:
        return f"❌ Erreur lancement Electron: {str(e)}"

def stop_all_services():
    """Arrête tous les services JARVIS"""
    try:
        # Arrêter les processus Python JARVIS
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'jarvis' in cmdline.lower():
                        proc.terminate()
            except:
                continue
        
        # Arrêter les processus Electron
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if 'electron' in proc.info['name'].lower():
                    proc.terminate()
            except:
                continue
        
        return "✅ Tous les services JARVIS arrêtés"
    except Exception as e:
        return f"❌ Erreur arrêt services: {str(e)}"

def generate_status_html():
    """Génère le HTML de statut du système"""
    status = check_system_status()
    
    # Statut principal
    jarvis_status = "🟢 ACTIF" if status['jarvis_main'] else "🔴 INACTIF"
    electron_status = "🟢 ACTIF" if status['electron_app'] else "🔴 INACTIF"
    deepseek_status = "🟢 ACTIF" if status['deepseek_server'] else "🔴 INACTIF"
    
    # Métriques système
    cpu = status['system_metrics']['cpu_percent']
    memory = status['system_metrics']['memory_percent']
    disk = status['system_metrics']['disk_percent']
    
    cpu_color = "#4CAF50" if cpu < 70 else "#FF9800" if cpu < 90 else "#F44336"
    memory_color = "#4CAF50" if memory < 70 else "#FF9800" if memory < 90 else "#F44336"
    disk_color = "#4CAF50" if disk < 70 else "#FF9800" if disk < 90 else "#F44336"
    
    html = f"""
    <div style='background: #f8f9fa; padding: 25px; border-radius: 15px; margin: 20px 0;'>
        <h2 style='margin: 0 0 20px 0; color: #333; text-align: center;'>🎯 STATUT SYSTÈME JARVIS</h2>
        
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;'>
            <div style='background: white; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid {"#4CAF50" if status["jarvis_main"] else "#F44336"};'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>🤖 JARVIS Principal</h3>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{jarvis_status}</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Port 7867</p>
            </div>
            
            <div style='background: white; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid {"#4CAF50" if status["electron_app"] else "#F44336"};'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>🖥️ Application Electron</h3>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{electron_status}</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Interface native</p>
            </div>
            
            <div style='background: white; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid {"#4CAF50" if status["deepseek_server"] else "#F44336"};'>
                <h3 style='margin: 0 0 10px 0; color: #333;'>🧠 DeepSeek R1</h3>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{deepseek_status}</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>Port 8000</p>
            </div>
        </div>
        
        <h3 style='margin: 30px 0 15px 0; color: #333;'>🌐 Interfaces JARVIS</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>
    """
    
    for name, active in status['interfaces'].items():
        status_icon = "🟢" if active else "🔴"
        status_text = "ACTIF" if active else "INACTIF"
        border_color = "#4CAF50" if active else "#F44336"
        
        html += f"""
        <div style='background: white; padding: 15px; border-radius: 8px; text-align: center; border-left: 3px solid {border_color};'>
            <h4 style='margin: 0 0 5px 0; color: #333;'>{name}</h4>
            <p style='margin: 0; font-weight: bold;'>{status_icon} {status_text}</p>
        </div>
        """
    
    html += f"""
        </div>
        
        <h3 style='margin: 30px 0 15px 0; color: #333;'>🍎 Métriques Apple M4</h3>
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>
            <div style='background: white; padding: 15px; border-radius: 8px; text-align: center; border-left: 3px solid {cpu_color};'>
                <h4 style='margin: 0 0 5px 0; color: #333;'>⚡ CPU</h4>
                <p style='margin: 0; font-weight: bold; color: {cpu_color};'>{cpu:.1f}%</p>
            </div>
            
            <div style='background: white; padding: 15px; border-radius: 8px; text-align: center; border-left: 3px solid {memory_color};'>
                <h4 style='margin: 0 0 5px 0; color: #333;'>💾 RAM</h4>
                <p style='margin: 0; font-weight: bold; color: {memory_color};'>{memory:.1f}%</p>
            </div>
            
            <div style='background: white; padding: 15px; border-radius: 8px; text-align: center; border-left: 3px solid {disk_color};'>
                <h4 style='margin: 0 0 5px 0; color: #333;'>💿 Disque</h4>
                <p style='margin: 0; font-weight: bold; color: {disk_color};'>{disk:.1f}%</p>
            </div>
        </div>
        
        <p style='margin: 20px 0 0 0; text-align: center; color: #666; font-size: 0.9em;'>
            Dernière vérification: {system_state['last_check']}
        </p>
    </div>
    """
    
    return html

def create_control_center():
    """Crée le centre de contrôle unifié"""
    
    with gr.Blocks(
        title="🎯 Centre de Contrôle JARVIS Unifié",
        theme=gr.themes.Soft()
    ) as control_center:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #1a237e, #3f51b5, #2196f3, #00bcd4); color: white; padding: 30px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.5em;">🎯 CENTRE DE CONTRÔLE JARVIS UNIFIÉ</h1>
            <h2 style="margin: 10px 0; font-size: 1.5em;">Gestion Complète de l'Écosystème JARVIS</h2>
            <p style="margin: 10px 0; font-size: 1.2em;"><strong>Jean-Luc Passave</strong> - Apple Silicon M4 Optimisé</p>
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px; margin: 15px 0;">
                <p style="margin: 0; font-size: 1.1em;">🤖 JARVIS Principal | 🖥️ Electron App | 🧠 DeepSeek R1 | 📊 Monitoring Temps Réel</p>
            </div>
        </div>
        """)

        # Statut système
        status_html = gr.HTML(
            value="<p>Chargement du statut système...</p>",
            label="Statut Système"
        )

        # Contrôles principaux
        gr.HTML("<h2 style='text-align: center; color: #333; margin: 30px 0;'>🚀 CONTRÔLES PRINCIPAUX</h2>")
        
        with gr.Row():
            launch_jarvis_btn = gr.Button("🤖 LANCER JARVIS", variant="primary", size="lg")
            launch_electron_btn = gr.Button("🖥️ LANCER ELECTRON", variant="primary", size="lg")
            refresh_status_btn = gr.Button("🔄 ACTUALISER STATUT", variant="secondary", size="lg")
            stop_all_btn = gr.Button("🛑 ARRÊTER TOUT", variant="stop", size="lg")

        # Résultats des actions
        action_result = gr.Textbox(
            label="Résultats des Actions",
            lines=3,
            interactive=False
        )

        # Accès rapide aux interfaces
        gr.HTML("<hr style='margin: 40px 0; border: 2px solid #e0e0e0;'>")
        gr.HTML("<h2 style='text-align: center; color: #333; margin: 30px 0;'>🌐 ACCÈS RAPIDE INTERFACES</h2>")

        with gr.Row():
            with gr.Column():
                gr.HTML("<h3 style='color: #666;'>🏠 Interfaces Principales</h3>")
                dashboard_btn = gr.Button("🏠 Dashboard (7867)", size="sm")
                communication_btn = gr.Button("💬 Communication (7866)", size="sm")
                editor_btn = gr.Button("💻 Éditeur (7868)", size="sm")
                
            with gr.Column():
                gr.HTML("<h3 style='color: #666;'>🎯 Interfaces Spécialisées</h3>")
                voice_btn = gr.Button("🎤 Vocale (7879)", size="sm")
                agents_btn = gr.Button("🤖 Multi-Agents (7880)", size="sm")
                monitoring_btn = gr.Button("📊 Monitoring (7894)", size="sm")

        # Outils de maintenance
        gr.HTML("<hr style='margin: 40px 0; border: 2px solid #e0e0e0;'>")
        gr.HTML("<h2 style='text-align: center; color: #333; margin: 30px 0;'>🔧 OUTILS DE MAINTENANCE</h2>")

        with gr.Row():
            test_memory_btn = gr.Button("🧠 Test Mémoire Thermique", size="sm")
            test_agents_btn = gr.Button("🧪 Test Agents", size="sm")
            validation_btn = gr.Button("✅ Validation Système", size="sm")
            cleanup_btn = gr.Button("🧹 Nettoyage", size="sm")

        # Fonctions de connexion
        def open_url(url):
            webbrowser.open(url)
            return f"✅ Interface ouverte: {url}"

        # Connexions des boutons
        launch_jarvis_btn.click(fn=launch_jarvis_main, outputs=[action_result])
        launch_electron_btn.click(fn=launch_electron_app, outputs=[action_result])
        stop_all_btn.click(fn=stop_all_services, outputs=[action_result])
        refresh_status_btn.click(fn=generate_status_html, outputs=[status_html])

        # Interfaces
        dashboard_btn.click(fn=lambda: open_url("http://localhost:7867"), outputs=[action_result])
        communication_btn.click(fn=lambda: open_url("http://localhost:7866"), outputs=[action_result])
        editor_btn.click(fn=lambda: open_url("http://localhost:7868"), outputs=[action_result])
        voice_btn.click(fn=lambda: open_url("http://localhost:7879"), outputs=[action_result])
        agents_btn.click(fn=lambda: open_url("http://localhost:7880"), outputs=[action_result])
        monitoring_btn.click(fn=lambda: open_url("http://localhost:7894"), outputs=[action_result])

        # Outils
        test_memory_btn.click(fn=lambda: open_url("http://localhost:7896"), outputs=[action_result])
        test_agents_btn.click(fn=lambda: open_url("http://localhost:7893"), outputs=[action_result])

        # Initialiser le statut
        control_center.load(fn=generate_status_html, outputs=[status_html])

    return control_center

if __name__ == "__main__":
    print("🎯 DÉMARRAGE CENTRE DE CONTRÔLE JARVIS UNIFIÉ")
    print("============================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Gestion complète écosystème JARVIS")
    print("")
    
    # Créer et lancer le centre de contrôle
    control_app = create_control_center()
    
    print("✅ Centre de contrôle créé")
    print("🌐 Lancement sur http://localhost:7897")
    print("🎯 Contrôle unifié JARVIS disponible")
    
    control_app.launch(
        server_name="127.0.0.1",
        server_port=7897,
        share=False,
        show_error=True,
        quiet=False
    )
