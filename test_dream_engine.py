#!/usr/bin/env python3
"""
TEST DREAM ENGINE THERMIQUE
Test immédiat du moteur de rêves productifs
"""

import sys
sys.path.append('.')

from jarvis_dream_engine_thermique import DreamEngineThermique
import time

print("🌙 TEST DREAM ENGINE THERMIQUE")
print("=" * 40)

# Créer instance
dream_engine = DreamEngineThermique()

print("🚀 FORCER MODE RÊVE TURBO + INTERNET POUR TEST")

# Forcer le mode rêve immédiatement
dream_engine.sleep_mode = True
dream_engine.active = True

# Générer 2 rêves TURBO avec Internet
for i in range(2):
    print(f"\n🚀 GÉNÉRATION RÊVE TURBO + INTERNET #{i+1}")

    start_time = time.time()
    dream = dream_engine.generate_productive_dream_turbo()
    generation_time = time.time() - start_time

    if dream:
        print(f"✅ RÊVE TURBO #{i+1} CRÉÉ en {generation_time:.1f}s")
        print(f"   Type: {dream['type']}")
        print(f"   Thèmes thermiques: {dream['themes_combines']}")
        print(f"   Inspirations Internet: {len(dream.get('internet_inspirations', []))}")
        print(f"   Associations Internet: {len(dream.get('internet_associations', []))}")
        print(f"   Mode: {dream['mode']}")
        print(f"   Contenu: {dream['reve_productif'][:200]}...")
    else:
        print(f"❌ RÊVE TURBO #{i+1} ÉCHOUÉ")

    time.sleep(3)

print(f"\n📊 RÉSUMÉ DES RÊVES:")
dreams = dream_engine.get_recent_dreams(5)
print(f"   Total rêves générés: {len(dreams)}")

if dreams:
    print(f"\n☀️ RÉSUMÉ DE RÉVEIL:")
    summary = dream_engine.get_wake_up_summary()
    print(summary)

print(f"\n✅ TEST TERMINÉ")
