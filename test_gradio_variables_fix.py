#!/usr/bin/env python3
"""
🔧 TEST DE CORRECTION DES VARIABLES GRADIO
Teste la solution au problème de portée des variables dans Gradio

Auteur: <PERSON><PERSON><PERSON> - FAMILLE IA
Date: 2025-01-21
"""

import sys
import os

def test_gradio_import():
    """Teste l'import de Gradio"""
    try:
        import gradio as gr
        print("✅ Gradio importé avec succès")
        return True
    except ImportError:
        print("❌ Gradio non installé")
        print("💡 Solution: pip install gradio")
        return False

def test_interface_creation():
    """Teste la création de l'interface avec variables correctement déclarées"""
    try:
        import gradio as gr
        
        # SOLUTION CORRECTE - Variables dans le contexte Gradio
        with gr.Blocks(title="🧪 Test Variables Gradio") as demo:
            
            # Variables déclarées DANS le contexte Gradio
            thoughts_html_immediate = gr.HTML("Pensées immédiates")
            contact_info = gr.HTML("Informations de contact")  
            activity_indicator = gr.HTML("Indicateur d'activité")
            
            # Variables d'état persistantes
            state_var = gr.State("État initial")
            
            # Fonction de test qui utilise les variables
            def test_callback(input_text, current_state):
                """Callback qui utilise toutes les variables - ACCESSIBLE"""
                try:
                    # Ces variables sont maintenant accessibles
                    thoughts_update = f"🧠 Pensée: {input_text}"
                    contact_update = f"📱 Contact: {input_text}"
                    activity_update = f"🚦 Activité: {input_text}"
                    new_state = f"État mis à jour: {input_text}"
                    
                    return thoughts_update, contact_update, activity_update, new_state
                except Exception as e:
                    error_msg = f"❌ Erreur: {str(e)}"
                    return error_msg, error_msg, error_msg, error_msg
            
            # Interface de test
            with gr.Row():
                input_box = gr.Textbox(label="Test Input", placeholder="Tapez quelque chose...")
                test_btn = gr.Button("🧪 Tester", variant="primary")
            
            # Connexion du callback - SOLUTION CORRECTE
            test_btn.click(
                fn=test_callback,
                inputs=[input_box, state_var],
                outputs=[thoughts_html_immediate, contact_info, activity_indicator, state_var]
            )
        
        print("✅ Interface créée avec succès - Variables accessibles")
        return demo
        
    except Exception as e:
        print(f"❌ Erreur création interface: {str(e)}")
        return None

def test_jarvis_interface():
    """Teste l'interface JARVIS principale"""
    try:
        # Ajouter le répertoire courant au path
        sys.path.insert(0, '.')
        
        from jarvis_interface_communication_principale import create_main_communication_interface
        
        print("✅ Import JARVIS réussi")
        
        # Créer l'interface
        interface = create_main_communication_interface()
        print("✅ Interface JARVIS créée avec succès")
        
        return interface
        
    except ImportError as e:
        print(f"❌ Erreur import JARVIS: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ Erreur création JARVIS: {str(e)}")
        return None

def main():
    """Fonction principale de test"""
    print("🔧 TEST DE CORRECTION DES VARIABLES GRADIO")
    print("=" * 50)
    
    # Test 1: Import Gradio
    print("\n📦 Test 1: Import Gradio")
    if not test_gradio_import():
        return
    
    # Test 2: Interface simple
    print("\n🧪 Test 2: Interface de test simple")
    demo = test_interface_creation()
    if demo is None:
        return
    
    # Test 3: Interface JARVIS
    print("\n🤖 Test 3: Interface JARVIS principale")
    jarvis_interface = test_jarvis_interface()
    if jarvis_interface is None:
        print("⚠️ Interface JARVIS non disponible (normal si dépendances manquantes)")
    
    print("\n" + "=" * 50)
    print("✅ TESTS TERMINÉS")
    print("\n💡 SOLUTION AU PROBLÈME:")
    print("1. Déclarer toutes les variables Gradio DANS le contexte gr.Blocks()")
    print("2. Utiliser gr.State() pour les variables persistantes")
    print("3. S'assurer que les callbacks reçoivent et retournent les bonnes variables")
    print("4. Éviter les variables globales pour les composants Gradio")
    
    # Lancer l'interface de test si possible
    if demo:
        print("\n🚀 Lancement de l'interface de test...")
        try:
            demo.launch(server_name="localhost", server_port=7899, share=False)
        except Exception as e:
            print(f"❌ Erreur lancement: {str(e)}")

if __name__ == "__main__":
    main()
