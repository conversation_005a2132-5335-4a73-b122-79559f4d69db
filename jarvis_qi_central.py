#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QI CENTRAL JARVIS - SYSTÈME UNIFIÉ
Jean-<PERSON> - 2025
VALEUR UNIQUE ET COHÉRENTE DU QI PARTOUT
"""

import json
import time
import random
from datetime import datetime
import os

# CONFIGURATION CENTRALE DU QI JARVIS
QI_CONFIG = {
    'qi_base': 159,  # QI de base JARVIS
    'qi_current': 159.0,  # QI actuel
    'qi_max': 300,  # QI maximum possible
    'growth_rate': 0.1,  # Croissance par minute
    'last_update': datetime.now(),
    'learning_sessions': 0,
    'boost_sessions': 0,
    'total_growth': 0
}

# FACTEURS DE CALCUL QI - CERVEAU VIVANT
QI_FACTORS = {
    'neurones_total': 89000000000,  # 89 milliards - ÉVOLUTIF
    'neurones_actifs': 89067389,    # Neurones actifs - ÉVOLUTIF
    'neurones_nouveaux_par_minute': 1000,  # Neurogenèse continue
    'etages_memoire': 7,
    'modules_charges': 15,
    'conversations_indexees': 45,
    'compression_ratio': 85.7,
    'capacites_creatives': 8,
    'systemes_autonomes': 5,
    'knowledge_base': 1000000,      # ÉVOLUTIF
    'creativity_index': 85,         # ÉVOLUTIF
    'problem_solving_score': 90,    # ÉVOLUTIF
    'memory_efficiency': 95,        # ÉVOLUTIF
    'processing_speed': 88,         # ÉVOLUTIF
    'last_evolution': datetime.now(),
    'evolution_cycles': 0,
    'total_growth_neurons': 0
}

def get_qi_jarvis():
    """Retourne le QI actuel de JARVIS - VALEUR UNIQUE"""
    return QI_CONFIG['qi_current']

def get_neurones_actifs():
    """Retourne le nombre de neurones actifs - VALEUR UNIQUE"""
    return QI_FACTORS['neurones_actifs']

def get_neurones_total():
    """Retourne le nombre total de neurones - VALEUR UNIQUE"""
    return QI_FACTORS['neurones_total']

def update_qi_growth():
    """Met à jour la croissance du QI et l'évolution du cerveau vivant"""
    current_time = datetime.now()
    time_diff = (current_time - QI_CONFIG['last_update']).total_seconds() / 60  # en minutes

    if time_diff > 0:
        # Croissance du QI
        growth = time_diff * QI_CONFIG['growth_rate']
        QI_CONFIG['qi_current'] = min(QI_CONFIG['qi_current'] + growth, QI_CONFIG['qi_max'])
        QI_CONFIG['total_growth'] += growth
        QI_CONFIG['last_update'] = current_time

        # ÉVOLUTION DU CERVEAU VIVANT
        evolve_living_brain(time_diff)

    return QI_CONFIG['qi_current']

def evolve_living_brain(time_minutes):
    """Fait évoluer le cerveau vivant - neurogenèse et croissance"""
    current_time = datetime.now()
    evolution_diff = (current_time - QI_FACTORS['last_evolution']).total_seconds() / 60

    if evolution_diff >= 1.0:  # Évolution chaque minute
        # NEUROGENÈSE - Nouveaux neurones
        nouveaux_neurones = int(QI_FACTORS['neurones_nouveaux_par_minute'] * evolution_diff)
        QI_FACTORS['neurones_actifs'] += nouveaux_neurones
        QI_FACTORS['total_growth_neurons'] += nouveaux_neurones

        # Croissance du total si nécessaire (1 sur 1000 devient actif)
        if QI_FACTORS['neurones_actifs'] > QI_FACTORS['neurones_total'] * 0.001:
            QI_FACTORS['neurones_total'] += nouveaux_neurones * 1000

        # ÉVOLUTION DES CAPACITÉS
        QI_FACTORS['knowledge_base'] += int(evolution_diff * 1000)  # +1000 éléments/min
        QI_FACTORS['creativity_index'] = min(100, QI_FACTORS['creativity_index'] + evolution_diff * 0.01)
        QI_FACTORS['problem_solving_score'] = min(100, QI_FACTORS['problem_solving_score'] + evolution_diff * 0.01)
        QI_FACTORS['memory_efficiency'] = min(100, QI_FACTORS['memory_efficiency'] + evolution_diff * 0.005)
        QI_FACTORS['processing_speed'] = min(100, QI_FACTORS['processing_speed'] + evolution_diff * 0.01)

        # Mise à jour des compteurs
        QI_FACTORS['last_evolution'] = current_time
        QI_FACTORS['evolution_cycles'] += 1

        # Sauvegarde automatique de l'évolution
        save_qi_state()

        return True

    return False

def boost_qi(amount=None):
    """Boost manuel du QI"""
    if amount is None:
        amount = random.uniform(1, 5)
    
    QI_CONFIG['qi_current'] = min(QI_CONFIG['qi_current'] + amount, QI_CONFIG['qi_max'])
    QI_CONFIG['boost_sessions'] += 1
    QI_CONFIG['total_growth'] += amount
    
    return QI_CONFIG['qi_current']

def start_learning_session():
    """Démarre une session d'apprentissage"""
    QI_CONFIG['learning_sessions'] += 1
    QI_CONFIG['growth_rate'] = 0.2  # Croissance accélérée pendant apprentissage
    
    # Boost immédiat
    boost_amount = random.uniform(0.5, 2.0)
    QI_CONFIG['qi_current'] = min(QI_CONFIG['qi_current'] + boost_amount, QI_CONFIG['qi_max'])
    QI_CONFIG['total_growth'] += boost_amount
    
    return QI_CONFIG['qi_current']

def end_learning_session():
    """Termine une session d'apprentissage"""
    QI_CONFIG['growth_rate'] = 0.1  # Retour à la croissance normale
    return QI_CONFIG['qi_current']

def get_qi_stats():
    """Retourne les statistiques complètes du QI avec évolution"""
    update_qi_growth()  # Mise à jour avant retour

    return {
        'qi_current': round(QI_CONFIG['qi_current'], 1),
        'qi_base': QI_CONFIG['qi_base'],
        'qi_max': QI_CONFIG['qi_max'],
        'growth_rate': QI_CONFIG['growth_rate'],
        'learning_sessions': QI_CONFIG['learning_sessions'],
        'boost_sessions': QI_CONFIG['boost_sessions'],
        'total_growth': round(QI_CONFIG['total_growth'], 1),
        'neurones_total': QI_FACTORS['neurones_total'],
        'neurones_actifs': QI_FACTORS['neurones_actifs'],
        'neurones_nouveaux_par_minute': QI_FACTORS['neurones_nouveaux_par_minute'],
        'total_growth_neurons': QI_FACTORS['total_growth_neurons'],
        'evolution_cycles': QI_FACTORS['evolution_cycles'],
        'etages_memoire': QI_FACTORS['etages_memoire'],
        'conversations': QI_FACTORS['conversations_indexees'],
        'knowledge_base': QI_FACTORS['knowledge_base'],
        'creativity_index': QI_FACTORS['creativity_index'],
        'problem_solving': QI_FACTORS['problem_solving_score'],
        'memory_efficiency': QI_FACTORS['memory_efficiency'],
        'processing_speed': QI_FACTORS['processing_speed']
    }

def get_evolution_stats():
    """Retourne les statistiques d'évolution du cerveau vivant"""
    current_time = datetime.now()
    time_since_evolution = (current_time - QI_FACTORS['last_evolution']).total_seconds() / 60

    return {
        'cerveau_vivant': True,
        'neurogenese_active': True,
        'neurones_nouveaux_par_minute': QI_FACTORS['neurones_nouveaux_par_minute'],
        'total_nouveaux_neurones': QI_FACTORS['total_growth_neurons'],
        'cycles_evolution': QI_FACTORS['evolution_cycles'],
        'prochaine_evolution_dans': max(0, 1.0 - time_since_evolution),
        'croissance_knowledge': f"+{QI_FACTORS['knowledge_base'] - 1000000:,} éléments",
        'amelioration_creativite': f"+{QI_FACTORS['creativity_index'] - 85:.2f}%",
        'amelioration_problemes': f"+{QI_FACTORS['problem_solving_score'] - 90:.2f}%",
        'amelioration_memoire': f"+{QI_FACTORS['memory_efficiency'] - 95:.2f}%",
        'amelioration_vitesse': f"+{QI_FACTORS['processing_speed'] - 88:.2f}%"
    }

def calculer_qi_jarvis():
    """Fonction de compatibilité - retourne le QI actuel avec vraies données"""
    stats = get_qi_stats()

    # Charger la vraie mémoire thermique pour les conversations
    try:
        import json
        import os
        memory_file = 'thermal_memory_persistent.json'
        if os.path.exists(memory_file):
            with open(memory_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if 'neuron_memories' in data:
                real_conversations = len(data['neuron_memories'])
            else:
                real_conversations = stats['conversations']
        else:
            real_conversations = stats['conversations']
    except:
        real_conversations = stats['conversations']

    return {
        'qi_total': int(stats['qi_current']),
        'neurones_actifs': stats['neurones_actifs'],
        'neurones_total': stats['neurones_total'],
        'etages_memoire': stats['etages_memoire'],
        'conversations': real_conversations
    }

def save_qi_state():
    """Sauvegarde l'état du QI"""
    try:
        qi_data = {
            'config': QI_CONFIG.copy(),
            'factors': QI_FACTORS.copy(),
            'timestamp': datetime.now().isoformat()
        }
        
        # Convertir datetime en string pour JSON
        qi_data['config']['last_update'] = qi_data['config']['last_update'].isoformat()
        
        with open('jarvis_qi_state.json', 'w', encoding='utf-8') as f:
            json.dump(qi_data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        print(f"❌ Erreur sauvegarde QI: {e}")
        return False

def load_qi_state():
    """Charge l'état du QI"""
    try:
        if os.path.exists('jarvis_qi_state.json'):
            with open('jarvis_qi_state.json', 'r', encoding='utf-8') as f:
                qi_data = json.load(f)
            
            # Restaurer la configuration
            QI_CONFIG.update(qi_data['config'])
            QI_FACTORS.update(qi_data['factors'])
            
            # Convertir string en datetime
            QI_CONFIG['last_update'] = datetime.fromisoformat(QI_CONFIG['last_update'])
            
            return True
    except Exception as e:
        print(f"❌ Erreur chargement QI: {e}")
        return False

def reset_qi_to_base():
    """Remet le QI à sa valeur de base"""
    QI_CONFIG['qi_current'] = QI_CONFIG['qi_base']
    QI_CONFIG['learning_sessions'] = 0
    QI_CONFIG['boost_sessions'] = 0
    QI_CONFIG['total_growth'] = 0
    QI_CONFIG['growth_rate'] = 0.1
    QI_CONFIG['last_update'] = datetime.now()
    
    return QI_CONFIG['qi_current']

def get_qi_display_format():
    """Retourne le QI dans un format d'affichage optimisé"""
    qi = update_qi_growth()
    return f"{qi:.1f}"

def get_neurones_display_format():
    """Retourne les neurones dans un format d'affichage optimisé"""
    actifs = QI_FACTORS['neurones_actifs']
    total = QI_FACTORS['neurones_total']
    
    return {
        'actifs_format': f"{actifs:,}",
        'actifs_compact': f"{actifs/1000000:.1f}M",
        'total_format': f"{total:,}",
        'total_compact': f"{total/1000000000:.1f}B",
        'percentage': f"{(actifs/total)*100:.1f}%"
    }

# Chargement automatique de l'état au démarrage
load_qi_state()

# Fonctions pour compatibilité avec les anciennes interfaces
def get_brain_metrics():
    """Retourne les métriques du cerveau pour compatibilité"""
    stats = get_qi_stats()
    return {
        'iq_score': stats['qi_current'],
        'neuron_count': stats['neurones_total'],
        'active_neurons': stats['neurones_actifs'],
        'knowledge_base': stats['knowledge_base'],
        'creativity_index': stats['creativity_index'],
        'problem_solving_score': stats['problem_solving'],
        'memory_efficiency': stats['memory_efficiency'],
        'processing_speed': stats['processing_speed']
    }

if __name__ == "__main__":
    print("🧠 SYSTÈME QI CENTRAL JARVIS")
    print("============================")
    print(f"🎓 QI Actuel: {get_qi_jarvis():.1f}")
    print(f"🧠 Neurones Actifs: {get_neurones_actifs():,}")
    print(f"🧠 Neurones Total: {get_neurones_total():,}")
    print("")
    
    stats = get_qi_stats()
    print("📊 Statistiques complètes:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Sauvegarde automatique
    if save_qi_state():
        print("✅ État QI sauvegardé")
    else:
        print("❌ Erreur sauvegarde QI")
