#!/bin/bash

# 🚀 SCRIPT DE DÉMARRAGE JARVIS ELECTRON M4 COMPLET
# Jean<PERSON><PERSON> - Version complète avec toutes les fonctionnalités
# Optimisé pour Apple Silicon M4

echo "🍎 JARVIS ELECTRON M4 COMPLET - <PERSON><PERSON>MAR<PERSON><PERSON>"
echo "========================================"
echo "👤 Jean-Luc Passave - Version Complète"
echo "📅 $(date)"
echo ""

# Détection de l'architecture
ARCH=$(uname -m)
echo "📱 Architecture détectée: $ARCH"

if [ "$ARCH" = "arm64" ]; then
    echo "🍎 Apple Silicon M4 détecté - Optimisations activées"
    export APPLE_SILICON_OPTIMIZED=1
    export M4_PERFORMANCE_MODE=1
    export PYTORCH_ENABLE_MPS_FALLBACK=1
else
    echo "💻 Architecture standard détectée"
    export APPLE_SILICON_OPTIMIZED=0
fi

# Variables d'environnement optimisées
export NODE_ENV=production
export ELECTRON_ENABLE_LOGGING=1
export PYTHONOPTIMIZE=2
export PYTHONUNBUFFERED=1

# Vérification des dépendances
echo ""
echo "🔍 VÉRIFICATION DES DÉPENDANCES"
echo "================================"

# Vérifier Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js: $NODE_VERSION"
else
    echo "❌ Node.js non trouvé"
    exit 1
fi

# Vérifier Electron
if command -v electron &> /dev/null; then
    ELECTRON_VERSION=$(electron --version)
    echo "✅ Electron: $ELECTRON_VERSION"
else
    echo "❌ Electron non trouvé - Installation..."
    npm install electron
fi

# Vérifier Python
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    echo "✅ Python: $PYTHON_VERSION"
else
    echo "❌ Python3 non trouvé"
    exit 1
fi

# Vérifier l'environnement virtuel
if [ -d "venv_deepseek" ]; then
    echo "✅ Environnement virtuel: venv_deepseek"
    source venv_deepseek/bin/activate
else
    echo "⚠️ Environnement virtuel non trouvé"
fi

# Vérifier les fichiers essentiels
echo ""
echo "📁 VÉRIFICATION DES FICHIERS"
echo "============================="

REQUIRED_FILES=(
    "jarvis_electron_complete_m4.js"
    "package.json"
    "memoire_thermique_turbo_adaptatif.py"
    "jarvis_optimisation_m4_apple_silicon.py"
    "jarvis_generateur_multimedia_complet.py"
    "jarvis_formation_complete.py"
    "jarvis_analyse_evolutive_complete.py"
    "jarvis_interface_multimedia_complete.py"
    "jarvis_nouvelles_fenetres_simple.py"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - MANQUANT"
        exit 1
    fi
done

# Créer les dossiers nécessaires
echo ""
echo "📁 CRÉATION DOSSIERS"
echo "===================="

mkdir -p jarvis_creations
mkdir -p logs
mkdir -p assets

echo "✅ Dossiers créés"

# Vérifier les ports disponibles
echo ""
echo "🌐 VÉRIFICATION PORTS"
echo "====================="

PORTS=(7866 7867 7868 7872 7873 7874 7875 7876 7877 7878 7879 7880)

for port in "${PORTS[@]}"; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️ Port $port occupé - Libération..."
        lsof -ti:$port | xargs kill -9 2>/dev/null
    else
        echo "✅ Port $port disponible"
    fi
done

# Optimisations spécifiques Apple Silicon
if [ "$ARCH" = "arm64" ]; then
    echo ""
    echo "🍎 OPTIMISATIONS APPLE SILICON M4"
    echo "=================================="
    
    # Détecter le type de M4
    CPU_COUNT=$(sysctl -n hw.ncpu)
    echo "🧠 Nombre de cœurs: $CPU_COUNT"
    
    if [ "$CPU_COUNT" -eq 10 ]; then
        echo "🔥 M4 Standard détecté (6P+4E)"
        export M4_PERFORMANCE_CORES=6
        export M4_EFFICIENCY_CORES=4
    elif [ "$CPU_COUNT" -eq 14 ]; then
        echo "🚀 M4 Pro détecté (10P+4E)"
        export M4_PERFORMANCE_CORES=10
        export M4_EFFICIENCY_CORES=4
    elif [ "$CPU_COUNT" -ge 16 ]; then
        echo "⚡ M4 Max détecté (12P+4E)"
        export M4_PERFORMANCE_CORES=12
        export M4_EFFICIENCY_CORES=4
    fi
    
    # Mémoire unifiée
    MEMORY_GB=$(( $(sysctl -n hw.memsize) / 1024 / 1024 / 1024 ))
    echo "💾 Mémoire unifiée: ${MEMORY_GB} GB"
    export M4_UNIFIED_MEMORY=$MEMORY_GB
    
    # Neural Engine
    echo "🧠 Neural Engine: Activé"
    export M4_NEURAL_ENGINE=1
fi

# Nettoyage des logs précédents
echo ""
echo "🧹 NETTOYAGE"
echo "============"

rm -f logs/*.log 2>/dev/null
echo "✅ Logs nettoyés"

# Démarrage de l'application
echo ""
echo "🚀 DÉMARRAGE JARVIS ELECTRON M4 COMPLET"
echo "========================================"
echo "📊 Configuration:"
echo "   🍎 Apple Silicon: $APPLE_SILICON_OPTIMIZED"
echo "   ⚡ Mode Performance: ${M4_PERFORMANCE_MODE:-0}"
echo "   🧠 P-cores: ${M4_PERFORMANCE_CORES:-N/A}"
echo "   🔋 E-cores: ${M4_EFFICIENCY_CORES:-N/A}"
echo "   💾 Mémoire: ${M4_UNIFIED_MEMORY:-N/A} GB"
echo ""

# Fonction de nettoyage à la sortie
cleanup() {
    echo ""
    echo "🔄 ARRÊT JARVIS ELECTRON M4"
    echo "============================"
    
    # Arrêter les processus Python
    echo "🐍 Arrêt des services Python..."
    pkill -f "python.*jarvis" 2>/dev/null
    pkill -f "python.*memoire" 2>/dev/null
    
    # Libérer les ports
    echo "🌐 Libération des ports..."
    for port in "${PORTS[@]}"; do
        lsof -ti:$port | xargs kill -9 2>/dev/null
    done
    
    echo "✅ Nettoyage terminé"
    exit 0
}

# Capturer les signaux d'arrêt
trap cleanup SIGINT SIGTERM

# Démarrer l'application Electron
echo "🚀 Lancement de l'application..."
echo ""

if [ "$ARCH" = "arm64" ]; then
    echo "🍎 Démarrage optimisé Apple Silicon M4..."
    # Utiliser les optimisations M4
    electron jarvis_electron_complete_m4.js 2>&1 | tee logs/jarvis_electron_m4.log
else
    echo "💻 Démarrage standard..."
    electron jarvis_electron_complete_m4.js 2>&1 | tee logs/jarvis_electron.log
fi

# Si on arrive ici, l'application s'est fermée normalement
cleanup
