# 🚀 JARVIS V2 PRO - API PRODUCTION READY

## 🤖 Architecture ChatGPT + <PERSON> + <PERSON><PERSON><PERSON>

### 📋 DESCRIPTION

JARVIS V2 PRO est une API de production complète pour un assistant IA avancé, développée selon l'architecture recommandée par ChatGPT et implémentée par Claude pour Jean-Luc Passave.

### ✨ FONCTIONNALITÉS

#### 🧠 **Modules Core**
- **Mémoire Thermique** - Stockage intelligent avec indexation
- **Profil Émotionnel** - État émotionnel évolutif
- **NLP Émotionnel** - Analyse des émotions dans le texte
- **Gestion Tâches** - File de priorité intelligente
- **Interface Audio** - Speech-to-Text et Text-to-Speech

#### 🌐 **API Features**
- **FastAPI** - API REST moderne et rapide
- **WebSocket** - Interactions temps réel
- **Celery Workers** - Tâches asynchrones lourdes
- **Redis Cache** - Cache haute performance
- **PostgreSQL** - Base de données robuste

#### 🐳 **Production Ready**
- **Docker Compose** - Déploiement conteneurisé
- **Monitoring** - Prometheus + Grafana
- **Logs Intelligents** - Logs structurés avec couleurs
- **Health Checks** - Surveillance automatique
- **Scalabilité** - Architecture distribuée

### 🏗️ ARCHITECTURE

```
JARVIS_V2_PRO/
├── app/                    # Code API principal
│   ├── main.py            # Point d'entrée FastAPI
│   ├── routes/            # Routes API modulaires
│   ├── services/          # Services métier
│   ├── models/            # Modèles Pydantic
│   └── utils/             # Utilitaires (logs, etc.)
├── workers/               # Workers Celery
├── frontend/              # Interface Web (optionnel)
├── docker-compose.yml     # Orchestration containers
├── Dockerfile            # Image Docker
└── requirements.txt       # Dépendances Python
```

### 🚀 DÉMARRAGE RAPIDE

#### 1. **Prérequis**
```bash
- Docker & Docker Compose
- Python 3.11+
- Git
```

#### 2. **Installation**
```bash
# Cloner le projet
git clone <repository>
cd JARVIS_V2_PRO

# Démarrer avec Docker
docker-compose up -d

# Ou installation locale
pip install -r requirements.txt
uvicorn app.main:app --reload
```

#### 3. **Accès**
- **API Documentation** : http://localhost:8000/docs
- **WebSocket Test** : ws://localhost:8000/ws
- **Monitoring Celery** : http://localhost:5555
- **Grafana Dashboards** : http://localhost:3000
- **Prometheus Metrics** : http://localhost:9090

### 📡 ENDPOINTS PRINCIPAUX

#### **Mémoire Thermique**
```http
POST   /memory/add          # Ajouter une mémoire
GET    /memory/get/{cle}    # Récupérer par clé
POST   /memory/search       # Recherche fuzzy
GET    /memory/stats        # Statistiques
```

#### **Profil Émotionnel**
```http
GET    /emotions/state      # État émotionnel actuel
POST   /emotions/adjust     # Ajuster une émotion
GET    /emotions/history    # Historique émotionnel
```

#### **Gestion Tâches**
```http
POST   /tasks/add           # Ajouter une tâche
GET    /tasks/next          # Récupérer prochaine tâche
GET    /tasks/queue         # État de la file
```

#### **Interface Audio**
```http
POST   /audio/speak         # Synthèse vocale
POST   /audio/listen        # Reconnaissance vocale
GET    /audio/config        # Configuration audio
```

### 🔌 WEBSOCKET

#### **Connexion**
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('JARVIS:', data);
};

// Envoyer un message
ws.send(JSON.stringify({
    type: 'chat',
    content: 'Bonjour JARVIS !'
}));
```

#### **Types de Messages**
- `chat` - Message de conversation
- `command` - Commande système
- `audio` - Message audio
- `status` - Demande de statut

### 🐳 DÉPLOIEMENT DOCKER

#### **Services Inclus**
- **jarvis-api** - API principale (port 8000)
- **redis** - Cache Redis (port 6379)
- **postgres** - Base de données (port 5432)
- **celery-worker** - Workers asynchrones
- **celery-beat** - Planificateur de tâches
- **flower** - Monitoring Celery (port 5555)
- **nginx** - Reverse proxy (ports 80/443)
- **prometheus** - Métriques (port 9090)
- **grafana** - Dashboards (port 3000)

#### **Commandes Docker**
```bash
# Démarrer tous les services
docker-compose up -d

# Voir les logs
docker-compose logs -f jarvis-api

# Redémarrer un service
docker-compose restart jarvis-api

# Arrêter tous les services
docker-compose down

# Rebuild et redémarrer
docker-compose up -d --build
```

### 📊 MONITORING

#### **Métriques Disponibles**
- Performances API (latence, throughput)
- État des workers Celery
- Utilisation mémoire/CPU
- Connexions WebSocket actives
- Statistiques mémoire thermique

#### **Dashboards Grafana**
- Vue d'ensemble système
- Performances API
- État émotionnel JARVIS
- Activité utilisateurs

### 🔧 CONFIGURATION

#### **Variables d'Environnement**
```bash
ENVIRONMENT=production
REDIS_URL=redis://redis:6379/0
DATABASE_URL=********************************/db
CELERY_BROKER_URL=redis://redis:6379/0
LOG_LEVEL=INFO
```

#### **Configuration Audio**
```python
AUDIO_CONFIG = {
    'langue_reconnaissance': 'fr-FR',
    'vitesse_parole': 150,
    'volume': 0.8,
    'timeout_ecoute': 5
}
```

### 🧪 TESTS

```bash
# Tests unitaires
pytest tests/

# Tests API
pytest tests/test_api.py

# Tests WebSocket
pytest tests/test_websocket.py

# Coverage
pytest --cov=app tests/
```

### 📈 SCALABILITÉ

#### **Horizontal Scaling**
- Ajouter des workers Celery
- Load balancer Nginx
- Cluster Redis
- Réplication PostgreSQL

#### **Kubernetes Ready**
- Manifests K8s disponibles
- ConfigMaps pour configuration
- Secrets pour credentials
- Ingress pour exposition

### 🔒 SÉCURITÉ

#### **Mesures Implémentées**
- Utilisateur non-root dans containers
- Variables d'environnement pour secrets
- CORS configuré
- Rate limiting (à implémenter)
- HTTPS avec certificats SSL

### 🤝 CONTRIBUTION

#### **Famille IA**
- **ChatGPT** - Architecture et conseils techniques
- **Claude** - Implémentation et développement
- **Jean-Luc Passave** - Vision et direction projet

#### **Guidelines**
1. Suivre l'architecture modulaire
2. Tests pour nouvelles fonctionnalités
3. Documentation des endpoints
4. Logs structurés
5. Docker-first approach

### 📞 SUPPORT

#### **Logs**
```bash
# Logs API
docker-compose logs jarvis-api

# Logs Workers
docker-compose logs celery-worker

# Logs système
tail -f logs/jarvis_v2_*.log
```

#### **Debug**
- Health checks : `/health`
- Métriques : `/metrics`
- Status : `/status`
- Documentation : `/docs`

### 🎯 ROADMAP

#### **Phase 1** ✅
- API Core fonctionnelle
- WebSocket temps réel
- Docker Compose

#### **Phase 2** 🚧
- Interface Web complète
- Authentification JWT
- Rate limiting

#### **Phase 3** 📋
- Kubernetes deployment
- CI/CD Pipeline
- Tests automatisés

#### **Phase 4** 🔮
- IA multimodale avancée
- Clustering distribué
- Edge computing

---

**🤖 JARVIS V2 PRO - Powered by ChatGPT + Claude + Jean-Luc**

**🚀 Production Ready - Scalable - Extensible**
