#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - ROUTES MÉMOIRE THERMIQUE
Jean-Luc Passave - 2025
API Routes pour la mémoire thermique selon architecture ChatGPT
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from app.services.memory_service import MemoryService
from app.utils.logger import jarvis_logger

# Créer le router
router = APIRouter()

# Service mémoire
memory_service = MemoryService()

# === [ MODÈLES PYDANTIC ] ===

class MemoryEntry(BaseModel):
    """Modèle pour une entrée mémoire"""
    cle: str
    contenu: Any
    date: Optional[str] = None
    tags: Optional[List[str]] = []
    importance: Optional[int] = 5  # 1-10

class MemoryResponse(BaseModel):
    """Modèle pour la réponse mémoire"""
    cle: str
    contenu: Any
    date: str
    acces: int
    derniere_modification: str
    tags: List[str]
    importance: int

class SearchQuery(BaseModel):
    """Modèle pour les requêtes de recherche"""
    terme: str
    limite: Optional[int] = 10
    date_debut: Optional[str] = None
    date_fin: Optional[str] = None
    tags: Optional[List[str]] = []

# === [ ROUTES CRUD ] ===

@router.post("/add", response_model=Dict[str, Any])
async def add_memory(entry: MemoryEntry):
    """Ajoute une entrée à la mémoire thermique"""
    try:
        # Utiliser la date actuelle si non fournie
        date = entry.date or datetime.now().isoformat()
        
        # Ajouter à la mémoire
        result = memory_service.ajouter(
            cle=entry.cle,
            contenu=entry.contenu,
            date=date,
            tags=entry.tags,
            importance=entry.importance
        )
        
        jarvis_logger.info(f"Mémoire ajoutée: {entry.cle}", cle=entry.cle)
        
        return {
            "success": True,
            "message": f"Mémoire '{entry.cle}' ajoutée avec succès",
            "cle": entry.cle,
            "date": date,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur ajout mémoire: {e}", cle=entry.cle)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get/{cle}", response_model=MemoryResponse)
async def get_memory(cle: str):
    """Récupère une entrée mémoire par clé"""
    try:
        result = memory_service.rechercher(cle)
        
        if not result:
            raise HTTPException(status_code=404, detail=f"Mémoire '{cle}' non trouvée")
        
        jarvis_logger.info(f"Mémoire consultée: {cle}", cle=cle)
        
        return MemoryResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur récupération mémoire: {e}", cle=cle)
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/get-by-date/{date}")
async def get_memory_by_date(date: str):
    """Récupère une entrée mémoire par date"""
    try:
        result = memory_service.rechercher_par_date(date)
        
        if not result:
            raise HTTPException(status_code=404, detail=f"Aucune mémoire trouvée pour la date '{date}'")
        
        jarvis_logger.info(f"Mémoire consultée par date: {date}", date=date)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur récupération mémoire par date: {e}", date=date)
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search", response_model=List[MemoryResponse])
async def search_memory(query: SearchQuery):
    """Recherche dans la mémoire thermique"""
    try:
        results = memory_service.rechercher_fuzzy(
            terme=query.terme,
            limite=query.limite,
            date_debut=query.date_debut,
            date_fin=query.date_fin,
            tags=query.tags
        )
        
        jarvis_logger.info(f"Recherche mémoire: '{query.terme}' - {len(results)} résultats", 
                          terme=query.terme, resultats=len(results))
        
        return [MemoryResponse(**result) for result in results]
        
    except Exception as e:
        jarvis_logger.error(f"Erreur recherche mémoire: {e}", terme=query.terme)
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/update/{cle}")
async def update_memory(cle: str, entry: MemoryEntry):
    """Met à jour une entrée mémoire"""
    try:
        # Vérifier que l'entrée existe
        existing = memory_service.rechercher(cle)
        if not existing:
            raise HTTPException(status_code=404, detail=f"Mémoire '{cle}' non trouvée")
        
        # Mettre à jour
        result = memory_service.mettre_a_jour(
            cle=cle,
            contenu=entry.contenu,
            tags=entry.tags,
            importance=entry.importance
        )
        
        jarvis_logger.info(f"Mémoire mise à jour: {cle}", cle=cle)
        
        return {
            "success": True,
            "message": f"Mémoire '{cle}' mise à jour avec succès",
            "cle": cle,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur mise à jour mémoire: {e}", cle=cle)
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/delete/{cle}")
async def delete_memory(cle: str):
    """Supprime une entrée mémoire"""
    try:
        result = memory_service.supprimer(cle)
        
        if not result:
            raise HTTPException(status_code=404, detail=f"Mémoire '{cle}' non trouvée")
        
        jarvis_logger.info(f"Mémoire supprimée: {cle}", cle=cle)
        
        return {
            "success": True,
            "message": f"Mémoire '{cle}' supprimée avec succès",
            "cle": cle,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur suppression mémoire: {e}", cle=cle)
        raise HTTPException(status_code=500, detail=str(e))

# === [ ROUTES AVANCÉES ] ===

@router.get("/stats")
async def get_memory_stats():
    """Statistiques de la mémoire thermique"""
    try:
        stats = memory_service.get_statistiques()
        
        jarvis_logger.info("Statistiques mémoire consultées")
        
        return {
            "statistiques": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur statistiques mémoire: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/list")
async def list_memories(
    limite: int = Query(50, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    tri: str = Query("date", regex="^(date|acces|importance|cle)$")
):
    """Liste les mémoires avec pagination"""
    try:
        memories = memory_service.lister_memoires(
            limite=limite,
            offset=offset,
            tri=tri
        )
        
        jarvis_logger.info(f"Liste mémoires: {len(memories)} entrées", 
                          limite=limite, offset=offset, tri=tri)
        
        return {
            "memories": memories,
            "limite": limite,
            "offset": offset,
            "tri": tri,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur liste mémoires: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tags")
async def get_all_tags():
    """Récupère tous les tags utilisés"""
    try:
        tags = memory_service.get_tous_tags()
        
        jarvis_logger.info(f"Tags récupérés: {len(tags)} tags")
        
        return {
            "tags": tags,
            "count": len(tags),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur récupération tags: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/backup")
async def backup_memory():
    """Crée une sauvegarde de la mémoire"""
    try:
        backup_path = memory_service.creer_sauvegarde()
        
        jarvis_logger.info(f"Sauvegarde mémoire créée: {backup_path}")
        
        return {
            "success": True,
            "message": "Sauvegarde créée avec succès",
            "backup_path": backup_path,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        jarvis_logger.error(f"Erreur sauvegarde mémoire: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/restore")
async def restore_memory(backup_path: str):
    """Restaure la mémoire depuis une sauvegarde"""
    try:
        result = memory_service.restaurer_sauvegarde(backup_path)

        if not result:
            raise HTTPException(status_code=404, detail="Fichier de sauvegarde non trouvé")

        jarvis_logger.info(f"Mémoire restaurée depuis: {backup_path}")

        return {
            "success": True,
            "message": "Mémoire restaurée avec succès",
            "backup_path": backup_path,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur restauration mémoire: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# === [ FONCTIONS SÉCURITÉ & RGPD ] ===

@router.post("/delete-personal")
async def delete_personal_data(
    user_identifier: str,
    data_types: List[str] = Query(["all"]),
    confirm_deletion: bool = False
):
    """Efface les données personnelles d'un utilisateur (RGPD)"""
    try:
        if not confirm_deletion:
            raise HTTPException(
                status_code=400,
                detail="Confirmation requise pour effacement données personnelles"
            )

        # Types de données effaçables
        valid_types = ["conversations", "preferences", "voice_data", "face_data", "all"]
        invalid_types = [t for t in data_types if t not in valid_types]

        if invalid_types:
            raise HTTPException(
                status_code=400,
                detail=f"Types de données invalides: {invalid_types}"
            )

        # Effacer selon les types demandés
        deleted_items = memory_service.effacer_donnees_personnelles(
            user_identifier=user_identifier,
            data_types=data_types
        )

        jarvis_logger.info(
            f"Données personnelles effacées: {user_identifier}",
            user_id=user_identifier,
            data_types=data_types,
            items_deleted=len(deleted_items)
        )

        return {
            "success": True,
            "message": f"Données personnelles effacées pour {user_identifier}",
            "user_identifier": user_identifier,
            "data_types_deleted": data_types,
            "items_deleted": len(deleted_items),
            "deleted_keys": deleted_items,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        jarvis_logger.error(f"Erreur effacement données personnelles: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/personal-data/{user_identifier}")
async def get_personal_data(user_identifier: str):
    """Récupère toutes les données personnelles d'un utilisateur"""
    try:
        personal_data = memory_service.recuperer_donnees_personnelles(user_identifier)

        jarvis_logger.info(f"Données personnelles consultées: {user_identifier}")

        return {
            "user_identifier": user_identifier,
            "personal_data": personal_data,
            "total_items": len(personal_data),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        jarvis_logger.error(f"Erreur récupération données personnelles: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/anonymize")
async def anonymize_data(
    user_identifier: str,
    keep_analytics: bool = True
):
    """Anonymise les données d'un utilisateur (garde les stats, supprime l'identité)"""
    try:
        anonymized_items = memory_service.anonymiser_donnees(
            user_identifier=user_identifier,
            keep_analytics=keep_analytics
        )

        jarvis_logger.info(f"Données anonymisées: {user_identifier}")

        return {
            "success": True,
            "message": f"Données anonymisées pour {user_identifier}",
            "user_identifier": user_identifier,
            "items_anonymized": len(anonymized_items),
            "analytics_preserved": keep_analytics,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        jarvis_logger.error(f"Erreur anonymisation: {e}")
        raise HTTPException(status_code=500, detail=str(e))
