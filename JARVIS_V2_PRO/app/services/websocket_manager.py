#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS V2 PRO - WEBSOCKET MANAGER
Jean-Luc <PERSON> - 2025
Gestionnaire WebSocket pour interactions temps réel
"""

from fastapi import WebSocket
from typing import List, Dict, Any
import json
import asyncio
from datetime import datetime

class ConnectionManager:
    """Gestionnaire de connexions WebSocket pour JARVIS V2 PRO"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket):
        """Accepte une nouvelle connexion WebSocket"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # Enregistrer les infos de connexion
        self.connection_info[websocket] = {
            "connected_at": datetime.now().isoformat(),
            "messages_sent": 0,
            "messages_received": 0,
            "user_id": None
        }
        
        print(f"🔌 Nouvelle connexion WebSocket - Total: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """Ferme une connexion WebSocket"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        if websocket in self.connection_info:
            info = self.connection_info[websocket]
            print(f"🔌 Connexion fermée - Durée: {info['connected_at']} - Messages: {info['messages_sent']}")
            del self.connection_info[websocket]
        
        print(f"🔌 Connexion WebSocket fermée - Total: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Envoie un message à une connexion spécifique"""
        try:
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            
            # Mettre à jour les statistiques
            if websocket in self.connection_info:
                self.connection_info[websocket]["messages_sent"] += 1
                
        except Exception as e:
            print(f"❌ Erreur envoi message WebSocket: {e}")
            # Nettoyer la connexion fermée
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Diffuse un message à toutes les connexions actives"""
        if not self.active_connections:
            return
        
        print(f"📡 Diffusion WebSocket vers {len(self.active_connections)} connexions")
        
        # Créer une copie de la liste pour éviter les modifications concurrentes
        connections_copy = self.active_connections.copy()
        
        for connection in connections_copy:
            try:
                await self.send_personal_message(message, connection)
            except Exception as e:
                print(f"❌ Erreur diffusion vers une connexion: {e}")
                self.disconnect(connection)
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any]):
        """Envoie un message à un utilisateur spécifique"""
        for websocket, info in self.connection_info.items():
            if info.get("user_id") == user_id:
                await self.send_personal_message(message, websocket)
                return True
        return False
    
    def set_user_id(self, websocket: WebSocket, user_id: str):
        """Associe un user_id à une connexion"""
        if websocket in self.connection_info:
            self.connection_info[websocket]["user_id"] = user_id
            print(f"👤 User ID {user_id} associé à la connexion WebSocket")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques des connexions"""
        total_messages_sent = sum(info["messages_sent"] for info in self.connection_info.values())
        total_messages_received = sum(info["messages_received"] for info in self.connection_info.values())
        
        return {
            "active_connections": len(self.active_connections),
            "total_messages_sent": total_messages_sent,
            "total_messages_received": total_messages_received,
            "connections_info": [
                {
                    "connected_at": info["connected_at"],
                    "messages_sent": info["messages_sent"],
                    "messages_received": info["messages_received"],
                    "user_id": info["user_id"]
                }
                for info in self.connection_info.values()
            ]
        }
    
    async def ping_all_connections(self):
        """Envoie un ping à toutes les connexions pour vérifier leur état"""
        ping_message = {
            "type": "ping",
            "timestamp": datetime.now().isoformat(),
            "message": "🏓 Ping de santé"
        }
        
        await self.broadcast(ping_message)
    
    async def notify_jarvis_state_change(self, new_state: Dict[str, Any]):
        """Notifie tous les clients d'un changement d'état de JARVIS"""
        notification = {
            "type": "jarvis_state_change",
            "new_state": new_state,
            "timestamp": datetime.now().isoformat(),
            "message": "🤖 État de JARVIS mis à jour"
        }
        
        await self.broadcast(notification)
