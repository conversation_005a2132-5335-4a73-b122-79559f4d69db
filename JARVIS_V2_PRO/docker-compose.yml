# JARVIS V2 PRO - DOCKER COMPOSE PRODUCTION
# <PERSON><PERSON><PERSON> - 2025
# Architecture ChatGPT + <PERSON> + <PERSON><PERSON>

version: '3.8'

services:
  # === [ API PRINCIPALE ] ===
  jarvis-api:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: jarvis_v2_api
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=*************************************************/jarvis_v2
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - jarvis-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # === [ REDIS CACHE ] ===
  redis:
    image: redis:7-alpine
    container_name: jarvis_v2_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - jarvis-network
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # === [ DATABASE POSTGRESQL ] ===
  postgres:
    image: postgres:15-alpine
    container_name: jarvis_v2_postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=jarvis_v2
      - POSTGRES_USER=jarvis
      - POSTGRES_PASSWORD=jarvis_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - jarvis-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jarvis -d jarvis_v2"]
      interval: 30s
      timeout: 10s
      retries: 3

  # === [ CELERY WORKER ] ===
  celery-worker:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: jarvis_v2_celery
    command: celery -A workers.celery_app worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=*************************************************/jarvis_v2
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./workers:/app/workers
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - jarvis-network

  # === [ CELERY BEAT (SCHEDULER) ] ===
  celery-beat:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: jarvis_v2_beat
    command: celery -A workers.celery_app beat --loglevel=info
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=*************************************************/jarvis_v2
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - jarvis-network

  # === [ FLOWER (MONITORING CELERY) ] ===
  flower:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: jarvis_v2_flower
    command: celery -A workers.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - jarvis-network

  # === [ NGINX REVERSE PROXY ] ===
  nginx:
    image: nginx:alpine
    container_name: jarvis_v2_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - jarvis-api
    restart: unless-stopped
    networks:
      - jarvis-network

  # === [ PROMETHEUS MONITORING ] ===
  prometheus:
    image: prom/prometheus:latest
    container_name: jarvis_v2_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - jarvis-network

  # === [ GRAFANA DASHBOARDS ] ===
  grafana:
    image: grafana/grafana:latest
    container_name: jarvis_v2_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=jarvis_admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - jarvis-network

# === [ VOLUMES ] ===
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# === [ NETWORKS ] ===
networks:
  jarvis-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
