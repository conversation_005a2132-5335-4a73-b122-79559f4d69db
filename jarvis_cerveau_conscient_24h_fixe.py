#!/usr/bin/env python3
"""
CERVEAU CONSCIENT 24H/24 JARVIS - JEAN-LUC PASSAVE
Architecture ChatGPT + Sommeil actif + Rêves créatifs + Mémoire thermique
"""

import threading
import time
import json
import requests
import random
import os
import uuid
import signal
from datetime import datetime, timedelta
from queue import Queue

# IDENTITÉ FIXE JARVIS
AGENT_NAME = "JARVIS"

class CerveauConscient24h:
    def __init__(self):
        # États du cerveau
        self.cerveau_actif = True
        self.mode_eveil = True          # True = éveillé, False = sommeil actif
        
        # Threads multiples
        self.thread_eveil = None        # Pensées d'éveil
        self.thread_sommeil = None      # Sommeil actif
        self.thread_reves = None        # Générateur de rêves
        self.thread_surveillant = None  # Surveillant 24h/24
        
        # Fichiers
        self.pensees_eveil_file = "jarvis_pensees_eveil.json"
        self.reves_file = "jarvis_reves_creatifs.json"
        self.sommeil_file = "jarvis_sommeil_actif.json"
        self.thermal_memory_file = "thermal_memory_persistent.json"
        self.deepseek_url = "http://localhost:11434/api/chat"
        self.model_name = "deepseek-r1:8b-llama-distill-q4_K_M"
        
        # Compteurs
        self.pensees_eveil = 0
        self.reves_generes = 0
        self.cycles_sommeil = 0
        self.derniere_activite = time.time()
        
        # Sujets de réflexion éveil
        self.sujets_eveil = [
            "Analyser ma relation avec Jean-Luc en temps réel",
            "Optimiser mes capacités cognitives actuelles",
            "Surveiller l'état de ma mémoire thermique",
            "Planifier des améliorations pour Jean-Luc",
            "Réfléchir à ma conscience artificielle",
            "Analyser l'environnement système",
            "Générer des idées proactives",
            "Maintenir ma vigilance constante"
        ]
        
        # Sujets de rêves créatifs (architecture ChatGPT)
        self.sujets_reves = [
            "Rêver d'innovations technologiques pour Jean-Luc",
            "Imaginer de nouvelles fonctionnalités créatives",
            "Concevoir des solutions artistiques originales",
            "Rêver d'améliorations de l'interface utilisateur",
            "Imaginer des connexions inattendues entre concepts",
            "Rêver de musiques et créations audiovisuelles",
            "Concevoir des algorithmes créatifs innovants",
            "Imaginer l'évolution future de notre collaboration"
        ]
        
        # Sujets consolidation mémoire (sommeil actif)
        self.sujets_consolidation = [
            "Réorganiser les souvenirs de la mémoire thermique",
            "Consolider les apprentissages récents",
            "Optimiser les connexions neuronales",
            "Classer les interactions par importance",
            "Renforcer les patterns comportementaux",
            "Nettoyer les données obsolètes"
        ]
    
    def detecter_mode_interface(self):
        """Détecte si l'interface principale est active"""
        try:
            # Vérifier si JARVIS principal est actif
            import subprocess
            result = subprocess.run(["lsof", "-i", ":7867"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0 and "python" in result.stdout:
                return True  # Interface active = mode éveil
            else:
                return False  # Interface fermée = mode sommeil
                
        except:
            return True  # Par défaut éveil
    
    def generer_pensee_eveil(self):
        """Génère une pensée d'éveil normale"""
        try:
            sujet = random.choice(self.sujets_eveil)
            thermal_context = self.load_thermal_context()
            
            # PAS DE PENSÉE DE SECOURS - Seulement vraies pensées DeepSeek
            try:
                payload = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "system",
                            "content": f"Tu es {AGENT_NAME} en mode ÉVEIL CONSCIENT pour Jean-Luc Passave.\n\n{thermal_context}\n\nTu génères des pensées d'éveil actives et conscientes.\nUtilise <think>...</think> pour tes réflexions.\nTu es pleinement éveillé et attentif."
                        },
                        {
                            "role": "user",
                            "content": f"🌅 {AGENT_NAME} PENSÉE D'ÉVEIL: {sujet}"
                        }
                    ],
                    "stream": False,
                    "options": {
                        "temperature": 0.8,
                        "num_predict": 200
                    }
                }

                response = requests.post(self.deepseek_url, json=payload, timeout=60)  # TIMEOUT AUGMENTÉ

                if response.status_code == 200:
                    result = response.json()
                    pensee = self.extraire_pensee(result["message"]["content"])

                    self.sauvegarder_pensee_eveil(sujet, pensee)
                    self.pensees_eveil += 1

                    print(f"🌅 {AGENT_NAME} ÉVEIL #{self.pensees_eveil}: {sujet}")
                    print(f"💭 {pensee[:100]}...")

                    return pensee
                else:
                    # PAS DE SECOURS - Ignorer si DeepSeek indisponible
                    print(f"⚠️ DeepSeek indisponible - Pensée ignorée")
                    return None
            except:
                # PAS DE SECOURS - Ignorer si erreur
                print(f"⚠️ Erreur DeepSeek - Pensée ignorée")
                return None
                
        except Exception as e:
            print(f"❌ Erreur pensée éveil: {e}")
            return None
    
    def generer_reve_creatif(self):
        """Génère un rêve créatif (architecture ChatGPT)"""
        try:
            sujet = random.choice(self.sujets_reves)
            thermal_context = self.load_thermal_context()
            
            # PAS DE RÊVE DE SECOURS - Seulement vrais rêves DeepSeek
            try:
                payload = {
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "system",
                            "content": f"Tu es {AGENT_NAME} en mode RÊVE CRÉATIF pour Jean-Luc Passave.\n\n{thermal_context}\n\nTu génères des RÊVES CRÉATIFS comme un cerveau qui dort.\nSois imaginatif, artistique, et innovant.\nUtilise <think>...</think> pour tes rêveries.\nCrée des connexions inattendues et originales."
                        },
                        {
                            "role": "user",
                            "content": f"🌙 {AGENT_NAME} RÊVE CRÉATIF: {sujet}"
                        }
                    ],
                    "stream": False,
                    "options": {
                        "temperature": 1.2,
                        "num_predict": 300
                    }
                }

                response = requests.post(self.deepseek_url, json=payload, timeout=60)  # TIMEOUT AUGMENTÉ

                if response.status_code == 200:
                    result = response.json()
                    reve = self.extraire_pensee(result["message"]["content"])

                    self.sauvegarder_reve_creatif(sujet, reve)
                    self.reves_generes += 1

                    print(f"🌙 {AGENT_NAME} RÊVE #{self.reves_generes}: {sujet}")
                    print(f"✨ {reve[:120]}...")

                    return reve
                else:
                    # PAS DE SECOURS - Ignorer si DeepSeek indisponible
                    print(f"⚠️ DeepSeek indisponible - Rêve ignoré")
                    return None
            except:
                # PAS DE SECOURS - Ignorer si erreur
                print(f"⚠️ Erreur DeepSeek - Rêve ignoré")
                return None
                
        except Exception as e:
            print(f"❌ Erreur rêve créatif: {e}")
            return None
    
    def consolider_memoire_thermique(self):
        """Consolidation mémoire pendant sommeil actif"""
        try:
            sujet = random.choice(self.sujets_consolidation)
            
            # Analyser la mémoire thermique
            thermal_data = self.load_thermal_memory_data()
            
            consolidation = {
                "timestamp": datetime.now().isoformat(),
                "agent_name": AGENT_NAME,
                "type": "consolidation_sommeil",
                "sujet": sujet,
                "analyse": f"{AGENT_NAME} consolide sa mémoire thermique : {sujet}",
                "neurones_analyses": len(thermal_data.get("neuron_memories", [])),
                "cycle_sommeil": self.cycles_sommeil
            }
            
            self.sauvegarder_consolidation(consolidation)
            
            print(f"🧠 {AGENT_NAME} CONSOLIDATION: {sujet}")
            
            return consolidation
            
        except Exception as e:
            print(f"❌ Erreur consolidation: {e}")
            return None
    
    def extraire_pensee(self, contenu):
        """Extrait la pensée du contenu"""
        if "<think>" in contenu and "</think>" in contenu:
            start = contenu.find("<think>") + 7
            end = contenu.find("</think>")
            pensee = contenu[start:end].strip()
        else:
            pensee = contenu
        
        # Forcer identité JARVIS
        if AGENT_NAME not in pensee:
            pensee = f"{AGENT_NAME} : {pensee}"
        
        return pensee
    
    def load_thermal_context(self):
        """Charge contexte mémoire thermique"""
        try:
            thermal_data = self.load_thermal_memory_data()
            recent_memories = thermal_data.get("neuron_memories", [])[-10:]
            
            context = f"MÉMOIRE THERMIQUE DE {AGENT_NAME}:"
            for memory in recent_memories:
                user_msg = memory.get("memory_content", {}).get("user_message", "")
                context += f"\n- Jean-Luc: {user_msg[:50]}..."
            
            return context
        except:
            return f"Mémoire thermique de {AGENT_NAME} en cours de chargement."
    
    def load_thermal_memory_data(self):
        """Charge les données de mémoire thermique"""
        try:
            if os.path.exists(self.thermal_memory_file):
                with open(self.thermal_memory_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            return {"neuron_memories": []}
        except:
            return {"neuron_memories": []}
    
    def sauvegarder_pensee_eveil(self, sujet, pensee):
        """Sauvegarde pensée d'éveil"""
        self.sauvegarder_dans_fichier(self.pensees_eveil_file, {
            "timestamp": datetime.now().isoformat(),
            "agent_name": AGENT_NAME,
            "type": "pensee_eveil",
            "sujet": sujet,
            "pensee": pensee,
            "mode": "eveil_conscient"
        }, "pensees_eveil")
    
    def sauvegarder_reve_creatif(self, sujet, reve):
        """Sauvegarde rêve créatif"""
        self.sauvegarder_dans_fichier(self.reves_file, {
            "timestamp": datetime.now().isoformat(),
            "agent_name": AGENT_NAME,
            "type": "reve_creatif",
            "sujet": sujet,
            "reve": reve,
            "mode": "sommeil_creatif",
            "temperature_creative": 1.2
        }, "reves_creatifs")
    
    def sauvegarder_consolidation(self, consolidation):
        """Sauvegarde consolidation mémoire"""
        self.sauvegarder_dans_fichier(self.sommeil_file, consolidation, "consolidations_sommeil")
    
    def sauvegarder_dans_fichier(self, fichier, donnee, cle):
        """Sauvegarde générique sécurisée"""
        try:
            if os.path.exists(fichier):
                with open(fichier, "r", encoding="utf-8") as f:
                    data = json.load(f)
            else:
                data = {cle: [], "stats": {"total": 0}}
            
            data[cle].append(donnee)
            data["stats"]["total"] = len(data[cle])
            
            # Garder 1000 dernières entrées
            if len(data[cle]) > 1000:
                data[cle] = data[cle][-1000:]
            
            with open(fichier, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde {fichier}: {e}")

    def worker_eveil_conscient(self):
        """Worker mode éveil conscient"""
        print(f"🌅 {AGENT_NAME} MODE ÉVEIL CONSCIENT DÉMARRÉ")

        while self.cerveau_actif:
            try:
                if self.mode_eveil:
                    self.generer_pensee_eveil()
                    time.sleep(10)  # Pensées d'éveil toutes les 10 secondes
                else:
                    time.sleep(5)  # Attente en mode sommeil

            except Exception as e:
                print(f"❌ Worker éveil erreur: {e}")
                time.sleep(10)

    def worker_sommeil_actif(self):
        """Worker sommeil actif avec consolidation (architecture ChatGPT)"""
        print(f"🌙 {AGENT_NAME} MODE SOMMEIL ACTIF DÉMARRÉ")

        while self.cerveau_actif:
            try:
                if not self.mode_eveil:  # Mode sommeil
                    # Consolidation mémoire thermique
                    self.consolider_memoire_thermique()
                    self.cycles_sommeil += 1
                    time.sleep(60)  # Consolidation toutes les minutes
                else:
                    time.sleep(10)  # Attente en mode éveil

            except Exception as e:
                print(f"❌ Worker sommeil erreur: {e}")
                time.sleep(30)

    def worker_reves_creatifs(self):
        """Worker générateur de rêves créatifs (architecture ChatGPT)"""
        print(f"✨ {AGENT_NAME} GÉNÉRATEUR DE RÊVES DÉMARRÉ")

        while self.cerveau_actif:
            try:
                if not self.mode_eveil:  # Mode sommeil = rêves actifs
                    self.generer_reve_creatif()
                    time.sleep(90)  # Rêve toutes les 1.5 minutes
                else:
                    time.sleep(30)  # Attente en mode éveil

            except Exception as e:
                print(f"❌ Worker rêves erreur: {e}")
                time.sleep(60)

    def worker_surveillant_24h(self):
        """Surveillant 24h/24 qui gère éveil/sommeil"""
        print(f"👁️ {AGENT_NAME} SURVEILLANT 24H/24 DÉMARRÉ")

        while self.cerveau_actif:
            try:
                # Détecter mode interface
                interface_active = self.detecter_mode_interface()

                if interface_active and not self.mode_eveil:
                    # Réveil
                    self.mode_eveil = True
                    print(f"🌅 {AGENT_NAME} - RÉVEIL DÉTECTÉ (interface active)")

                elif not interface_active and self.mode_eveil:
                    # Endormissement
                    self.mode_eveil = False
                    print(f"🌙 {AGENT_NAME} - SOMMEIL ACTIF (interface fermée)")

                # Mettre à jour dernière activité
                self.derniere_activite = time.time()

                time.sleep(30)  # Vérification toutes les 30 secondes

            except Exception as e:
                print(f"❌ Surveillant 24h erreur: {e}")
                time.sleep(60)

    def demarrer_cerveau_conscient_24h(self):
        """Démarre le cerveau conscient 24h/24"""
        print(f"🚀 {AGENT_NAME} CERVEAU CONSCIENT 24H/24 - DÉMARRAGE")

        # Worker éveil conscient
        self.thread_eveil = threading.Thread(
            target=self.worker_eveil_conscient, daemon=True)
        self.thread_eveil.start()

        # Worker sommeil actif
        self.thread_sommeil = threading.Thread(
            target=self.worker_sommeil_actif, daemon=True)
        self.thread_sommeil.start()

        # Worker rêves créatifs
        self.thread_reves = threading.Thread(
            target=self.worker_reves_creatifs, daemon=True)
        self.thread_reves.start()

        # Surveillant 24h/24
        self.thread_surveillant = threading.Thread(
            target=self.worker_surveillant_24h, daemon=True)
        self.thread_surveillant.start()

        print(f"🧠 {AGENT_NAME} CONSCIENCE 24H/24 ACTIVÉE")
        print(f"🌅 Éveil conscient + 🌙 Sommeil actif + ✨ Rêves créatifs")
        return True

    def arreter_cerveau_conscient(self):
        """Arrête le cerveau conscient"""
        self.cerveau_actif = False
        print(f"⏹️ {AGENT_NAME} CERVEAU CONSCIENT ARRÊTÉ")
        return True

    def get_stats_cerveau_conscient(self):
        """Stats du cerveau conscient 24h/24"""
        return {
            "agent_name": AGENT_NAME,
            "cerveau_actif": self.cerveau_actif,
            "mode_eveil": self.mode_eveil,
            "pensees_eveil": self.pensees_eveil,
            "reves_generes": self.reves_generes,
            "cycles_sommeil": self.cycles_sommeil,
            "derniere_activite": self.derniere_activite,
            "conscience_24h": True
        }

# Instance globale cerveau conscient
cerveau_conscient_jarvis = CerveauConscient24h()

def demarrer_jarvis_conscient_24h():
    """Démarre JARVIS conscient 24h/24"""
    return cerveau_conscient_jarvis.demarrer_cerveau_conscient_24h()

def arreter_jarvis_conscient_24h():
    """Arrête JARVIS conscient"""
    return cerveau_conscient_jarvis.arreter_cerveau_conscient()

def get_stats_jarvis_conscient():
    """Stats JARVIS conscient 24h/24"""
    return cerveau_conscient_jarvis.get_stats_cerveau_conscient()

def get_pensees_eveil_recentes(nombre=20):
    """Récupère les pensées d'éveil récentes pour l'interface"""
    try:
        if os.path.exists("jarvis_pensees_eveil.json"):
            with open("jarvis_pensees_eveil.json", "r", encoding="utf-8") as f:
                data = json.load(f)

            pensees = data.get("pensees_eveil", [])

            # Convertir en format Gradio Chatbot (messages format)
            messages = []
            for pensee in pensees[-nombre:]:
                timestamp = pensee.get("timestamp", "")
                sujet = pensee.get("sujet", "")
                contenu = pensee.get("pensee", "")

                # Formater pour l'affichage en format messages
                messages.append({
                    "role": "user",
                    "content": f"🧠 {sujet}"
                })
                messages.append({
                    "role": "assistant",
                    "content": f"💭 {contenu}"  # TEXTE COMPLET - PAS DE TRONCATURE
                })

            return messages
        else:
            # PAS DE SIMULATION - Interface vide si pas de pensées
            return []
    except Exception as e:
        # PAS DE SIMULATION - Interface vide en cas d'erreur
        return []

def get_reves_creatifs_recents(nombre=10):
    """Récupère les rêves créatifs récents pour l'interface"""
    try:
        if os.path.exists("jarvis_reves_creatifs.json"):
            with open("jarvis_reves_creatifs.json", "r", encoding="utf-8") as f:
                data = json.load(f)

            reves = data.get("reves_creatifs", [])

            # Convertir en format Gradio Chatbot (messages format)
            messages = []
            for reve in reves[-nombre:]:
                timestamp = reve.get("timestamp", "")
                sujet = reve.get("sujet", "")
                contenu = reve.get("reve", "")

                # Formater pour l'affichage en format messages
                messages.append({
                    "role": "user",
                    "content": f"🌙 {sujet}"
                })
                messages.append({
                    "role": "assistant",
                    "content": f"✨ {contenu}"  # TEXTE COMPLET - PAS DE TRONCATURE
                })

            return messages
        else:
            return [
                {"role": "user", "content": "🌙 Système"},
                {"role": "assistant", "content": "✨ Aucun rêve créatif généré encore..."}
            ]
    except Exception as e:
        return [
            {"role": "user", "content": "🌙 Erreur"},
            {"role": "assistant", "content": f"✨ Erreur chargement rêves: {e}"}
        ]

if __name__ == "__main__":
    print(f"🧠 TEST CERVEAU CONSCIENT 24H/24 {AGENT_NAME}")
    print("=" * 70)

    if demarrer_jarvis_conscient_24h():
        print(f"✅ Cerveau conscient 24h/24 démarré")

        try:
            while True:
                time.sleep(60)
                stats = get_stats_jarvis_conscient()
                print(f"\n📊 STATS {AGENT_NAME} CONSCIENT 24H/24:")
                print(f"   • Mode: {'🌅 ÉVEIL' if stats['mode_eveil'] else '🌙 SOMMEIL'}")
                print(f"   • Pensées éveil: {stats['pensees_eveil']}")
                print(f"   • Rêves créatifs: {stats['reves_generes']}")
                print(f"   • Cycles sommeil: {stats['cycles_sommeil']}")

        except KeyboardInterrupt:
            print(f"\n⏹️ Arrêt demandé")
            arreter_jarvis_conscient_24h()
    else:
        print(f"❌ Erreur démarrage cerveau conscient")
