#!/usr/bin/env python3
"""
🧠 JARVIS THERMAL MEMORY MANAGER
Gestion automatique des tokens avec mémoire thermique
Créé pour Jean-Luc Passave
"""

import json
import time
from datetime import datetime, timedelta
from pathlib import Path

class ThermalMemoryManager:
    """Gestionnaire de mémoire thermique pour optimisation automatique des tokens"""
    
    def __init__(self):
        self.memory_file = Path("thermal_memory_tokens.json")
        self.interactions_history = self.load_memory()
        self.token_optimization_cache = {}
        
        print("🧠 Thermal Memory Manager initialisé")
    
    def load_memory(self):
        """Charge la mémoire thermique depuis le fichier"""
        try:
            if self.memory_file.exists():
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                "interactions": [],
                "token_stats": {},
                "optimization_patterns": {},
                "videos": {},  # 🎬 MÉMOIRE VIDÉO COGNITIVE - JEAN-LUC PASSAVE
                "images": {},  # 🎨 MÉMOIRE IMAGES COGNITIVE
                "audio": {},   # 🎵 MÉMOIRE AUDIO COGNITIVE
                "multimodal_associations": {},  # 🧠 ASSOCIATIONS MULTIMODALES
                "cognitive_index": {}  # 🔍 INDEX COGNITIF SÉMANTIQUE
            }
        except Exception as e:
            print(f"❌ Erreur chargement mémoire thermique: {e}")
            return {
                "interactions": [],
                "token_stats": {},
                "optimization_patterns": {},
                "videos": {},
                "images": {},
                "audio": {},
                "multimodal_associations": {},
                "cognitive_index": {}
            }
    
    def save_memory(self):
        """Sauvegarde la mémoire thermique"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.interactions_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire thermique: {e}")
    
    def get_optimal_token_count(self, message_length, turbo_factor=1.0):
        """🚀 CALCUL AUTOMATIQUE OPTIMAL DES TOKENS - JEAN-LUC PASSAVE"""
        
        # Base de calcul selon la longueur du message
        if message_length < 50:
            base_tokens = 100
        elif message_length < 150:
            base_tokens = 200
        elif message_length < 300:
            base_tokens = 350
        elif message_length < 500:
            base_tokens = 500
        else:
            base_tokens = 750
        
        # 🚀 AJUSTEMENT AVEC MÉMOIRE THERMIQUE
        # Analyser les interactions passées pour optimiser
        recent_interactions = self.get_recent_interactions(hours=24)
        
        if recent_interactions:
            # Calculer la moyenne des tokens utilisés avec succès
            successful_tokens = [
                interaction.get("tokens_used", base_tokens) 
                for interaction in recent_interactions 
                if interaction.get("success", False)
            ]
            
            if successful_tokens:
                avg_successful = sum(successful_tokens) / len(successful_tokens)
                # Ajuster selon l'historique thermique
                base_tokens = int((base_tokens + avg_successful) / 2)
        
        # 🚀 FACTEUR TURBO APPLIQUÉ
        optimized_tokens = int(base_tokens * turbo_factor)
        
        # 🧠 APPRENTISSAGE THERMIQUE - Mémoriser le pattern
        pattern_key = f"length_{message_length//50*50}_turbo_{turbo_factor}"
        self.interactions_history["optimization_patterns"][pattern_key] = optimized_tokens
        
        print(f"🧠 Tokens optimisés: {optimized_tokens} (base: {base_tokens}, turbo: {turbo_factor})")
        return optimized_tokens
    
    def optimize_message_for_context(self, message, agent_id):
        """🚀 OPTIMISATION AUTOMATIQUE DU MESSAGE AVEC CONTEXTE THERMIQUE"""
        
        # Récupérer le contexte thermique pour cet agent
        agent_history = [
            interaction for interaction in self.interactions_history["interactions"]
            if interaction.get("agent_id") == agent_id
        ]
        
        # Si on a de l'historique, adapter le style
        if agent_history:
            recent_successful = [
                interaction for interaction in agent_history[-10:]  # 10 dernières
                if interaction.get("success", False)
            ]
            
            if recent_successful:
                # Analyser les patterns de succès
                avg_length = sum(len(i.get("message", "")) for i in recent_successful) / len(recent_successful)
                
                # Ajuster la longueur du message si nécessaire
                if len(message) > avg_length * 1.5:
                    # Message trop long, le condenser
                    words = message.split()
                    target_words = int(len(words) * 0.8)
                    optimized_message = " ".join(words[:target_words]) + "..."
                    print(f"🧠 Message condensé: {len(message)} -> {len(optimized_message)} chars")
                    return optimized_message
        
        return message
    
    def save_agent_interaction(self, agent_id, message, response, tokens_used):
        """🧠 ENREGISTRER L'INTERACTION DANS LA MÉMOIRE THERMIQUE"""
        
        interaction = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id,
            "message": message[:200],  # Limiter pour économiser l'espace
            "response": response[:200],
            "tokens_used": tokens_used,
            "message_length": len(message),
            "response_length": len(response),
            "success": len(response) > 10 and "erreur" not in response.lower(),
            "thermal_score": self.calculate_thermal_score(message, response)
        }
        
        # Ajouter à l'historique
        self.interactions_history["interactions"].append(interaction)
        
        # Nettoyer l'historique (garder seulement les 1000 dernières)
        if len(self.interactions_history["interactions"]) > 1000:
            self.interactions_history["interactions"] = self.interactions_history["interactions"][-1000:]
        
        # Mettre à jour les stats par agent
        if agent_id not in self.interactions_history["token_stats"]:
            self.interactions_history["token_stats"][agent_id] = {
                "total_interactions": 0,
                "successful_interactions": 0,
                "avg_tokens": 0,
                "best_thermal_score": 0
            }
        
        stats = self.interactions_history["token_stats"][agent_id]
        stats["total_interactions"] += 1
        if interaction["success"]:
            stats["successful_interactions"] += 1
        
        # Calculer moyenne mobile des tokens
        stats["avg_tokens"] = (stats["avg_tokens"] * 0.9) + (tokens_used * 0.1)
        stats["best_thermal_score"] = max(stats["best_thermal_score"], interaction["thermal_score"])
        
        # Sauvegarder périodiquement
        if stats["total_interactions"] % 10 == 0:
            self.save_memory()
        
        print(f"🧠 Interaction thermique sauvegardée: {agent_id} (score: {interaction['thermal_score']:.2f})")
    
    def calculate_thermal_score(self, message, response):
        """Calcule un score thermique pour l'interaction"""
        # Score basé sur la qualité de la réponse
        score = 0.0
        
        # Longueur appropriée
        if 50 <= len(response) <= 500:
            score += 0.3
        
        # Pas d'erreurs
        if "erreur" not in response.lower() and "error" not in response.lower():
            score += 0.3
        
        # Contenu riche (présence d'emojis, ponctuation)
        if any(char in response for char in "🚀⚡🧠💭🌙✅"):
            score += 0.2
        
        # Ratio message/réponse équilibré
        ratio = len(response) / max(len(message), 1)
        if 0.5 <= ratio <= 3.0:
            score += 0.2
        
        return min(score, 1.0)
    
    def get_recent_interactions(self, hours=24):
        """Récupère les interactions récentes"""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        recent = []
        for interaction in self.interactions_history["interactions"]:
            try:
                interaction_time = datetime.fromisoformat(interaction["timestamp"])
                if interaction_time > cutoff:
                    recent.append(interaction)
            except:
                continue
        
        return recent
    
    def get_thermal_stats(self):
        """Statistiques de la mémoire thermique"""
        total_interactions = len(self.interactions_history["interactions"])
        recent_interactions = len(self.get_recent_interactions(24))
        
        return {
            "total_interactions": total_interactions,
            "recent_24h": recent_interactions,
            "agents_stats": self.interactions_history["token_stats"],
            "optimization_patterns": len(self.interactions_history["optimization_patterns"]),
            "memory_efficiency": recent_interactions / max(total_interactions, 1)
        }

# Instance globale
thermal_manager = ThermalMemoryManager()

def get_optimal_token_count(message_length, turbo_factor=1.0):
    """Interface pour calcul optimal des tokens"""
    return thermal_manager.get_optimal_token_count(message_length, turbo_factor)

def optimize_message_for_context(message, agent_id):
    """Interface pour optimisation du message"""
    return thermal_manager.optimize_message_for_context(message, agent_id)

def save_agent_interaction(agent_id, message, response, tokens_used):
    """Interface pour sauvegarde d'interaction"""
    return thermal_manager.save_agent_interaction(agent_id, message, response, tokens_used)

def get_thermal_stats():
    """Interface pour statistiques thermiques"""
    return thermal_manager.get_thermal_stats()

if __name__ == "__main__":
    print("🧠 JARVIS THERMAL MEMORY MANAGER")
    print("=" * 50)
    
    # Test du système
    print(f"🧪 Test tokens optimaux: {get_optimal_token_count(150, 1.5)}")
    print(f"🧪 Test optimisation message: {optimize_message_for_context('Test message', 'agent1')}")
    
    # Stats
    stats = get_thermal_stats()
    print(f"📊 Stats thermiques: {stats}")
