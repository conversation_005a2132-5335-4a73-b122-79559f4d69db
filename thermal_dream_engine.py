#!/usr/bin/env python3
"""
THERMAL DREAM ENGINE - JARVIS
Architecture cognitive complète par ChatGPT pour Jean-Luc Passave
Moteur de rêves thermiques avec mémoire vivante
"""

import os
import datetime
import random
import json
import threading
import time
from pathlib import Path

class ThermalDreamEngine:
    def __init__(self, memory_service=None):
        self.memory_service = memory_service
        self.dream_folder = "./thermal_dreams/"
        self.active = True
        self.dream_thread = None
        
        # Créer dossier rêves
        os.makedirs(self.dream_folder, exist_ok=True)
        
        # Configuration rêves
        self.dream_interval = 300  # 5 minutes entre rêves
        self.max_dreams_per_session = 10
        self.dream_tags = [
            "créatif", "technique", "organisationnel", "émotionnel",
            "innovation", "amélioration", "futuriste", "artistique"
        ]
        
        # Templates de rêves
        self.dream_templates = [
            "Projet Nova - Innovation technologique",
            "Idée <PERSON> - Créativité pure", 
            "Extension Cognitive - Amélioration JARVIS",
            "Vision Futuriste - Évolution système",
            "Connexion Émotionnelle - Relation Jean-Luc",
            "Optimisation Thermique - Mémoire vivante",
            "Interface Révolutionnaire - UX/UI",
            "Intelligence Augmentée - Capacités étendues"
        ]

    def load_thermal_memory(self):
        """Charge la mémoire thermique pour les rêves"""
        try:
            if os.path.exists("thermal_memory_persistent.json"):
                with open("thermal_memory_persistent.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                return data.get("neuron_memories", [])
            return []
        except:
            return []

    def get_memories_by_temperature(self, min_temp=0.5):
        """Récupère mémoires par température (pertinence)"""
        memories = self.load_thermal_memory()
        hot_memories = []
        
        for memory in memories[-20:]:  # 20 dernières mémoires
            # Simuler température basée sur récence et contenu
            content = memory.get("memory_content", {})
            user_msg = content.get("user_message", "")
            agent_msg = content.get("agent_response", "")
            
            # Calculer "température" basée sur mots-clés importants
            hot_keywords = ["jarvis", "amélioration", "créativité", "innovation", "rêve", "futur"]
            temp_score = sum(1 for keyword in hot_keywords if keyword.lower() in (user_msg + agent_msg).lower())
            
            if temp_score >= min_temp:
                hot_memories.append({
                    "content": user_msg,
                    "response": agent_msg,
                    "temperature": temp_score,
                    "tags": memory.get("neuron_metadata", {}).get("keywords", [])
                })
        
        return hot_memories

    def generate_dream(self):
        """Génère un rêve thermique basé sur la mémoire"""
        try:
            memories = self.get_memories_by_temperature(min_temp=0.3)
            
            dream = {
                "timestamp": datetime.datetime.now().isoformat(),
                "title": random.choice(self.dream_templates),
                "type": "thermal_dream",
                "agent": "JARVIS",
                "ideas": [],
                "memory_fragments": [],
                "tags": [],
                "temperature": 0.0,
                "creativity_level": random.uniform(0.7, 1.0)
            }

            # Ajouter fragments de mémoire
            if memories:
                selected_memories = random.sample(memories, min(len(memories), 5))
                for mem in selected_memories:
                    dream["memory_fragments"].append({
                        "content": mem["content"][:200],
                        "temperature": mem["temperature"]
                    })
                    dream["tags"].extend(mem.get("tags", []))
                    dream["temperature"] += mem["temperature"]

            # Générer idées créatives basées sur mémoire
            creative_ideas = [
                f"Fusion entre {random.choice(['mémoire thermique', 'interface utilisateur', 'intelligence artificielle'])} et {random.choice(['créativité', 'innovation', 'efficacité'])}",
                f"Nouveau module JARVIS pour {random.choice(['Jean-Luc', 'la productivité', 'l expérience utilisateur'])}",
                f"Évolution cognitive : {random.choice(['pensées continues', 'rêves créatifs', 'mémoire vivante'])}",
                f"Interface révolutionnaire avec {random.choice(['commandes vocales', 'gestes intuitifs', 'prédiction comportementale'])}"
            ]
            
            dream["ideas"] = random.sample(creative_ideas, random.randint(2, 4))
            
            # Ajouter tags automatiques
            auto_tags = random.sample(self.dream_tags, random.randint(2, 4))
            dream["tags"].extend(auto_tags)
            dream["tags"] = list(set(dream["tags"]))  # Supprimer doublons

            # Calculer température moyenne
            if dream["memory_fragments"]:
                dream["temperature"] = dream["temperature"] / len(dream["memory_fragments"])

            # Sauvegarder rêve
            filename = f"{self.dream_folder}dream_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(dream, f, ensure_ascii=False, indent=2)

            print(f"✨ RÊVE THERMIQUE GÉNÉRÉ: {dream['title']}")
            print(f"   📁 Fichier: {filename}")
            print(f"   🌡️ Température: {dream['temperature']:.2f}")
            print(f"   💡 Idées: {len(dream['ideas'])}")

            return filename

        except Exception as e:
            print(f"❌ Erreur génération rêve thermique: {e}")
            return None

    def get_last_dream(self):
        """Récupère le dernier rêve généré"""
        try:
            files = sorted([f for f in os.listdir(self.dream_folder) if f.endswith('.json')], reverse=True)
            if files:
                with open(os.path.join(self.dream_folder, files[0]), 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except:
            return None

    def get_all_dreams(self, limit=10):
        """Récupère tous les rêves récents"""
        try:
            files = sorted([f for f in os.listdir(self.dream_folder) if f.endswith('.json')], reverse=True)
            dreams = []
            
            for file in files[:limit]:
                with open(os.path.join(self.dream_folder, file), 'r', encoding='utf-8') as f:
                    dreams.append(json.load(f))
            
            return dreams
        except:
            return []

    def dream_worker(self):
        """Worker thread pour génération automatique de rêves"""
        print("🌙 THERMAL DREAM ENGINE - Démarrage worker rêves")
        
        while self.active:
            try:
                # Générer rêve thermique
                self.generate_dream()
                
                # Attendre avant prochain rêve
                time.sleep(self.dream_interval)
                
            except Exception as e:
                print(f"❌ Erreur worker rêves thermiques: {e}")
                time.sleep(60)  # Attendre 1 minute en cas d'erreur

    def start_dreaming(self):
        """Démarre le moteur de rêves thermiques"""
        if not self.dream_thread or not self.dream_thread.is_alive():
            self.active = True
            self.dream_thread = threading.Thread(target=self.dream_worker, daemon=True)
            self.dream_thread.start()
            print("🌙 THERMAL DREAM ENGINE - Démarré")
            return True
        return False

    def stop_dreaming(self):
        """Arrête le moteur de rêves thermiques"""
        self.active = False
        print("🌙 THERMAL DREAM ENGINE - Arrêté")
        return True

    def get_dream_stats(self):
        """Statistiques des rêves thermiques"""
        dreams = self.get_all_dreams(100)
        
        return {
            "total_dreams": len(dreams),
            "active": self.active,
            "last_dream": dreams[0]["timestamp"] if dreams else None,
            "avg_temperature": sum(d.get("temperature", 0) for d in dreams) / len(dreams) if dreams else 0,
            "dream_folder": self.dream_folder
        }

# Instance globale
thermal_dream_engine = ThermalDreamEngine()

def start_thermal_dreams():
    """Démarre les rêves thermiques"""
    return thermal_dream_engine.start_dreaming()

def stop_thermal_dreams():
    """Arrête les rêves thermiques"""
    return thermal_dream_engine.stop_dreaming()

def get_thermal_dream_stats():
    """Stats rêves thermiques"""
    return thermal_dream_engine.get_dream_stats()

def get_recent_thermal_dreams(limit=5):
    """Rêves thermiques récents"""
    return thermal_dream_engine.get_all_dreams(limit)

if __name__ == "__main__":
    print("🌙 TEST THERMAL DREAM ENGINE")
    print("=" * 40)
    
    # Test génération rêve
    engine = ThermalDreamEngine()
    dream_file = engine.generate_dream()
    
    if dream_file:
        print(f"✅ Rêve généré: {dream_file}")
        
        # Afficher le rêve
        last_dream = engine.get_last_dream()
        if last_dream:
            print(f"\n🌙 DERNIER RÊVE:")
            print(f"   Titre: {last_dream['title']}")
            print(f"   Idées: {len(last_dream['ideas'])}")
            print(f"   Tags: {', '.join(last_dream['tags'][:5])}")
    else:
        print("❌ Échec génération rêve")
