#!/bin/bash

# 🚀 SCRIPT DE DÉMARRAGE JARVIS COMPLET RÉEL
# Jean<PERSON><PERSON> - <PERSON><PERSON>marre le VRAI programme JARVIS + Electron
# Version qui utilise votre programme existant qui fonctionne

echo "🤖 JARVIS COMPLET RÉEL - DÉMARRAGE"
echo "=================================="
echo "👤 Jean-Luc Passave"
echo "📅 $(date)"
echo ""

# Détection de l'architecture
ARCH=$(uname -m)
echo "📱 Architecture détectée: $ARCH"

if [ "$ARCH" = "arm64" ]; then
    echo "🍎 Apple Silicon M4 détecté - Optimisations activées"
    export APPLE_SILICON_OPTIMIZED=1
    export M4_PERFORMANCE_MODE=1
    export PYTORCH_ENABLE_MPS_FALLBACK=1
else
    echo "💻 Architecture standard détectée"
    export APPLE_SILICON_OPTIMIZED=0
fi

# Variables d'environnement
export NODE_ENV=production
export ELECTRON_ENABLE_LOGGING=1
export PYTHONOPTIMIZE=2
export PYTHONUNBUFFERED=1

echo ""
echo "🔍 VÉRIFICATION PROGRAMME JARVIS EXISTANT"
echo "========================================="

# Vérifier si JARVIS fonctionne déjà
if curl -s http://localhost:7867 > /dev/null 2>&1; then
    echo "✅ JARVIS déjà en cours d'exécution sur port 7867"
    JARVIS_RUNNING=true
else
    echo "⚠️ JARVIS non détecté sur port 7867"
    JARVIS_RUNNING=false
fi

# Vérifier l'environnement virtuel
if [ -d "venv_deepseek" ]; then
    echo "✅ Environnement virtuel: venv_deepseek"
    source venv_deepseek/bin/activate
else
    echo "⚠️ Environnement virtuel non trouvé"
fi

# Vérifier les fichiers essentiels
echo ""
echo "📁 VÉRIFICATION FICHIERS JARVIS"
echo "==============================="

if [ -f "jarvis_architecture_multi_fenetres.py" ]; then
    echo "✅ jarvis_architecture_multi_fenetres.py"
    JARVIS_FILE_EXISTS=true
else
    echo "❌ jarvis_architecture_multi_fenetres.py - MANQUANT"
    JARVIS_FILE_EXISTS=false
fi

if [ -f "jarvis_electron_complete_m4.js" ]; then
    echo "✅ jarvis_electron_complete_m4.js"
else
    echo "❌ jarvis_electron_complete_m4.js - MANQUANT"
    exit 1
fi

# Fonction de nettoyage
cleanup() {
    echo ""
    echo "🔄 ARRÊT JARVIS COMPLET"
    echo "======================="
    
    # Arrêter Electron
    echo "🖥️ Arrêt Electron..."
    pkill -f "electron.*jarvis" 2>/dev/null
    
    # Arrêter JARVIS Python (seulement si on l'a démarré nous-mêmes)
    if [ "$JARVIS_STARTED_BY_US" = "true" ]; then
        echo "🐍 Arrêt JARVIS Python..."
        pkill -f "python.*jarvis_architecture" 2>/dev/null
    fi
    
    echo "✅ Nettoyage terminé"
    exit 0
}

# Capturer les signaux d'arrêt
trap cleanup SIGINT SIGTERM

echo ""
echo "🚀 DÉMARRAGE JARVIS COMPLET RÉEL"
echo "================================"

# Démarrer JARVIS Python si nécessaire
if [ "$JARVIS_RUNNING" = "false" ] && [ "$JARVIS_FILE_EXISTS" = "true" ]; then
    echo "🐍 Démarrage du programme JARVIS Python..."
    python jarvis_architecture_multi_fenetres.py &
    JARVIS_PID=$!
    JARVIS_STARTED_BY_US=true
    
    echo "⏳ Attente du démarrage de JARVIS..."
    sleep 5
    
    # Vérifier que JARVIS a démarré
    if curl -s http://localhost:7867 > /dev/null 2>&1; then
        echo "✅ JARVIS Python démarré avec succès (PID: $JARVIS_PID)"
    else
        echo "❌ Échec du démarrage de JARVIS Python"
        exit 1
    fi
else
    echo "✅ Utilisation du programme JARVIS existant"
    JARVIS_STARTED_BY_US=false
fi

# Attendre un peu plus pour s'assurer que JARVIS est stable
echo "⏳ Stabilisation de JARVIS..."
sleep 3

# Démarrer l'application Electron
echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON M4"
echo "===================================="

if [ "$ARCH" = "arm64" ]; then
    echo "🍎 Démarrage optimisé Apple Silicon M4..."
    echo "   ⚡ P-cores: Activés"
    echo "   🔋 E-cores: Activés" 
    echo "   🧠 Neural Engine: Activé"
    echo "   💾 Unified Memory: Optimisée"
fi

echo ""
echo "🚀 Lancement Electron avec connexion au VRAI JARVIS..."

# Démarrer Electron
electron jarvis_electron_complete_m4.js

# Si on arrive ici, Electron s'est fermé
echo ""
echo "🔄 Application Electron fermée"
cleanup
