#!/usr/bin/env python3
"""
ANALYSE ÉVOLUTIVE COMPLÈTE JARVIS - JEAN-LUC PASSAVE
Implémentation complète des propositions ChatGPT pour la mémoire thermique
"""

import os
import json
import time
import datetime
from collections import defaultdict, Counter
from memoire_thermique_turbo_adaptatif import get_memoire_thermique

class AnalyseEvolutiveJARVIS:
    """Analyse évolutive complète selon les propositions ChatGPT"""
    
    def __init__(self):
        self.memoire = get_memoire_thermique()
        self.patterns_detectes = {}
        self.tendances_evolutives = {}
        self.predictions = []
        
        print("🧬 Analyse évolutive JARVIS initialisée")
    
    def analyze_user_habits(self):
        """PATTERN 1 - Analyse des habitudes de Jean-Luc (ChatGPT)"""
        try:
            # Analyser les patterns d'usage
            user_queries = []
            topics = Counter()
            time_patterns = Counter()
            complexity_evolution = []
            
            # Parcourir toutes les données de mémoire
            for date_key, memories in self.memoire.neuron_memories.items():
                if isinstance(memories, list):
                    for memory in memories:
                        if isinstance(memory, dict):
                            content = memory.get('content', '').lower()
                            timestamp = memory.get('timestamp', '')
                            
                            user_queries.append(content)
                            
                            # Analyse des sujets
                            words = content.split()
                            for word in words:
                                if len(word) > 3:  # Mots significatifs
                                    topics[word] += 1
                            
                            # Analyse temporelle
                            if timestamp:
                                try:
                                    if isinstance(timestamp, str):
                                        dt = datetime.datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                    else:
                                        dt = timestamp
                                    hour = dt.hour
                                    time_patterns[hour] += 1
                                except:
                                    pass
                            
                            # Évolution complexité
                            complexity = len(set(words)) / max(len(words), 1)
                            complexity_evolution.append(complexity)
            
            # Top sujets et heures
            top_topics = topics.most_common(10)
            top_hours = time_patterns.most_common(5)
            
            # Calcul tendances
            avg_complexity = sum(complexity_evolution) / max(len(complexity_evolution), 1)
            
            # Détection patterns spécifiques Jean-Luc
            jean_luc_patterns = self._detect_jean_luc_patterns(user_queries)
            
            return {
                "total_queries": len(user_queries),
                "unique_topics": len(topics),
                "top_topics": top_topics,
                "preferred_hours": top_hours,
                "activity_periods": len(time_patterns),
                "avg_complexity": avg_complexity,
                "complexity_trend": self._analyze_complexity_trend(complexity_evolution),
                "jean_luc_patterns": jean_luc_patterns,
                "evolution_score": self._calculate_evolution_score(complexity_evolution, topics)
            }
            
        except Exception as e:
            return f"❌ **ERREUR ANALYSE HABITUDES**: {str(e)}"
    
    def _detect_jean_luc_patterns(self, queries):
        """Détecte les patterns spécifiques à Jean-Luc Passave"""
        patterns = {
            "code_focus": 0,
            "jarvis_development": 0,
            "ai_research": 0,
            "optimization_requests": 0,
            "memory_thermal": 0,
            "deepseek_usage": 0,
            "interface_improvements": 0
        }
        
        for query in queries:
            if any(word in query for word in ['code', 'programme', 'fonction', 'classe']):
                patterns["code_focus"] += 1
            
            if any(word in query for word in ['jarvis', 'agent', 'assistant']):
                patterns["jarvis_development"] += 1
            
            if any(word in query for word in ['intelligence', 'artificielle', 'ia', 'ai', 'deepseek']):
                patterns["ai_research"] += 1
            
            if any(word in query for word in ['optimise', 'améliore', 'performance', 'turbo']):
                patterns["optimization_requests"] += 1
            
            if any(word in query for word in ['mémoire', 'thermique', 'souvenir', 'apprentissage']):
                patterns["memory_thermal"] += 1
            
            if any(word in query for word in ['deepseek', 'r1', 'modèle', 'llm']):
                patterns["deepseek_usage"] += 1
            
            if any(word in query for word in ['interface', 'gradio', 'fenêtre', 'bouton']):
                patterns["interface_improvements"] += 1
        
        return patterns
    
    def _analyze_complexity_trend(self, complexity_evolution):
        """Analyse la tendance d'évolution de la complexité"""
        if len(complexity_evolution) < 2:
            return "insufficient_data"
        
        # Calculer la tendance sur les 20 dernières interactions
        recent = complexity_evolution[-20:] if len(complexity_evolution) > 20 else complexity_evolution
        
        if len(recent) < 2:
            return "stable"
        
        # Régression linéaire simple
        n = len(recent)
        x_sum = sum(range(n))
        y_sum = sum(recent)
        xy_sum = sum(i * recent[i] for i in range(n))
        x2_sum = sum(i * i for i in range(n))
        
        slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
        
        if slope > 0.01:
            return "increasing_complexity"
        elif slope < -0.01:
            return "decreasing_complexity"
        else:
            return "stable_complexity"
    
    def _calculate_evolution_score(self, complexity_evolution, topics):
        """Calcule un score d'évolution global"""
        if not complexity_evolution or not topics:
            return 0
        
        # Score basé sur diversité des sujets et complexité moyenne
        topic_diversity = len(topics) / max(sum(topics.values()), 1)
        avg_complexity = sum(complexity_evolution) / len(complexity_evolution)
        
        # Score composite (0-100)
        evolution_score = (topic_diversity * 50) + (avg_complexity * 50)
        return min(100, max(0, evolution_score))
    
    def suggest_recurrent_queries(self):
        """PATTERN 2 - Suggestions basées sur l'historique (ChatGPT)"""
        try:
            # Analyser les patterns de questions récentes
            question_patterns = []
            recent_topics = []
            command_patterns = []
            
            # Analyser les 50 dernières interactions
            recent_memories = []
            for memories in list(self.memoire.neuron_memories.values())[-10:]:
                if isinstance(memories, list):
                    recent_memories.extend(memories[-5:])  # 5 dernières par jour
            
            for memory in recent_memories[-50:]:  # 50 dernières au total
                if isinstance(memory, dict):
                    content = memory.get('content', '').lower()
                    
                    # Détecter les questions
                    if any(q in content for q in ['?', 'comment', 'peux-tu', 'pourrais-tu', 'veux-tu']):
                        question_patterns.append(content)
                    
                    # Détecter les commandes
                    if any(cmd in content for cmd in ['lance', 'ouvre', 'démarre', 'active', 'génère']):
                        command_patterns.append(content)
                    
                    # Sujets récents
                    words = [w for w in content.split() if len(w) > 4]
                    recent_topics.extend(words[:3])
            
            # Générer suggestions intelligentes basées sur les patterns
            suggestions = self._generate_intelligent_suggestions(recent_topics, question_patterns, command_patterns)
            
            return {
                "suggestions": suggestions,
                "question_patterns": len(question_patterns),
                "command_patterns": len(command_patterns),
                "recent_topics": list(set(recent_topics[:10])),
                "trend": self._determine_usage_trend(recent_topics),
                "proactive_actions": self._suggest_proactive_actions(recent_topics)
            }
            
        except Exception as e:
            return f"❌ **ERREUR SUGGESTIONS**: {str(e)}"
    
    def _generate_intelligent_suggestions(self, recent_topics, questions, commands):
        """Génère des suggestions intelligentes basées sur l'analyse"""
        suggestions = []
        topics_text = ' '.join(recent_topics).lower()
        
        # Suggestions basées sur les sujets récents
        if any(word in topics_text for word in ['code', 'programme', 'fonction']):
            suggestions.append("💻 Veux-tu que je révise ton code récent et propose des optimisations ?")
            suggestions.append("🔍 Dois-je analyser la qualité de ton code et détecter les améliorations possibles ?")
        
        if any(word in topics_text for word in ['jarvis', 'agent', 'assistant']):
            suggestions.append("🤖 Veux-tu que j'améliore mes capacités ou ajoute de nouvelles fonctionnalités ?")
            suggestions.append("🧠 Dois-je analyser notre historique de collaboration pour m'optimiser ?")
        
        if any(word in topics_text for word in ['mémoire', 'thermique', 'apprentissage']):
            suggestions.append("🧠 Veux-tu voir l'évolution de notre mémoire partagée et les patterns détectés ?")
            suggestions.append("📊 Dois-je générer un rapport d'analyse de notre collaboration ?")
        
        if any(word in topics_text for word in ['interface', 'gradio', 'fenêtre']):
            suggestions.append("🖥️ Veux-tu que j'améliore l'interface ou ajoute de nouvelles fonctionnalités ?")
            suggestions.append("🎨 Dois-je proposer des améliorations visuelles pour l'interface ?")
        
        if any(word in topics_text for word in ['optimisation', 'performance', 'turbo']):
            suggestions.append("⚡ Veux-tu que j'active des optimisations supplémentaires pour améliorer les performances ?")
            suggestions.append("🚀 Dois-je analyser les goulots d'étranglement et proposer des accélérations ?")
        
        if any(word in topics_text for word in ['génération', 'image', 'vidéo', 'musique']):
            suggestions.append("🎨 Veux-tu que je génère du contenu créatif (images, vidéos, musique) ?")
            suggestions.append("🎬 Dois-je améliorer les capacités de génération multimédia ?")
        
        # Suggestions par défaut si aucun pattern spécifique
        if not suggestions:
            suggestions = [
                "🔍 Veux-tu que je scanne tes applications et propose des optimisations ?",
                "📊 Dois-je analyser tes habitudes de travail et suggérer des améliorations ?",
                "🧠 Veux-tu voir un résumé de nos conversations récentes et les insights détectés ?",
                "🚀 Dois-je activer des fonctionnalités avancées ou des modes spéciaux ?",
                "🎯 Veux-tu que je me concentre sur un domaine particulier pour t'assister ?"
            ]
        
        return suggestions[:5]  # Limiter à 5 suggestions
    
    def _determine_usage_trend(self, recent_topics):
        """Détermine la tendance d'usage actuelle"""
        topics_text = ' '.join(recent_topics).lower()
        
        # Compter les occurrences par catégorie
        categories = {
            "technique": len([t for t in recent_topics if any(word in t.lower() for word in ['code', 'programme', 'fonction', 'classe', 'api'])]),
            "ia_research": len([t for t in recent_topics if any(word in t.lower() for word in ['intelligence', 'artificielle', 'deepseek', 'modèle'])]),
            "interface": len([t for t in recent_topics if any(word in t.lower() for word in ['interface', 'gradio', 'fenêtre', 'bouton'])]),
            "creative": len([t for t in recent_topics if any(word in t.lower() for word in ['génération', 'image', 'vidéo', 'musique', 'créatif'])]),
            "optimization": len([t for t in recent_topics if any(word in t.lower() for word in ['optimisation', 'performance', 'turbo', 'accélération'])])
        }
        
        # Déterminer la catégorie dominante
        dominant_category = max(categories, key=categories.get)
        
        trend_mapping = {
            "technique": "Développement Technique",
            "ia_research": "Recherche IA",
            "interface": "Amélioration Interface",
            "creative": "Création Multimédia",
            "optimization": "Optimisation Performance"
        }
        
        return trend_mapping.get(dominant_category, "Générale")
    
    def _suggest_proactive_actions(self, recent_topics):
        """Suggère des actions proactives basées sur les tendances"""
        topics_text = ' '.join(recent_topics).lower()
        actions = []
        
        if 'code' in topics_text:
            actions.append("Analyser automatiquement la qualité du code")
            actions.append("Proposer des refactorisations intelligentes")
        
        if 'jarvis' in topics_text:
            actions.append("Auto-amélioration des capacités")
            actions.append("Apprentissage continu des préférences")
        
        if 'mémoire' in topics_text:
            actions.append("Optimisation automatique de la mémoire thermique")
            actions.append("Génération de rapports d'évolution")
        
        if 'interface' in topics_text:
            actions.append("Suggestions d'améliorations UX")
            actions.append("Adaptation automatique de l'interface")
        
        return actions[:3]  # Limiter à 3 actions
    
    def analyze_evolutionary_memory(self):
        """Analyse complète de l'évolution de la mémoire thermique"""
        try:
            # Statistiques globales
            total_memories = sum(len(memories) if isinstance(memories, list) else 1 
                               for memories in self.memoire.neuron_memories.values())
            
            # Analyse par période
            daily_stats = {}
            for date_key, memories in self.memoire.neuron_memories.items():
                if isinstance(memories, list):
                    daily_stats[date_key] = {
                        "count": len(memories),
                        "complexity": sum(len(set(m.get('content', '').split())) / max(len(m.get('content', '').split()), 1) 
                                        for m in memories if isinstance(m, dict)) / max(len(memories), 1),
                        "topics": len(set(word for m in memories if isinstance(m, dict) 
                                        for word in m.get('content', '').split() if len(word) > 3))
                    }
            
            # Évolution temporelle
            evolution_trend = self._calculate_memory_evolution(daily_stats)
            
            # Zones thermiques actives
            thermal_zones = dict(self.memoire.thermal_zones)
            
            # Tags émotionnels
            emotional_distribution = {k: len(v) for k, v in self.memoire.emotional_tags.items()}
            
            return {
                "total_memories": total_memories,
                "daily_stats": daily_stats,
                "evolution_trend": evolution_trend,
                "thermal_zones": thermal_zones,
                "emotional_distribution": emotional_distribution,
                "cache_efficiency": len(self.memoire.cache_lru),
                "index_size": len(self.memoire.index_global),
                "learning_progress": self._assess_learning_progress()
            }
            
        except Exception as e:
            return f"❌ Erreur analyse évolutive: {str(e)}"
    
    def _calculate_memory_evolution(self, daily_stats):
        """Calcule l'évolution de la mémoire dans le temps"""
        if len(daily_stats) < 2:
            return "insufficient_data"
        
        # Trier par date
        sorted_dates = sorted(daily_stats.keys())
        
        # Calculer les tendances
        counts = [daily_stats[date]["count"] for date in sorted_dates]
        complexities = [daily_stats[date]["complexity"] for date in sorted_dates]
        
        # Tendance du volume
        volume_trend = "increasing" if counts[-1] > counts[0] else "decreasing" if counts[-1] < counts[0] else "stable"
        
        # Tendance de la complexité
        complexity_trend = "increasing" if complexities[-1] > complexities[0] else "decreasing" if complexities[-1] < complexities[0] else "stable"
        
        return {
            "volume_trend": volume_trend,
            "complexity_trend": complexity_trend,
            "total_days": len(sorted_dates),
            "avg_daily_memories": sum(counts) / len(counts),
            "avg_complexity": sum(complexities) / len(complexities)
        }
    
    def _assess_learning_progress(self):
        """Évalue le progrès d'apprentissage"""
        # Analyser l'évolution des préférences utilisateur
        preferences = self.memoire.user_preferences
        
        # Analyser la qualité des suggestions
        suggestions_count = len(self.memoire.proactive_suggestions)
        
        # Analyser l'adaptation
        adaptation_score = len(preferences) * 10 + suggestions_count * 5
        
        return {
            "preferences_learned": len(preferences),
            "suggestions_generated": suggestions_count,
            "adaptation_score": min(100, adaptation_score),
            "learning_stage": self._determine_learning_stage(adaptation_score)
        }
    
    def _determine_learning_stage(self, score):
        """Détermine le stade d'apprentissage actuel"""
        if score < 20:
            return "Initial"
        elif score < 50:
            return "Developing"
        elif score < 80:
            return "Advanced"
        else:
            return "Expert"

# Instance globale
analyse_evolutive = AnalyseEvolutiveJARVIS()

def get_analyse_evolutive():
    """Retourne l'instance d'analyse évolutive"""
    return analyse_evolutive
