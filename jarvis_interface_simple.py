#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
JARVIS INTERFACE SIMPLE - VERSION FONCTIONNELLE
Jean<PERSON> - Interface minimaliste qui fonctionne
"""

import gradio as gr
import requests
import json
import time
import os

# Configuration
SERVER_URL = "http://localhost:8000/v1/chat/completions"
MODEL_NAME = "DeepSeek R1 0528 Qwen3 8B"

def send_message_to_deepseek(message):
    """Envoie un message à DeepSeek R1 8B"""
    try:
        payload = {
            "model": MODEL_NAME,
            "messages": [
                {"role": "system", "content": "Tu es JARVIS, l'assistant <PERSON><PERSON> <PERSON>. Tu es intelligent, utile et amical."},
                {"role": "user", "content": message}
            ],
            "temperature": 0.7,
            "max_tokens": 2000,
            "stream": False
        }
        
        response = requests.post(
            SERVER_URL,
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            return data["choices"][0]["message"]["content"]
        else:
            return f"❌ Erreur {response.status_code}: {response.text}"
            
    except Exception as e:
        return f"❌ Erreur de connexion: {str(e)}"

def chat_interface(message, history):
    """Interface de chat Gradio"""
    if not message.strip():
        return history, ""
    
    # Ajouter le message utilisateur
    history.append([message, "🤔 JARVIS réfléchit..."])
    
    # Obtenir la réponse de JARVIS
    response = send_message_to_deepseek(message)
    
    # Mettre à jour avec la vraie réponse
    history[-1][1] = response
    
    return history, ""

def test_connection():
    """Teste la connexion avec DeepSeek"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            return "✅ DeepSeek R1 8B connecté et opérationnel"
        else:
            return f"❌ DeepSeek répond mais erreur {response.status_code}"
    except:
        return "❌ DeepSeek R1 8B non accessible - Vérifiez qu'il est démarré"

def create_interface():
    """Crée l'interface Gradio"""
    
    with gr.Blocks(
        title="🤖 JARVIS - Interface Simple",
        theme=gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="green",
            neutral_hue="slate"
        )
    ) as interface:
        
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(45deg, #1e3c72, #2a5298); color: white; border-radius: 10px; margin-bottom: 20px;">
            <h1>🤖 JARVIS - Interface Simple</h1>
            <p><strong>Jean-Luc Passave</strong> - DeepSeek R1 8B</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=3):
                chatbot = gr.Chatbot(
                    label="💬 Chat avec JARVIS",
                    height=500,
                    show_label=True
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="Votre message",
                        placeholder="💬 Tapez votre message à JARVIS...",
                        scale=4
                    )
                    send_btn = gr.Button("📤 Envoyer", scale=1)
            
            with gr.Column(scale=1):
                gr.HTML("""
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                    <h3>📊 Statut JARVIS</h3>
                    <p>🤖 Agent: DeepSeek R1 8B</p>
                    <p>🧠 Neurones: 8 milliards</p>
                    <p>🔥 Statut: Actif</p>
                </div>
                """)
                
                test_btn = gr.Button("🔧 Test Connexion", variant="secondary")
                status_output = gr.Textbox(label="Statut", interactive=False)
                
                gr.HTML("""
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin-top: 15px;">
                    <h3>🌐 Interfaces</h3>
                    <p><a href="http://localhost:8000" target="_blank">🚀 API DeepSeek</a></p>
                    <p><a href="http://localhost:7863" target="_blank">🏠 Dashboard</a></p>
                </div>
                """)
        
        # Événements
        msg.submit(chat_interface, [msg, chatbot], [chatbot, msg])
        send_btn.click(chat_interface, [msg, chatbot], [chatbot, msg])
        test_btn.click(test_connection, outputs=status_output)
        
        # Message de bienvenue
        interface.load(
            lambda: [["Bonjour Jean-Luc ! 👋", "🤖 Salut Jean-Luc ! Je suis JARVIS, votre assistant IA. Comment puis-je vous aider aujourd'hui ?"]],
            outputs=chatbot
        )
    
    return interface

if __name__ == "__main__":
    print("🚀 Démarrage JARVIS Interface Simple...")
    print("🌐 Interface disponible sur: http://localhost:7863")
    
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        show_error=True,
        quiet=False
    )
