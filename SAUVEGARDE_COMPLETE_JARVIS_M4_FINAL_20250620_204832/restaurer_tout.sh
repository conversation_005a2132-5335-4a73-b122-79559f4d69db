#!/bin/bash

echo "🔄 RESTAURATION COMPLÈTE JARVIS M4 FINAL"
echo "======================================="
echo "👤 Jean-Luc Passave"
echo ""

# Vérifier qu'on est dans le bon répertoire
if [ ! -f "jarvis_electron_final_complet.js" ]; then
    echo "❌ Fichiers de sauvegarde non trouvés dans ce répertoire"
    exit 1
fi

echo "📁 Restauration des fichiers principaux..."

# Application Electron
echo "🖥️ Restauration Application Electron..."
cp jarvis_electron_final_complet.js ../
cp package.json ../

# Dashboard et interfaces
echo "📋 Restauration Dashboard et interfaces..."
cp dashboard_avec_onglets.py ../
cp visualisation_memoire_thermique.py ../
cp systeme_notifications_jarvis.py ../
cp tableau_bord_final_ultime.py ../

# JARVIS principal
echo "🤖 Restauration JARVIS principal..."
cp jarvis_architecture_multi_fenetres.py ../
cp jarvis_sans_simulation.py ../

# Tests et validation
echo "🧪 Restauration tests..."
cp test_*.py ../
cp validation_*.py ../
cp verification_*.py ../

# Monitoring
echo "📊 Restauration monitoring..."
cp monitoring_*.py ../
cp centre_*.py ../

# Scripts de maintenance
echo "🔧 Restauration scripts maintenance..."
cp *.sh ../
chmod +x ../*.sh

# Documentation
echo "📋 Restauration documentation..."
cp *.md ../

echo ""
echo "✅ RESTAURATION TERMINÉE"
echo "======================="
echo "🚀 Pour lancer l'application Electron :"
echo "   cd .."
echo "   npm run final"
echo ""
echo "📋 Pour lancer le dashboard avec onglets :"
echo "   cd .."
echo "   python3 dashboard_avec_onglets.py"
echo ""
echo "🌟 Pour lancer le tableau de bord ultime :"
echo "   cd .."
echo "   python3 tableau_bord_final_ultime.py"
