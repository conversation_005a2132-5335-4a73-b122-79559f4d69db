#!/bin/bash

# 🧹 NETTOYAGE ET REDÉMARRAGE JARVIS COMPLET
# Jean<PERSON><PERSON> - 2025
# Script pour nettoyer tous les processus et redémarrer proprement

echo "🧹 NETTOYAGE ET REDÉMARRAGE JARVIS COMPLET"
echo "=========================================="
echo "👤 Jean-Luc Passave"
echo "📅 $(date)"
echo ""

# Fonction pour tuer les processus sur un port
kill_port() {
    local port=$1
    echo "🔍 Vérification port $port..."
    
    # Trouver le processus utilisant le port
    local pid=$(lsof -ti:$port)
    
    if [ ! -z "$pid" ]; then
        echo "❌ Port $port occupé par PID $pid - Arrêt..."
        kill -9 $pid 2>/dev/null
        sleep 1
        
        # Vérifier si le processus est vraiment arrêté
        if lsof -ti:$port > /dev/null 2>&1; then
            echo "⚠️ Processus résistant sur port $port - Force kill..."
            sudo kill -9 $pid 2>/dev/null
        else
            echo "✅ Port $port libéré"
        fi
    else
        echo "✅ Port $port libre"
    fi
}

# Arrêter tous les processus Python JARVIS
echo "🐍 Arrêt des processus Python JARVIS..."
pkill -f "python.*jarvis" 2>/dev/null
pkill -f "gradio" 2>/dev/null
sleep 2

# Arrêter tous les processus Electron JARVIS
echo "🖥️ Arrêt des processus Electron JARVIS..."
pkill -f "electron.*jarvis" 2>/dev/null
pkill -f "npm.*final" 2>/dev/null
sleep 2

# Libérer tous les ports JARVIS
echo "🔌 Libération des ports JARVIS..."
ports=(7866 7867 7868 7869 7870 7871 7872 7873 7874 7875 7876 7877 7878 7879 7880 7881 7882 7883 7884 7885 7890 7891)

for port in "${ports[@]}"; do
    kill_port $port
done

# Nettoyer les processus Node.js
echo "📦 Nettoyage Node.js..."
pkill -f "node" 2>/dev/null
sleep 1

# Vérifier que tous les ports sont libres
echo ""
echo "📊 VÉRIFICATION FINALE DES PORTS:"
echo "================================="
for port in "${ports[@]}"; do
    if lsof -ti:$port > /dev/null 2>&1; then
        echo "❌ Port $port: OCCUPÉ"
    else
        echo "✅ Port $port: LIBRE"
    fi
done

echo ""
echo "🧹 NETTOYAGE TERMINÉ"
echo "===================="

# Attendre un peu pour que tout se stabilise
echo "⏳ Attente stabilisation (5 secondes)..."
sleep 5

echo ""
echo "🚀 REDÉMARRAGE JARVIS COMPLET"
echo "============================="

# Aller dans le bon répertoire
cd "/Volumes/seagate/Louna_Electron_Latest"

# Activer l'environnement virtuel
echo "🐍 Activation environnement virtuel..."
source venv_deepseek/bin/activate

# Vérifier les dépendances
echo "📦 Vérification dépendances..."
if [ ! -d "node_modules" ]; then
    echo "📦 Installation dépendances Node.js..."
    npm install
fi

# Démarrer JARVIS principal
echo "🤖 Démarrage JARVIS principal..."
echo "🌐 Dashboard sera disponible sur: http://localhost:7867"
echo "💬 Communication sur: http://localhost:7866"
echo ""
echo "🎯 FONCTIONNALITÉS DISPONIBLES:"
echo "   🖥️ Application Electron Finale (bouton dans dashboard)"
echo "   🎤 Micro natif dans Electron"
echo "   📹 Webcam intégrée"
echo "   🧠 Mémoire thermique complète"
echo "   🍎 Optimisations Apple Silicon M4"
echo ""
echo "🚀 Lancement en cours..."

# Lancer JARVIS
python3 jarvis_architecture_multi_fenetres.py
