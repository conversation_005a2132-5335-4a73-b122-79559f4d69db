#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test Mémoire Thermique avec Electron
Jean-Luc <PERSON>ave - 2025
Test de l'intégration mémoire thermique avec l'application Electron
"""

import gradio as gr
import requests
import json
import time
from datetime import datetime

def test_sauvegarde_memoire():
    """Test de sauvegarde dans la mémoire thermique"""
    try:
        test_data = {
            'user': '<PERSON><PERSON><PERSON>',
            'message': 'Test de sauvegarde mémoire thermique depuis Electron',
            'timestamp': datetime.now().isoformat(),
            'context': 'Application Electron JARVIS M4 Final',
            'importance': 'high'
        }
        
        response = requests.post(
            'http://localhost:7866/api/memory/save',
            json=test_data,
            timeout=10
        )
        
        if response.status_code == 200:
            return "✅ Sauvegarde mémoire thermique réussie"
        else:
            return f"⚠️ Sauvegarde mémoire: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Erreur sauvegarde mémoire: {str(e)}"

def test_recherche_memoire():
    """Test de recherche dans la mémoire thermique"""
    try:
        search_data = {
            'user': 'Jean-Luc Passave',
            'query': 'Application Electron',
            'limit': 5
        }
        
        response = requests.post(
            'http://localhost:7866/api/memory/search',
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data.get('results', [])
            return f"✅ Recherche mémoire: {len(results)} résultats trouvés"
        else:
            return f"⚠️ Recherche mémoire: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Erreur recherche mémoire: {str(e)}"

def test_integration_electron():
    """Test de l'intégration complète Electron-Mémoire"""
    try:
        # Simuler une conversation Electron
        conversation_data = {
            'user': 'Jean-Luc Passave',
            'messages': [
                {
                    'role': 'user',
                    'content': 'JARVIS, peux-tu te souvenir de cette conversation Electron ?',
                    'timestamp': datetime.now().isoformat()
                },
                {
                    'role': 'assistant',
                    'content': 'Oui Jean-Luc, je sauvegarde cette conversation dans ma mémoire thermique.',
                    'timestamp': datetime.now().isoformat()
                }
            ],
            'source': 'Electron App',
            'session_id': f"electron_session_{int(time.time())}"
        }
        
        response = requests.post(
            'http://localhost:7866/api/memory/conversation',
            json=conversation_data,
            timeout=10
        )
        
        if response.status_code == 200:
            return "✅ Intégration Electron-Mémoire fonctionnelle"
        else:
            return f"⚠️ Intégration: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Erreur intégration: {str(e)}"

def test_persistance_memoire():
    """Test de la persistance de la mémoire"""
    try:
        # Vérifier que les données persistent
        response = requests.get(
            'http://localhost:7866/api/memory/stats',
            params={'user': 'Jean-Luc Passave'},
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            total_memories = data.get('total_memories', 0)
            return f"✅ Persistance mémoire: {total_memories} souvenirs stockés"
        else:
            return f"⚠️ Persistance: Status {response.status_code}"
            
    except Exception as e:
        return f"❌ Erreur persistance: {str(e)}"

def run_all_memory_tests():
    """Lance tous les tests de mémoire thermique"""
    results = []
    
    print("🧠 TESTS MÉMOIRE THERMIQUE ELECTRON")
    print("=" * 40)
    
    # Test 1: Sauvegarde
    print("🔍 Test sauvegarde mémoire...")
    result1 = test_sauvegarde_memoire()
    results.append(f"1. Sauvegarde: {result1}")
    print(result1)
    time.sleep(1)
    
    # Test 2: Recherche
    print("🔍 Test recherche mémoire...")
    result2 = test_recherche_memoire()
    results.append(f"2. Recherche: {result2}")
    print(result2)
    time.sleep(1)
    
    # Test 3: Intégration
    print("🔍 Test intégration Electron...")
    result3 = test_integration_electron()
    results.append(f"3. Intégration: {result3}")
    print(result3)
    time.sleep(1)
    
    # Test 4: Persistance
    print("🔍 Test persistance...")
    result4 = test_persistance_memoire()
    results.append(f"4. Persistance: {result4}")
    print(result4)
    
    # Résumé
    success_count = sum(1 for r in results if "✅" in r)
    total_tests = len(results)
    
    print(f"\n📊 RÉSUMÉ: {success_count}/{total_tests} tests réussis")
    
    return "\n".join(results)

def create_memory_test_interface():
    """Interface de test de la mémoire thermique"""
    
    with gr.Blocks(
        title="🧠 Test Mémoire Thermique Electron",
        theme=gr.themes.Soft()
    ) as memory_interface:

        gr.HTML("""
        <div style="text-align: center; background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 25px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.2em;">🧠 TEST MÉMOIRE THERMIQUE ELECTRON</h1>
            <p style="margin: 10px 0; font-size: 1.1em;">Validation intégration mémoire avec application Electron</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 8px; margin: 10px 0;">
                <p style="margin: 0; font-size: 1em;">👤 Jean-Luc Passave | 🖥️ Electron | 🧠 Mémoire Thermique | 💾 Persistance</p>
            </div>
        </div>
        """)

        with gr.Row():
            with gr.Column():
                gr.HTML("""
                <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h3>🎯 Tests Disponibles:</h3>
                    <ul style='text-align: left; margin: 10px 0; line-height: 1.8;'>
                        <li><strong>💾 Sauvegarde</strong> - Test stockage mémoire</li>
                        <li><strong>🔍 Recherche</strong> - Test récupération données</li>
                        <li><strong>🔗 Intégration</strong> - Test Electron-Mémoire</li>
                        <li><strong>⏱️ Persistance</strong> - Test durabilité données</li>
                    </ul>
                </div>
                """)
                
                test_all_memory_btn = gr.Button(
                    "🧠 LANCER TOUS LES TESTS MÉMOIRE",
                    variant="primary",
                    size="lg"
                )
            
            with gr.Column():
                gr.HTML("""
                <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h3>📋 Fonctionnalités Testées:</h3>
                    <ul style='text-align: left; margin: 10px 0; line-height: 1.8;'>
                        <li>Sauvegarde conversations Electron</li>
                        <li>Recherche sémantique dans mémoire</li>
                        <li>Intégration temps réel</li>
                        <li>Persistance entre sessions</li>
                    </ul>
                </div>
                """)

        # Résultats
        memory_results = gr.Textbox(
            label="Résultats Tests Mémoire Thermique",
            lines=10,
            interactive=False
        )

        # Tests individuels
        gr.HTML("<hr style='margin: 30px 0;'>")
        gr.HTML("<h3 style='text-align: center;'>🔍 Tests Individuels</h3>")

        with gr.Row():
            test_save_btn = gr.Button("💾 Test Sauvegarde")
            test_search_btn = gr.Button("🔍 Test Recherche")
            test_integration_btn = gr.Button("🔗 Test Intégration")
            test_persistence_btn = gr.Button("⏱️ Test Persistance")

        # Informations sur la mémoire thermique
        gr.HTML("""
        <div style='background: #fff3e0; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #FF9800;'>
            <h3 style='color: #F57C00; margin: 0 0 15px 0;'>🧠 À propos de la Mémoire Thermique</h3>
            <p style='margin: 10px 0; color: #333;'>
                La mémoire thermique de JARVIS stocke toutes vos conversations et interactions de manière persistante.
                Elle utilise des embeddings sémantiques pour permettre une recherche intelligente et contextuelle.
            </p>
            <p style='margin: 10px 0; color: #333;'>
                <strong>Fonctionnalités :</strong> Stockage illimité, recherche sémantique, indexation temporelle, 
                compression automatique, et intégration complète avec l'application Electron.
            </p>
        </div>
        """)

        # Connexions
        test_all_memory_btn.click(
            fn=run_all_memory_tests,
            outputs=[memory_results]
        )
        
        test_save_btn.click(
            fn=test_sauvegarde_memoire,
            outputs=[memory_results]
        )
        
        test_search_btn.click(
            fn=test_recherche_memoire,
            outputs=[memory_results]
        )
        
        test_integration_btn.click(
            fn=test_integration_electron,
            outputs=[memory_results]
        )
        
        test_persistence_btn.click(
            fn=test_persistance_memoire,
            outputs=[memory_results]
        )

    return memory_interface

if __name__ == "__main__":
    print("🧠 DÉMARRAGE TEST MÉMOIRE THERMIQUE ELECTRON")
    print("===========================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Test intégration mémoire avec Electron")
    print("")
    
    # Créer et lancer l'interface
    memory_app = create_memory_test_interface()
    
    print("✅ Interface de test mémoire créée")
    print("🌐 Lancement sur http://localhost:7896")
    
    memory_app.launch(
        server_name="127.0.0.1",
        server_port=7896,
        share=False,
        show_error=True,
        quiet=False
    )
