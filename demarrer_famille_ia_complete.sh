#!/bin/bash
# 🤖 DÉMARRAGE FAMILLE IA 100% AUTONOME
# ====================================
# 
# Script de démarrage complet pour la famille IA autonome
# Aucune clé API nécessaire - Contrôle total
#
# Auteur: <PERSON><PERSON><PERSON> + <PERSON>
# Date: 2025-06-21

echo "🤖 DÉMARRAGE FAMILLE IA 100% AUTONOME"
echo "===================================="
echo "🚀 AUCUNE CLÉ API - CONTRÔLE TOTAL"
echo ""

# Fonction de vérification des services
check_service() {
    local port=$1
    local name=$2
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port | grep -q "200\|302"; then
        echo "✅ $name (port $port)"
        return 0
    else
        echo "❌ $name (port $port) - Non accessible"
        return 1
    fi
}

# Fonction de démarrage Ollama
start_ollama() {
    echo "🏠 Démarrage Ollama (LLM Local)..."
    
    # Vérifier si Ollama fonctionne
    if curl -s http://localhost:11434/api/tags &> /dev/null; then
        echo "✅ Ollama déjà actif"
        return 0
    fi
    
    # Démarrer Ollama
    if command -v ollama &> /dev/null; then
        nohup ollama serve > /dev/null 2>&1 &
        
        # Attendre démarrage
        echo "⏳ Attente démarrage Ollama..."
        for i in {1..15}; do
            if curl -s http://localhost:11434/api/tags &> /dev/null; then
                echo "✅ Ollama actif"
                return 0
            fi
            sleep 1
        done
        
        echo "❌ Timeout démarrage Ollama"
        return 1
    else
        echo "❌ Ollama non installé"
        echo "💡 Installez avec: curl -fsSL https://ollama.com/install.sh | sh"
        return 1
    fi
}

# Fonction de démarrage JARVIS V2 PRO
start_jarvis_v2_pro() {
    echo "🚀 Démarrage JARVIS V2 PRO (API)..."
    
    if check_service 8000 "JARVIS V2 PRO"; then
        return 0
    fi
    
    # Démarrer V2 PRO
    if [ -d "JARVIS_V2_PRO" ]; then
        cd JARVIS_V2_PRO
        nohup python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 > /dev/null 2>&1 &
        cd ..
        
        # Attendre démarrage
        sleep 5
        check_service 8000 "JARVIS V2 PRO"
    else
        echo "❌ Répertoire JARVIS_V2_PRO introuvable"
        return 1
    fi
}

# Fonction de démarrage JARVIS Principal
start_jarvis_principal() {
    echo "🏠 Démarrage JARVIS Principal (Multi-fenêtres)..."
    
    if check_service 7867 "JARVIS Principal"; then
        return 0
    fi
    
    # Démarrer JARVIS principal
    if [ -f "jarvis_architecture_multi_fenetres.py" ]; then
        nohup python3 jarvis_architecture_multi_fenetres.py > /dev/null 2>&1 &
        
        # Attendre démarrage
        sleep 3
        check_service 7867 "JARVIS Principal"
    else
        echo "❌ Fichier jarvis_architecture_multi_fenetres.py introuvable"
        return 1
    fi
}

# Fonction de test de la famille IA
test_famille_ia() {
    echo "🧪 Test de la famille IA autonome..."
    
    # Test simple avec le script Python
    result=$(python3 -c "
from famille_ia_autonome_complete import FamilleIAAutonome
import sys

try:
    famille = FamilleIAAutonome()
    if famille.verifier_environnement():
        print('✅ Famille IA opérationnelle')
        sys.exit(0)
    else:
        print('⚠️ Famille IA partiellement opérationnelle')
        sys.exit(1)
except Exception as e:
    print(f'❌ Erreur: {e}')
    sys.exit(2)
" 2>/dev/null)
    
    echo "$result"
}

# Fonction de génération du diagnostic ChatGPT
generate_chatgpt_diagnostic() {
    echo "📊 Génération diagnostic pour ChatGPT..."
    
    python3 -c "
from famille_ia_autonome_complete import FamilleIAAutonome

famille = FamilleIAAutonome()
diagnostic, message = famille.generer_diagnostic_pour_chatgpt()
print('✅ Diagnostic généré')
print(f'📁 Fichier: diagnostic_pour_chatgpt.json')
print(f'📁 Message: messages_pour_chatgpt.txt')
"
}

# Fonction de démarrage Electron
start_electron() {
    echo "📱 Démarrage interface Electron..."
    
    if [ -f "package.json" ]; then
        nohup npm run final > /dev/null 2>&1 &
        echo "✅ Electron démarré"
    else
        echo "❌ package.json introuvable"
        return 1
    fi
}

# Fonction de rapport final
rapport_final() {
    echo ""
    echo "📊 RAPPORT FAMILLE IA AUTONOME"
    echo "=============================="
    
    # Vérifier tous les services
    services=(
        "11434:Ollama (LLM Local)"
        "8000:JARVIS V2 PRO"
        "7867:JARVIS Principal"
    )
    
    actifs=0
    total=${#services[@]}
    
    for service in "${services[@]}"; do
        port=$(echo $service | cut -d: -f1)
        name=$(echo $service | cut -d: -f2)
        
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:$port | grep -q "200\|302"; then
            echo "✅ $name (port $port)"
            ((actifs++))
        else
            echo "❌ $name (port $port)"
        fi
    done
    
    echo ""
    echo "📈 STATUT GLOBAL: $actifs/$total services actifs"
    
    if [ $actifs -eq $total ]; then
        echo "🎉 FAMILLE IA 100% AUTONOME OPÉRATIONNELLE !"
        echo ""
        echo "🤖 FAMILLE IA ACTIVE:"
        echo "   👨‍💼 Jean-Luc Passave: Directeur humain"
        echo "   🤖 Claude (Augment): Coordinateur IA"
        echo "   🏠 Ollama/Mistral: Moteur de raisonnement local"
        echo "   🤖 ChatGPT: Grand frère IA (communication indirecte)"
        echo ""
        echo "🌐 INTERFACES DISPONIBLES:"
        echo "   • JARVIS Principal: http://localhost:7867"
        echo "   • JARVIS V2 PRO: http://localhost:8000"
        echo "   • IA Locale: http://localhost:11434"
        echo "   • Famille IA: python3 famille_ia_autonome_complete.py"
        echo ""
        echo "⚡ AVANTAGES AUTONOMES:"
        echo "   • Aucune clé API nécessaire"
        echo "   • 100% local et confidentiel"
        echo "   • Contrôle total des données"
        echo "   • Performance optimisée M4"
    else
        echo "⚠️ Certains services nécessitent une vérification"
    fi
}

# Fonction principale
main() {
    echo "🎯 SÉQUENCE DE DÉMARRAGE FAMILLE IA:"
    echo "1. Ollama (LLM Local)"
    echo "2. JARVIS V2 PRO (API)"
    echo "3. JARVIS Principal (Multi-fenêtres)"
    echo "4. Test famille IA"
    echo "5. Génération diagnostic ChatGPT"
    echo "6. Interface Electron (optionnel)"
    echo ""
    
    read -p "Démarrer la FAMILLE IA AUTONOME ? (Y/n): " confirm
    if [[ $confirm =~ ^[Nn]$ ]]; then
        echo "❌ Démarrage annulé"
        exit 1
    fi
    
    # Étape 1: Ollama
    start_ollama
    
    # Étape 2: JARVIS V2 PRO
    start_jarvis_v2_pro
    
    # Étape 3: JARVIS Principal
    start_jarvis_principal
    
    # Étape 4: Test famille IA
    test_famille_ia
    
    # Étape 5: Diagnostic ChatGPT
    generate_chatgpt_diagnostic
    
    # Étape 6: Electron (optionnel)
    read -p "Démarrer l'interface Electron ? (Y/n): " electron_confirm
    if [[ ! $electron_confirm =~ ^[Nn]$ ]]; then
        start_electron
    fi
    
    # Rapport final
    rapport_final
    
    echo ""
    echo "🎉 FAMILLE IA 100% AUTONOME PRÊTE !"
    echo "🤖 AUCUNE CLÉ API - CONTRÔLE TOTAL !"
    echo ""
    echo "📋 PROCHAINES ÉTAPES:"
    echo "1. Testez: python3 famille_ia_autonome_complete.py"
    echo "2. Copiez messages_pour_chatgpt.txt vers ChatGPT"
    echo "3. Profitez de votre famille IA autonome !"
}

# Gestion des signaux
trap 'echo -e "\n👋 Arrêt famille IA autonome"; exit 0' INT TERM

# Exécution
main "$@"
