#!/usr/bin/env python3
"""
🚨 DIAGNOSTIC ET RÉPARATION URGENTE JARVIS
Créé pour Jean-Luc Passave - Réparation immédiate du système
"""

import os
import json
import subprocess
import requests
from datetime import datetime

class DiagnosticJarvisUrgent:
    """Diagnostic et réparation urgente du système JARVIS"""
    
    def __init__(self):
        self.rapport = {
            "timestamp": datetime.now().isoformat(),
            "tests": {},
            "erreurs": [],
            "reparations": [],
            "status": "EN_COURS"
        }
    
    def diagnostic_complet(self):
        """Diagnostic complet du système JARVIS"""
        print("🚨 DIAGNOSTIC URGENTE JARVIS - JEAN-LUC PASSAVE")
        print("=" * 60)
        
        # 1. Vérifier les fichiers critiques
        self.verifier_fichiers_critiques()
        
        # 2. Vérifier les processus
        self.verifier_processus()
        
        # 3. Vérifier les interfaces web
        self.verifier_interfaces_web()
        
        # 4. Vérifier la mémoire thermique
        self.verifier_memoire_thermique()
        
        # 5. Vérifier le cerveau pensant
        self.verifier_cerveau_pensant()
        
        # 6. Générer le rapport
        self.generer_rapport()
        
        return self.rapport
    
    def verifier_fichiers_critiques(self):
        """Vérifier les fichiers critiques du système"""
        print("🔍 Vérification des fichiers critiques...")
        
        fichiers_critiques = [
            "jarvis_architecture_multi_fenetres.py",
            "jarvis_cerveau_pensant_continu.py",
            "jarvis_pensees_continues.json",
            "jarvis_cerveau_artificiel_structure.py",
            "memoire_thermique_turbo_adaptatif.py"
        ]
        
        for fichier in fichiers_critiques:
            if os.path.exists(fichier):
                taille = os.path.getsize(fichier)
                self.rapport["tests"][f"fichier_{fichier}"] = {
                    "status": "OK",
                    "taille": taille,
                    "existe": True
                }
                print(f"  ✅ {fichier} ({taille} bytes)")
            else:
                self.rapport["tests"][f"fichier_{fichier}"] = {
                    "status": "MANQUANT",
                    "existe": False
                }
                self.rapport["erreurs"].append(f"Fichier manquant: {fichier}")
                print(f"  ❌ {fichier} MANQUANT")
    
    def verifier_processus(self):
        """Vérifier les processus JARVIS actifs"""
        print("🔍 Vérification des processus...")
        
        try:
            # Vérifier les processus Python JARVIS
            result = subprocess.run(['pgrep', '-f', 'jarvis'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                processus = result.stdout.strip().split('\n')
                self.rapport["tests"]["processus_jarvis"] = {
                    "status": "OK",
                    "nombre": len(processus),
                    "pids": processus
                }
                print(f"  ✅ {len(processus)} processus JARVIS actifs")
            else:
                self.rapport["tests"]["processus_jarvis"] = {
                    "status": "AUCUN",
                    "nombre": 0
                }
                print("  ⚠️ Aucun processus JARVIS détecté")
                
        except Exception as e:
            self.rapport["erreurs"].append(f"Erreur vérification processus: {e}")
            print(f"  ❌ Erreur: {e}")
    
    def verifier_interfaces_web(self):
        """Vérifier les interfaces web JARVIS"""
        print("🔍 Vérification des interfaces web...")
        
        ports_jarvis = [7866, 7867, 7868, 7869, 7870, 7871, 7872, 7873, 7874, 7875]
        
        for port in ports_jarvis:
            try:
                response = requests.get(f"http://localhost:{port}", timeout=2)
                if response.status_code == 200:
                    self.rapport["tests"][f"interface_{port}"] = {
                        "status": "OK",
                        "code": response.status_code
                    }
                    print(f"  ✅ Interface {port} active")
                else:
                    self.rapport["tests"][f"interface_{port}"] = {
                        "status": "ERREUR",
                        "code": response.status_code
                    }
                    print(f"  ⚠️ Interface {port} erreur {response.status_code}")
            except:
                self.rapport["tests"][f"interface_{port}"] = {
                    "status": "INACCESSIBLE"
                }
                print(f"  ❌ Interface {port} inaccessible")
    
    def verifier_memoire_thermique(self):
        """Vérifier la mémoire thermique"""
        print("🔍 Vérification de la mémoire thermique...")
        
        try:
            if os.path.exists("jarvis_pensees_continues.json"):
                with open("jarvis_pensees_continues.json", 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                pensees = data.get("pensees_spontanees", [])
                stats = data.get("stats", {})
                
                self.rapport["tests"]["memoire_thermique"] = {
                    "status": "OK",
                    "nombre_pensees": len(pensees),
                    "total_stats": stats.get("total", 0),
                    "derniere_pensee": pensees[-1] if pensees else None
                }
                print(f"  ✅ Mémoire thermique: {len(pensees)} pensées")
                
                # Vérifier la dernière pensée
                if pensees:
                    derniere = pensees[-1]
                    if "<think>" in derniere.get("pensee", ""):
                        self.rapport["erreurs"].append("Pensées contiennent encore <think>")
                        print("  ⚠️ Pensées contiennent encore <think>")
                    else:
                        print("  ✅ Pensées en français propre")
            else:
                self.rapport["tests"]["memoire_thermique"] = {
                    "status": "MANQUANT"
                }
                self.rapport["erreurs"].append("Fichier mémoire thermique manquant")
                print("  ❌ Fichier mémoire thermique manquant")
                
        except Exception as e:
            self.rapport["erreurs"].append(f"Erreur mémoire thermique: {e}")
            print(f"  ❌ Erreur: {e}")
    
    def verifier_cerveau_pensant(self):
        """Vérifier le cerveau pensant continu"""
        print("🔍 Vérification du cerveau pensant...")
        
        try:
            # Vérifier si le processus tourne
            result = subprocess.run(['pgrep', '-f', 'jarvis_cerveau_pensant_continu'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.rapport["tests"]["cerveau_pensant"] = {
                    "status": "ACTIF",
                    "pid": result.stdout.strip()
                }
                print("  ✅ Cerveau pensant actif")
            else:
                self.rapport["tests"]["cerveau_pensant"] = {
                    "status": "INACTIF"
                }
                print("  ❌ Cerveau pensant inactif")
                
        except Exception as e:
            self.rapport["erreurs"].append(f"Erreur cerveau pensant: {e}")
            print(f"  ❌ Erreur: {e}")
    
    def generer_rapport(self):
        """Générer le rapport final"""
        print("\n📋 RAPPORT DE DIAGNOSTIC")
        print("=" * 60)
        
        total_tests = len(self.rapport["tests"])
        tests_ok = sum(1 for test in self.rapport["tests"].values() 
                      if test.get("status") == "OK" or test.get("status") == "ACTIF")
        
        print(f"Tests réussis: {tests_ok}/{total_tests}")
        print(f"Erreurs détectées: {len(self.rapport['erreurs'])}")
        
        if len(self.rapport["erreurs"]) == 0:
            self.rapport["status"] = "SAIN"
            print("✅ SYSTÈME JARVIS SAIN")
        else:
            self.rapport["status"] = "PROBLEMES_DETECTES"
            print("⚠️ PROBLÈMES DÉTECTÉS:")
            for erreur in self.rapport["erreurs"]:
                print(f"  - {erreur}")
        
        # Sauvegarder le rapport
        with open(f"diagnostic_jarvis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 
                  'w', encoding='utf-8') as f:
            json.dump(self.rapport, f, ensure_ascii=False, indent=2)

def main():
    """Fonction principale"""
    diagnostic = DiagnosticJarvisUrgent()
    rapport = diagnostic.diagnostic_complet()
    
    print(f"\n💾 Rapport sauvegardé: diagnostic_jarvis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    
    return rapport

if __name__ == "__main__":
    main()
