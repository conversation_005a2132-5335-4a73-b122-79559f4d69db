#!/usr/bin/env python3
"""
TEST COMPLET CONNEXIONS AGENT JARVIS - JEAN-LUC PASSAVE
Vérifie si l'agent est connecté dans toutes les applications
"""

import requests
import json
import time

def test_agent_interface(port, nom_interface):
    """Test si l'agent JARVIS répond sur une interface"""
    try:
        print(f"🔍 Test {nom_interface} (port {port})...")
        
        # Test simple de connexion
        response = requests.get(f"http://localhost:{port}", timeout=5)
        
        if response.status_code == 200:
            print(f"   ✅ Interface accessible")
            
            # Vérifier si JARVIS est mentionné dans la page
            content = response.text.lower()
            if "jarvis" in content:
                print(f"   ✅ Agent JARVIS détecté dans l'interface")
                return True
            else:
                print(f"   ⚠️ Agent JARVIS non détecté dans l'interface")
                return False
        else:
            print(f"   ❌ Interface non accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur connexion: {e}")
        return False

def test_agent_deepseek():
    """Test si l'agent DeepSeek R1 8B répond"""
    try:
        print(f"🔍 Test Agent DeepSeek R1 8B...")
        
        payload = {
            "model": "deepseek-r1",
            "messages": [
                {"role": "user", "content": "Test connexion agent JARVIS"}
            ],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        response = requests.post("http://localhost:8000/v1/chat/completions", 
                               json=payload, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"   ✅ Agent DeepSeek actif")
            print(f"   💬 Réponse: {content[:100]}...")
            return True
        else:
            print(f"   ❌ Agent DeepSeek erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur agent DeepSeek: {e}")
        return False

def test_mémoire_thermique():
    """Test si la mémoire thermique est accessible"""
    try:
        print(f"🔍 Test Mémoire Thermique...")
        
        import os
        if os.path.exists("thermal_memory_persistent.json"):
            with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            neurones = len(data.get("neuron_memories", []))
            print(f"   ✅ Mémoire thermique accessible")
            print(f"   🧠 Neurones: {neurones}")
            return True
        else:
            print(f"   ❌ Fichier mémoire thermique introuvable")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur mémoire thermique: {e}")
        return False

def test_pensées_autonomes():
    """Test si les pensées autonomes fonctionnent"""
    try:
        print(f"🔍 Test Pensées Autonomes...")
        
        import os
        if os.path.exists("jarvis_pensees_autonomes.json"):
            with open("jarvis_pensees_autonomes.json", 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            pensees = len(data.get("pensees_autonomes", []))
            print(f"   ✅ Pensées autonomes accessibles")
            print(f"   💭 Pensées générées: {pensees}")
            return True
        else:
            print(f"   ⚠️ Aucune pensée autonome générée encore")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur pensées autonomes: {e}")
        return False

def test_multi_agents():
    """Test si les multi-agents communiquent"""
    try:
        print(f"🔍 Test Multi-Agents...")
        
        # Test communication Agent 1
        payload = {
            "model": "deepseek-r1",
            "messages": [
                {
                    "role": "system",
                    "content": "Tu es Agent 1 de JARVIS pour Jean-Luc Passave. Réponds brièvement."
                },
                {"role": "user", "content": "Test Agent 1"}
            ],
            "max_tokens": 30,
            "temperature": 0.7
        }
        
        response = requests.post("http://localhost:8000/v1/chat/completions", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ Multi-agents actifs")
            return True
        else:
            print(f"   ❌ Multi-agents erreur: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur multi-agents: {e}")
        return False

def diagnostic_complet():
    """Diagnostic complet de toutes les connexions"""
    print("🚀 DIAGNOSTIC COMPLET CONNEXIONS AGENT JARVIS")
    print("=" * 70)
    
    # Interfaces à tester
    interfaces = [
        (7866, "Communication Principale"),
        (7867, "Dashboard Principal"),
        (7868, "Éditeur Code"),
        (7869, "Pensées JARVIS"),
        (7870, "Configuration"),
        (7871, "WhatsApp"),
        (7872, "Sécurité"),
        (7873, "Monitoring"),
        (7874, "Mémoire Thermique"),
        (7875, "Créativité"),
        (7876, "Musique"),
        (7877, "Système"),
        (7878, "Recherche Web"),
        (7879, "Interface Vocale"),
        (7880, "Multi-Agents")
    ]
    
    # Test interfaces
    interfaces_ok = 0
    for port, nom in interfaces:
        if test_agent_interface(port, nom):
            interfaces_ok += 1
        time.sleep(0.5)
    
    print(f"\n📊 RÉSULTATS INTERFACES:")
    print(f"   ✅ Interfaces fonctionnelles: {interfaces_ok}/{len(interfaces)}")
    
    # Test composants critiques
    print(f"\n🔧 TEST COMPOSANTS CRITIQUES:")
    
    composants_ok = 0
    total_composants = 5
    
    if test_agent_deepseek():
        composants_ok += 1
    
    if test_mémoire_thermique():
        composants_ok += 1
    
    if test_pensées_autonomes():
        composants_ok += 1
    
    if test_multi_agents():
        composants_ok += 1
    
    # Test port multi-agents spécifique
    try:
        response = requests.get("http://localhost:7880", timeout=3)
        if response.status_code == 200:
            print(f"   ✅ Interface Multi-Agents accessible")
            composants_ok += 1
        else:
            print(f"   ❌ Interface Multi-Agents inaccessible")
    except:
        print(f"   ❌ Interface Multi-Agents erreur connexion")
    
    print(f"\n📊 RÉSULTATS COMPOSANTS:")
    print(f"   ✅ Composants fonctionnels: {composants_ok}/{total_composants}")
    
    # Résumé final
    print(f"\n" + "=" * 70)
    print(f"📋 RÉSUMÉ DIAGNOSTIC COMPLET:")
    print(f"=" * 70)
    
    pourcentage_interfaces = (interfaces_ok / len(interfaces)) * 100
    pourcentage_composants = (composants_ok / total_composants) * 100
    
    print(f"🌐 Interfaces: {interfaces_ok}/{len(interfaces)} ({pourcentage_interfaces:.1f}%)")
    print(f"🔧 Composants: {composants_ok}/{total_composants} ({pourcentage_composants:.1f}%)")
    
    if pourcentage_interfaces >= 80 and pourcentage_composants >= 80:
        print(f"\n🎉 ✅ JARVIS SYSTÈME GLOBAL: FONCTIONNEL")
        print(f"   Agent connecté et opérationnel dans la majorité des applications")
    elif pourcentage_interfaces >= 60 or pourcentage_composants >= 60:
        print(f"\n⚠️ JARVIS SYSTÈME GLOBAL: PARTIELLEMENT FONCTIONNEL")
        print(f"   Certaines connexions nécessitent des corrections")
    else:
        print(f"\n❌ JARVIS SYSTÈME GLOBAL: PROBLÈMES MAJEURS")
        print(f"   Connexions agent nécessitent intervention urgente")
    
    return pourcentage_interfaces, pourcentage_composants

if __name__ == "__main__":
    interfaces_score, composants_score = diagnostic_complet()
    
    if interfaces_score >= 80 and composants_score >= 80:
        print(f"\n🚀 DIAGNOSTIC: SYSTÈME OPÉRATIONNEL")
    else:
        print(f"\n🔧 DIAGNOSTIC: CORRECTIONS NÉCESSAIRES")
