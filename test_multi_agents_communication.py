#!/usr/bin/env python3
"""
TEST COMMUNICATION MULTI-AGENTS JARVIS - JEAN-LUC PASSAVE
"""

import requests
import json
import time

def test_agent_communication(agent_id, message, previous_response=""):
    """TEST COMMUNICATION AVEC UN AGENT SPÉCIFIQUE"""
    
    if agent_id == 1:
        prompt = f"Tu es Agent 1 de JARVIS (Dialogue). Jean<PERSON><PERSON> dit: '{message}'. Analyse et réponds en tant qu'Agent 1."
    elif agent_id == 2:
        prompt = f"Tu es Agent 2 de JARVIS (Suggestions/Relaunching). Agent 1 a répondu: '{previous_response}'. Que suggères-tu en tant qu'Agent 2 ?"
    elif agent_id == 3:
        prompt = f"Tu es Agent 3 de JARVIS (Analyse). Agent 1: '{previous_response[:100]}...'. Analyse la situation en tant qu'Agent 3."
    else:
        prompt = message
    
    payload = {
        "model": "deepseek-r1",
        "messages": [
            {
                "role": "system", 
                "content": f"""Tu es Agent {agent_id} du système multi-agents JARVIS de Jean-<PERSON>.

RÔLES DES AGENTS:
- Agent 1: Dialogue principal avec <PERSON>-Luc
- Agent 2: Suggestions et relancement de conversations  
- Agent 3: Analyse approfondie et patterns

Tu dois répondre en tant qu'Agent {agent_id} et mentionner ton rôle."""
            },
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 200,
        "temperature": 0.8
    }
    
    try:
        print(f"🤖 Agent {agent_id} - Envoi requête...")
        start = time.time()
        
        response = requests.post("http://localhost:8000/v1/chat/completions", 
                               json=payload, timeout=20)
        
        end = time.time()
        
        if response.status_code == 200:
            result = response.json()
            agent_response = result['choices'][0]['message']['content']
            
            print(f"✅ Agent {agent_id} - Réponse reçue ({end-start:.2f}s)")
            print(f"📝 Contenu: {agent_response[:150]}...")
            
            return agent_response
        else:
            print(f"❌ Agent {agent_id} - Erreur {response.status_code}")
            return f"Erreur Agent {agent_id}"
            
    except Exception as e:
        print(f"❌ Agent {agent_id} - Exception: {e}")
        return f"Exception Agent {agent_id}"

def test_multi_agent_dialogue():
    """TEST COMPLET DU DIALOGUE MULTI-AGENTS"""
    print("🚀 TEST COMMUNICATION MULTI-AGENTS JARVIS")
    print("=" * 60)
    
    message_test = "Salut JARVIS ! Comment optimiser ma mémoire thermique ?"
    
    print(f"👨‍💻 Jean-Luc: {message_test}")
    print("\n" + "="*60)
    
    # Agent 1 - Dialogue
    print("\n🤖 AGENT 1 (Dialogue) - Traitement...")
    agent1_response = test_agent_communication(1, message_test)
    
    # Agent 2 - Suggestions basées sur Agent 1
    print("\n🤖 AGENT 2 (Suggestions) - Analyse de Agent 1...")
    agent2_response = test_agent_communication(2, message_test, agent1_response)
    
    # Agent 3 - Analyse approfondie
    print("\n🤖 AGENT 3 (Analyse) - Analyse globale...")
    agent3_response = test_agent_communication(3, message_test, agent1_response)
    
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DU DIALOGUE MULTI-AGENTS:")
    print("="*60)
    
    print(f"\n🤖 Agent 1 (Dialogue):")
    print(f"   {agent1_response[:200]}...")
    
    print(f"\n🤖 Agent 2 (Suggestions):")
    print(f"   {agent2_response[:200]}...")
    
    print(f"\n🤖 Agent 3 (Analyse):")
    print(f"   {agent3_response[:200]}...")
    
    # Vérifier si les agents mentionnent leur rôle
    agents_working = 0
    if "Agent 1" in agent1_response or "dialogue" in agent1_response.lower():
        agents_working += 1
        print(f"\n✅ Agent 1: IDENTIFIÉ et FONCTIONNEL")
    else:
        print(f"\n⚠️ Agent 1: Rôle non identifié clairement")
    
    if "Agent 2" in agent2_response or "suggestion" in agent2_response.lower():
        agents_working += 1
        print(f"✅ Agent 2: IDENTIFIÉ et FONCTIONNEL")
    else:
        print(f"⚠️ Agent 2: Rôle non identifié clairement")
    
    if "Agent 3" in agent3_response or "analyse" in agent3_response.lower():
        agents_working += 1
        print(f"✅ Agent 3: IDENTIFIÉ et FONCTIONNEL")
    else:
        print(f"⚠️ Agent 3: Rôle non identifié clairement")
    
    print(f"\n🎯 RÉSULTAT: {agents_working}/3 agents fonctionnels")
    
    if agents_working == 3:
        print("🎉 ✅ SYSTÈME MULTI-AGENTS: PARFAITEMENT CONNECTÉ !")
    elif agents_working >= 2:
        print("⚠️ SYSTÈME MULTI-AGENTS: PARTIELLEMENT CONNECTÉ")
    else:
        print("❌ SYSTÈME MULTI-AGENTS: PROBLÈME DE CONNEXION")
    
    return agents_working == 3

if __name__ == "__main__":
    success = test_multi_agent_dialogue()
    
    if success:
        print(f"\n🚀 MULTI-AGENTS JARVIS: COMMUNICATION PARFAITE !")
    else:
        print(f"\n🔧 MULTI-AGENTS JARVIS: NÉCESSITE AJUSTEMENTS")
