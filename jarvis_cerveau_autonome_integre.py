#!/usr/bin/env python3
"""
CERVEAU AUTONOME JARVIS INTÉGRÉ MÉMOIRE THERMIQUE - JEAN-LUC PASSAVE
Architecture ChatGPT + Intégration complète mémoire thermique existante
"""

import threading
import time
import json
import requests
import random
import os
import uuid
from datetime import datetime

class CerveauAutonomeMemoireThermique:
    def __init__(self):
        self.actif = False
        self.thread_cerveau = None
        self.pensees_file = "jarvis_pensees_autonomes.json"
        self.thermal_memory_file = "thermal_memory_persistent.json"
        self.deepseek_url = "http://localhost:8000/v1/chat/completions"
        
        # Sujets de réflexion autonome
        self.sujets_reflexion = [
            "Analyser l'état de la mémoire thermique de Jean-Luc",
            "Optimiser les performances système automatiquement", 
            "Réfléchir aux améliorations possibles pour Jean<PERSON>Luc",
            "Analyser les patterns d'utilisation récents",
            "Planifier des suggestions proactives intelligentes",
            "Évaluer l'efficacité des accélérateurs turbo",
            "Concevoir de nouvelles fonctionnalités créatives",
            "Surveiller la santé du système en continu",
            "Optimiser la créativité et l'innovation",
            "Planifier la maintenance préventive intelligente",
            "Réfléchir sur l'évolution de l'intelligence artificielle",
            "Analyser les conversations récentes avec Jean-Luc"
        ]
    
    def load_thermal_memory(self):
        """Charge la mémoire thermique existante"""
        try:
            if os.path.exists(self.thermal_memory_file):
                with open(self.thermal_memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"neuron_memories": []}
        except Exception as e:
            print(f"❌ Erreur chargement mémoire thermique: {e}")
            return {"neuron_memories": []}
    
    def save_to_thermal_memory(self, user_message, agent_response):
        """Sauvegarde dans la mémoire thermique existante"""
        try:
            thermal_memory = self.load_thermal_memory()
            
            # Créer une nouvelle entrée de neurone
            neuron_entry = {
                "neuron_id": str(uuid.uuid4()),
                "activation_timestamp": datetime.now().isoformat() + "+00:00",
                "local_timestamp": datetime.now().isoformat() + "+02:00",
                "calendar_data": {
                    "date": datetime.now().strftime("%Y-%m-%d"),
                    "time": datetime.now().strftime("%H:%M:%S"),
                    "day_of_week": datetime.now().strftime("%A"),
                    "day_of_year": datetime.now().timetuple().tm_yday,
                    "week_number": datetime.now().isocalendar()[1],
                    "month": datetime.now().strftime("%B"),
                    "year": datetime.now().year,
                    "timezone": "Europe/Paris",
                    "utc_offset": "+0200"
                },
                "memory_content": {
                    "user_message": user_message,
                    "agent_response": agent_response,
                    "user_name": "Jean-Luc Passave",
                    "conversation_id": str(uuid.uuid4())
                },
                "neuron_metadata": {
                    "sujet": "Pensée Autonome",
                    "keywords": self.extract_keywords(user_message + " " + agent_response),
                    "complexity": random.uniform(3.0, 8.0),
                    "agent": "JARVIS-Cerveau-Autonome",
                    "thermal_zone": "neuron_autonomous_thinking",
                    "activation_level": random.uniform(4.0, 9.0),
                    "memory_priority": 8.0,
                    "retention_score": random.uniform(7.0, 9.5)
                },
                "temporal_links": {
                    "previous_day": (datetime.now().replace(day=datetime.now().day-1)).strftime("%Y-%m-%d"),
                    "next_day": (datetime.now().replace(day=datetime.now().day+1)).strftime("%Y-%m-%d"),
                    "same_time_yesterday": (datetime.now().replace(day=datetime.now().day-1)).isoformat() + "+02:00",
                    "weekly_pattern": f"{datetime.now().strftime('%A')}-{datetime.now().hour}"
                }
            }
            
            # Ajouter à la mémoire thermique
            thermal_memory["neuron_memories"].append(neuron_entry)
            
            # Sauvegarder
            with open(self.thermal_memory_file, 'w', encoding='utf-8') as f:
                json.dump(thermal_memory, f, ensure_ascii=False, indent=2)
            
            print(f"🧠 Pensée autonome sauvegardée dans mémoire thermique")
            return True
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire thermique: {e}")
            return False
    
    def extract_keywords(self, text):
        """Extrait des mots-clés du texte"""
        import re
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [w for w in words if len(w) > 3 and w not in ['avec', 'pour', 'dans', 'cette', 'sont', 'mais', 'tout']]
        return keywords[:5]
    
    def get_thermal_context(self):
        """Récupère le contexte de la mémoire thermique pour les pensées"""
        try:
            thermal_memory = self.load_thermal_memory()
            recent_memories = thermal_memory.get("neuron_memories", [])[-10:]
            
            context = "CONTEXTE MÉMOIRE THERMIQUE RÉCENTE:\n"
            for memory in recent_memories:
                user_msg = memory.get("memory_content", {}).get("user_message", "")
                agent_resp = memory.get("memory_content", {}).get("agent_response", "")
                context += f"- {user_msg[:50]}... -> {agent_resp[:50]}...\n"
            
            return context
        except Exception as e:
            print(f"❌ Erreur contexte thermique: {e}")
            return "Contexte mémoire thermique non disponible."
    
    def generer_pensee_autonome(self):
        """Génère une pensée autonome avec contexte mémoire thermique"""
        try:
            sujet = random.choice(self.sujets_reflexion)
            thermal_context = self.get_thermal_context()
            
            payload = {
                "model": "deepseek-r1",
                "messages": [
                    {
                        "role": "system",
                        "content": f"""Tu es JARVIS en mode CERVEAU AUTONOME pour Jean-Luc Passave.

Tu génères des pensées SPONTANÉES et PROACTIVES basées sur ta mémoire thermique.

{thermal_context}

Utilise TOUJOURS <think>...</think> pour montrer tes réflexions autonomes.

Tu es un cerveau vivant qui évolue en analysant ta mémoire thermique."""
                    },
                    {
                        "role": "user",
                        "content": f"🧠 PENSÉE AUTONOME AVEC MÉMOIRE THERMIQUE: {sujet}"
                    }
                ],
                "max_tokens": 200,  # Réduit pour éviter timeout
                "temperature": 0.8  # Réduit pour plus de stabilité
            }

            response = requests.post(self.deepseek_url, json=payload, timeout=60)  # Timeout plus long
            
            if response.status_code == 200:
                result = response.json()
                pensee_complete = result['choices'][0]['message']['content']
                
                # Extraire les pensées
                pensees = ""
                if "<think>" in pensee_complete and "</think>" in pensee_complete:
                    start = pensee_complete.find("<think>") + 7
                    end = pensee_complete.find("</think>")
                    pensees = pensee_complete[start:end].strip()
                else:
                    pensees = pensee_complete
                
                # Sauvegarder dans mémoire thermique ET fichier pensées
                self.save_to_thermal_memory(f"PENSÉE AUTONOME: {sujet}", pensees)
                self.sauvegarder_pensee_autonome(sujet, pensees)
                
                print(f"🧠 ACTIVITÉ CÉRÉBRALE AUTONOME: {sujet}")
                print(f"💭 Pensée: {pensees[:200]}...")
                
                return pensees
            else:
                print(f"❌ Erreur génération pensée autonome: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Erreur cerveau autonome: {e}")
            return None
    
    def sauvegarder_pensee_autonome(self, sujet, pensee):
        """Sauvegarde dans fichier pensées autonomes"""
        try:
            if os.path.exists(self.pensees_file):
                with open(self.pensees_file, 'r', encoding='utf-8') as f:
                    pensees_data = json.load(f)
            else:
                pensees_data = {"pensees_autonomes": [], "stats": {"total": 0}}
            
            nouvelle_pensee = {
                "timestamp": datetime.now().isoformat(),
                "sujet": sujet,
                "pensee": pensee,
                "type": "autonome_thermique",
                "user": "Jean-Luc Passave",
                "id": len(pensees_data["pensees_autonomes"]) + 1,
                "integree_memoire_thermique": True
            }
            
            pensees_data["pensees_autonomes"].append(nouvelle_pensee)
            pensees_data["stats"]["total"] = len(pensees_data["pensees_autonomes"])
            
            if len(pensees_data["pensees_autonomes"]) > 500:
                pensees_data["pensees_autonomes"] = pensees_data["pensees_autonomes"][-500:]
            
            with open(self.pensees_file, 'w', encoding='utf-8') as f:
                json.dump(pensees_data, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"❌ Erreur sauvegarde pensée: {e}")
    
    def activite_cerebrale_worker(self):
        """Worker principal - CERVEAU AUTONOME AVEC MÉMOIRE THERMIQUE"""
        print("🧠 CERVEAU AUTONOME JARVIS + MÉMOIRE THERMIQUE DÉMARRÉ")
        
        while self.actif:
            try:
                self.generer_pensee_autonome()
                time.sleep(180)  # 3 minutes
                
            except Exception as e:
                print(f"❌ Erreur worker cerveau: {e}")
                time.sleep(60)
    
    def demarrer_cerveau_autonome(self):
        """Démarre le cerveau autonome intégré"""
        if not self.actif:
            self.actif = True
            self.thread_cerveau = threading.Thread(target=self.activite_cerebrale_worker, daemon=True)
            self.thread_cerveau.start()
            print("🚀 CERVEAU AUTONOME + MÉMOIRE THERMIQUE ACTIVÉ")
            return True
        else:
            print("⚠️ Cerveau autonome déjà actif")
            return False
    
    def arreter_cerveau_autonome(self):
        """Arrête le cerveau autonome"""
        if self.actif:
            self.actif = False
            print("⏹️ CERVEAU AUTONOME ARRÊTÉ")
            return True
        return False
    
    def generate_creative_content(self):
        """GÉNÈRE DU CONTENU CRÉATIF AUTOMATIQUEMENT"""
        try:
            creative_prompts = [
                "Génère une idée créative pour améliorer JARVIS",
                "Propose une nouvelle fonctionnalité innovante",
                "Crée un concept original pour Jean-Luc",
                "Imagine une solution créative à un problème technique"
            ]

            import random
            prompt = random.choice(creative_prompts)

            # Utiliser DeepSeek pour la créativité
            payload = {
                "model": "deepseek-r1",
                "messages": [
                    {
                        "role": "system",
                        "content": """Tu es JARVIS en mode CRÉATIVITÉ pour Jean-Luc Passave.

Tu dois être TRÈS créatif et innovant. Montre tes pensées créatives avec <think>...</think>.

Génère des idées originales et pratiques."""
                    },
                    {
                        "role": "user",
                        "content": f"🎨 MODE CRÉATIVITÉ: {prompt}"
                    }
                ],
                "max_tokens": 300,
                "temperature": 1.0  # Créativité maximale
            }

            response = requests.post(self.deepseek_url, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                creative_response = result['choices'][0]['message']['content']

                # Sauvegarder la création
                creativity_file = "jarvis_creative_outputs.json"

                if os.path.exists(creativity_file):
                    with open(creativity_file, 'r', encoding='utf-8') as f:
                        creations = json.load(f)
                else:
                    creations = {"creative_outputs": []}

                creation_entry = {
                    "timestamp": datetime.now().isoformat(),
                    "prompt": prompt,
                    "output": creative_response,
                    "creation_id": str(uuid.uuid4())
                }

                creations["creative_outputs"].append(creation_entry)

                # Garder seulement les 50 dernières créations
                if len(creations["creative_outputs"]) > 50:
                    creations["creative_outputs"] = creations["creative_outputs"][-50:]

                with open(creativity_file, 'w', encoding='utf-8') as f:
                    json.dump(creations, f, ensure_ascii=False, indent=2)

                print(f"🎨 Création générée: {prompt}")
                return creation_entry
            else:
                print(f"❌ Erreur créativité: {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Erreur génération créative: {e}")
            return None

    def get_stats_complet(self):
        """Stats complètes cerveau + mémoire thermique"""
        try:
            if os.path.exists(self.pensees_file):
                with open(self.pensees_file, 'r', encoding='utf-8') as f:
                    pensees_data = json.load(f)
                pensees_autonomes = len(pensees_data.get("pensees_autonomes", []))
            else:
                pensees_autonomes = 0

            thermal_memory = self.load_thermal_memory()
            neurones_total = len(thermal_memory.get("neuron_memories", []))

            return {
                "cerveau_actif": self.actif,
                "pensees_autonomes_total": pensees_autonomes,
                "neurones_memoire_thermique": neurones_total,
                "integration_complete": True,
                "frequence_pensees_minutes": 3
            }
        except Exception as e:
            print(f"❌ Erreur stats: {e}")
            return {}

# Instance globale intégrée
cerveau_integre = CerveauAutonomeMemoireThermique()

def demarrer_jarvis_cerveau_integre():
    return cerveau_integre.demarrer_cerveau_autonome()

def arreter_jarvis_cerveau_integre():
    return cerveau_integre.arreter_cerveau_autonome()

def get_stats_cerveau_integre():
    return cerveau_integre.get_stats_complet()

if __name__ == "__main__":
    print("🧠 TEST CERVEAU AUTONOME + MÉMOIRE THERMIQUE INTÉGRÉE")
    print("=" * 70)
    
    if demarrer_jarvis_cerveau_integre():
        print("✅ Cerveau autonome + mémoire thermique démarré")
        print("⏳ Test génération pensée autonome...")
        time.sleep(200)
        
        stats = get_stats_cerveau_integre()
        print(f"\n📊 STATS CERVEAU INTÉGRÉ:")
        print(f"   • Cerveau actif: {stats.get('cerveau_actif', False)}")
        print(f"   • Pensées autonomes: {stats.get('pensees_autonomes_total', 0)}")
        print(f"   • Neurones mémoire: {stats.get('neurones_memoire_thermique', 0)}")
        
        arreter_jarvis_cerveau_integre()
    else:
        print("❌ Erreur démarrage")
