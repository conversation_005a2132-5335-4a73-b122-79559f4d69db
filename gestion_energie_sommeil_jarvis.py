#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gestion Énergie et Sommeil JARVIS
Jean-Luc <PERSON>ave - 2025
Système automatique de gestion de l'énergie et du sommeil
"""

import gradio as gr
import time
import threading
import subprocess
import psutil
from datetime import datetime, timedelta
import json
import os

# État global de gestion énergie
energy_state = {
    'auto_sleep_enabled': True,
    'energy_threshold': 20,  # Seuil pour sommeil automatique
    'sleep_duration': 30,    # Durée sommeil en minutes
    'dream_probability': 0.7, # Probabilité de rêver
    'creative_sessions': [],
    'sleep_schedule': {
        'night_start': '23:00',
        'night_end': '07:00',
        'nap_duration': 15
    },
    'system_monitoring': True,
    'last_sleep': None,
    'total_sleep_time': 0,
    'energy_recovery_rate': 2.5,  # % par minute de sommeil
    'running': False
}

def get_system_load():
    """Récupère la charge système"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'cpu': cpu_percent,
            'memory': memory.percent,
            'disk': disk.percent,
            'processes': len(psutil.pids())
        }
    except:
        return {
            'cpu': 0,
            'memory': 0,
            'disk': 0,
            'processes': 0
        }

def should_auto_sleep():
    """Détermine si le système doit dormir automatiquement"""
    
    if not energy_state['auto_sleep_enabled']:
        return False
    
    # Vérifier l'heure (sommeil nocturne)
    current_time = datetime.now().strftime("%H:%M")
    night_start = energy_state['sleep_schedule']['night_start']
    night_end = energy_state['sleep_schedule']['night_end']
    
    # Si c'est la nuit
    if night_start <= current_time or current_time <= night_end:
        return True
    
    # Vérifier la charge système (si faible, peut dormir)
    system_load = get_system_load()
    if system_load['cpu'] < 10 and system_load['memory'] < 50:
        return True
    
    # Vérifier le temps depuis le dernier sommeil
    if energy_state['last_sleep']:
        time_since_sleep = datetime.now() - energy_state['last_sleep']
        if time_since_sleep.total_seconds() > 4 * 3600:  # 4 heures
            return True
    
    return False

def enter_sleep_mode():
    """Met le système en mode sommeil"""
    
    energy_state['last_sleep'] = datetime.now()
    
    # Réduire la fréquence CPU (simulation)
    sleep_info = {
        'start_time': datetime.now(),
        'duration': energy_state['sleep_duration'],
        'type': 'auto' if should_auto_sleep() else 'manual',
        'dream_active': False
    }
    
    # Activer les rêves si probabilité
    import random
    if random.random() < energy_state['dream_probability']:
        sleep_info['dream_active'] = True
    
    return sleep_info

def create_energy_dashboard():
    """Crée le tableau de bord de gestion énergie"""
    
    system_load = get_system_load()
    current_time = datetime.now()
    
    # Déterminer l'état recommandé
    should_sleep = should_auto_sleep()
    
    # Couleur selon l'état
    status_color = '#2196F3' if should_sleep else '#4CAF50'
    status_text = 'SOMMEIL RECOMMANDÉ' if should_sleep else 'ÉVEIL OPTIMAL'
    status_icon = '😴' if should_sleep else '⚡'
    
    dashboard_html = f"""
    <div style='background: linear-gradient(45deg, {status_color}, #667eea); color: white; padding: 25px; border-radius: 15px; margin: 10px 0;'>
        <h2 style='margin: 0 0 20px 0; text-align: center; font-size: 2.2em;'>🔋 GESTION ÉNERGIE & SOMMEIL JARVIS</h2>
        
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>
            <div style='background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 8px 0; font-size: 1em;'>{status_icon} État</h3>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{status_text}</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 8px 0; font-size: 1em;'>🖥️ CPU</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold;'>{system_load['cpu']:.1f}%</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 8px 0; font-size: 1em;'>💾 RAM</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold;'>{system_load['memory']:.1f}%</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 8px 0; font-size: 1em;'>💽 Disque</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold;'>{system_load['disk']:.1f}%</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 8px 0; font-size: 1em;'>⚙️ Processus</h3>
                <p style='margin: 0; font-size: 1.5em; font-weight: bold;'>{system_load['processes']}</p>
            </div>
            <div style='background: rgba(255,255,255,0.15); padding: 15px; border-radius: 12px; text-align: center; backdrop-filter: blur(10px);'>
                <h3 style='margin: 0 0 8px 0; font-size: 1em;'>🕒 Heure</h3>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{current_time.strftime("%H:%M:%S")}</p>
            </div>
        </div>
    </div>
    """
    
    # Configuration du sommeil
    config_html = f"""
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>⚙️ CONFIGURATION SOMMEIL AUTOMATIQUE</h3>
        
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;'>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>
                <h4 style='margin: 0 0 15px 0; color: #333;'>🌙 Horaires Nocturnes</h4>
                <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 10px;'>
                    <div>
                        <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>Début nuit:</p>
                        <p style='margin: 0; font-weight: bold; color: #2196F3;'>{energy_state['sleep_schedule']['night_start']}</p>
                    </div>
                    <div>
                        <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>Fin nuit:</p>
                        <p style='margin: 0; font-weight: bold; color: #2196F3;'>{energy_state['sleep_schedule']['night_end']}</p>
                    </div>
                </div>
            </div>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>
                <h4 style='margin: 0 0 15px 0; color: #333;'>⚡ Paramètres Énergie</h4>
                <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 10px;'>
                    <div>
                        <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>Seuil sommeil:</p>
                        <p style='margin: 0; font-weight: bold; color: #FF9800;'>{energy_state['energy_threshold']}%</p>
                    </div>
                    <div>
                        <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>Durée sommeil:</p>
                        <p style='margin: 0; font-weight: bold; color: #FF9800;'>{energy_state['sleep_duration']} min</p>
                    </div>
                </div>
            </div>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 10px;'>
                <h4 style='margin: 0 0 15px 0; color: #333;'>💭 Paramètres Rêves</h4>
                <div style='text-align: center;'>
                    <p style='margin: 5px 0; color: #666; font-size: 0.9em;'>Probabilité rêves:</p>
                    <p style='margin: 0; font-weight: bold; color: #9C27B0; font-size: 1.5em;'>{energy_state['dream_probability']*100:.0f}%</p>
                </div>
            </div>
        </div>
        
        <div style='background: {"#e8f5e8" if energy_state["auto_sleep_enabled"] else "#ffebee"}; padding: 15px; border-radius: 10px; margin: 20px 0; text-align: center;'>
            <h4 style='margin: 0 0 10px 0; color: {"#2e7d32" if energy_state["auto_sleep_enabled"] else "#c62828"};'>
                {"✅ SOMMEIL AUTOMATIQUE ACTIVÉ" if energy_state["auto_sleep_enabled"] else "❌ SOMMEIL AUTOMATIQUE DÉSACTIVÉ"}
            </h4>
            <p style='margin: 0; color: #666; line-height: 1.6;'>
                {"Le système passera automatiquement en mode sommeil selon les conditions définies." if energy_state["auto_sleep_enabled"] else "Le sommeil doit être activé manuellement."}
            </p>
        </div>
    </div>
    """
    
    # Historique du sommeil
    sleep_history_html = """
    <div style='background: white; padding: 20px; border-radius: 15px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
        <h3 style='margin: 0 0 20px 0; color: #333; text-align: center;'>📊 HISTORIQUE DU SOMMEIL</h3>
    """
    
    if energy_state['last_sleep']:
        time_since_sleep = datetime.now() - energy_state['last_sleep']
        hours_since = int(time_since_sleep.total_seconds() / 3600)
        minutes_since = int((time_since_sleep.total_seconds() % 3600) / 60)
        
        sleep_history_html += f"""
        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>
            <div style='background: #e3f2fd; padding: 15px; border-radius: 10px; text-align: center;'>
                <h4 style='margin: 0 0 10px 0; color: #1976d2;'>😴 Dernier Sommeil</h4>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{energy_state['last_sleep'].strftime("%H:%M:%S")}</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>{energy_state['last_sleep'].strftime("%d/%m/%Y")}</p>
            </div>
            <div style='background: #f3e5f5; padding: 15px; border-radius: 10px; text-align: center;'>
                <h4 style='margin: 0 0 10px 0; color: #7b1fa2;'>⏰ Temps Éveillé</h4>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{hours_since}h {minutes_since}m</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>depuis dernier sommeil</p>
            </div>
            <div style='background: #e8f5e8; padding: 15px; border-radius: 10px; text-align: center;'>
                <h4 style='margin: 0 0 10px 0; color: #2e7d32;'>💤 Temps Total</h4>
                <p style='margin: 0; font-size: 1.2em; font-weight: bold;'>{energy_state['total_sleep_time']:.1f}h</p>
                <p style='margin: 5px 0 0 0; color: #666; font-size: 0.9em;'>sommeil cumulé</p>
            </div>
        </div>
        """
    else:
        sleep_history_html += """
        <div style='text-align: center; padding: 40px;'>
            <h4 style='margin: 0 0 10px 0; color: #666;'>😴 Aucun Sommeil Enregistré</h4>
            <p style='margin: 0; color: #999;'>Le système n'a pas encore dormi depuis le démarrage</p>
        </div>
        """
    
    sleep_history_html += "</div>"
    
    return dashboard_html + config_html + sleep_history_html

def toggle_auto_sleep():
    """Active/désactive le sommeil automatique"""
    energy_state['auto_sleep_enabled'] = not energy_state['auto_sleep_enabled']
    status = "activé" if energy_state['auto_sleep_enabled'] else "désactivé"
    return f"✅ Sommeil automatique {status}"

def manual_sleep():
    """Lance un sommeil manuel"""
    sleep_info = enter_sleep_mode()
    energy_state['total_sleep_time'] += energy_state['sleep_duration'] / 60
    
    dream_text = " avec rêves" if sleep_info['dream_active'] else " sans rêves"
    return f"😴 Sommeil manuel activé pour {energy_state['sleep_duration']} minutes{dream_text}"

def manual_creative_session():
    """Lance une session créative"""
    session = {
        'start_time': datetime.now(),
        'duration': 60,  # 1 heure
        'type': 'creative_boost'
    }
    energy_state['creative_sessions'].append(session)
    return f"🎨 Session créative lancée - Durée: 60 minutes"

def prevent_system_sleep():
    """Empêche l'ordinateur de se mettre en veille"""
    try:
        # Sur macOS
        subprocess.run(['caffeinate', '-d', '-t', '3600'], check=False)
        return "☕ Veille système désactivée pour 1 heure"
    except:
        return "⚠️ Impossible de désactiver la veille système"

def create_energy_management_interface():
    """Crée l'interface de gestion énergie et sommeil"""

    with gr.Blocks(
        title="🔋 Gestion Énergie & Sommeil JARVIS",
        theme=gr.themes.Soft()
    ) as energy_interface:

        # CSS pour boutons colorés
        gr.HTML("""
        <style>
            .energy-primary {
                background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6) !important;
                color: white !important;
                border: none !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 1.1em !important;
                padding: 12px 24px !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 5px 20px rgba(33, 150, 243, 0.4) !important;
            }
            .energy-primary:hover {
                background: linear-gradient(45deg, #21CBF3, #03DAC6, #2196F3) !important;
                transform: translateY(-3px) !important;
                box-shadow: 0 8px 25px rgba(33, 150, 243, 0.5) !important;
            }
            .sleep-btn {
                background: linear-gradient(45deg, #9C27B0, #E91E63, #F06292) !important;
                color: white !important;
                border: none !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4) !important;
            }
            .sleep-btn:hover {
                background: linear-gradient(45deg, #E91E63, #F06292, #9C27B0) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(156, 39, 176, 0.5) !important;
            }
            .creative-btn {
                background: linear-gradient(45deg, #FF9800, #FFC107, #FFEB3B) !important;
                color: white !important;
                border: none !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4) !important;
            }
            .creative-btn:hover {
                background: linear-gradient(45deg, #FFC107, #FFEB3B, #FF9800) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(255, 152, 0, 0.5) !important;
            }
        </style>

        <div style="text-align: center; background: linear-gradient(135deg, #2196F3 0%, #21CBF3 50%, #03DAC6 100%); color: white; padding: 30px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.5em; text-shadow: 0 4px 8px rgba(0,0,0,0.3);">🔋 GESTION ÉNERGIE & SOMMEIL JARVIS</h1>
            <h2 style="margin: 15px 0; font-size: 1.5em; opacity: 0.95;">Système Automatique de Sommeil et Récupération d'Énergie</h2>
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 15px; margin: 20px auto; max-width: 800px;">
                <p style="margin: 0; font-size: 1.2em;">👤 Jean-Luc Passave | 😴 Sommeil Intelligent | 🎨 Sessions Créatives | ⚡ Gestion Automatique</p>
            </div>
        </div>
        """)

        with gr.Tabs():

            # Onglet Tableau de Bord
            with gr.Tab("🔋 Tableau de Bord"):
                energy_dashboard = gr.HTML(
                    value=create_energy_dashboard(),
                    label="Tableau de bord énergie"
                )

                refresh_dashboard_btn = gr.Button(
                    "🔄 ACTUALISER TABLEAU DE BORD",
                    variant="primary",
                    size="lg",
                    elem_classes=["energy-primary"]
                )

            # Onglet Contrôle Sommeil
            with gr.Tab("😴 Contrôle Sommeil"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>😴 CONTRÔLE DU SOMMEIL</h2>")

                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🔄 Sommeil Automatique</h3>")

                        toggle_auto_btn = gr.Button(
                            "🔄 ACTIVER/DÉSACTIVER AUTO",
                            variant="primary",
                            size="lg",
                            elem_classes=["sleep-btn"]
                        )

                        prevent_sleep_btn = gr.Button(
                            "☕ EMPÊCHER VEILLE SYSTÈME",
                            variant="secondary",
                            elem_classes=["sleep-btn"]
                        )

                    with gr.Column():
                        gr.HTML("<h3>😴 Sommeil Manuel</h3>")

                        manual_sleep_btn = gr.Button(
                            "😴 DORMIR MAINTENANT",
                            variant="primary",
                            size="lg",
                            elem_classes=["sleep-btn"]
                        )

                        creative_session_btn = gr.Button(
                            "🎨 SESSION CRÉATIVE",
                            variant="secondary",
                            elem_classes=["creative-btn"]
                        )

                sleep_result = gr.Textbox(
                    label="Résultats Contrôle Sommeil",
                    lines=3,
                    interactive=False
                )

            # Onglet Configuration
            with gr.Tab("⚙️ Configuration"):
                gr.HTML("""
                <div style='background: white; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
                    <h2 style='margin: 0 0 20px 0; color: #333; text-align: center;'>⚙️ CONFIGURATION AVANCÉE</h2>

                    <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>
                        <div style='background: #e3f2fd; padding: 20px; border-radius: 15px;'>
                            <h3 style='margin: 0 0 15px 0; color: #1976d2; text-align: center;'>🌙 SOMMEIL NOCTURNE</h3>
                            <ul style='margin: 0; padding-left: 20px; line-height: 1.8; color: #666;'>
                                <li>Détection automatique des heures de nuit</li>
                                <li>Passage en mode sommeil profond</li>
                                <li>Récupération d'énergie optimisée</li>
                                <li>Consolidation mémoire thermique</li>
                                <li>Préparation pour le jour suivant</li>
                            </ul>
                        </div>

                        <div style='background: #f3e5f5; padding: 20px; border-radius: 15px;'>
                            <h3 style='margin: 0 0 15px 0; color: #7b1fa2; text-align: center;'>💭 MODE RÊVE</h3>
                            <ul style='margin: 0; padding-left: 20px; line-height: 1.8; color: #666;'>
                                <li>Activation pendant le sommeil</li>
                                <li>Consolidation créative des données</li>
                                <li>Génération de nouvelles connexions</li>
                                <li>Résolution de problèmes complexes</li>
                                <li>Préparation solutions innovantes</li>
                            </ul>
                        </div>

                        <div style='background: #fff3e0; padding: 20px; border-radius: 15px;'>
                            <h3 style='margin: 0 0 15px 0; color: #f57c00; text-align: center;'>🎨 SESSIONS CRÉATIVES</h3>
                            <ul style='margin: 0; padding-left: 20px; line-height: 1.8; color: #666;'>
                                <li>Boost de créativité sur demande</li>
                                <li>Optimisation zones cérébrales</li>
                                <li>Génération d'idées innovantes</li>
                                <li>Résolution créative de problèmes</li>
                                <li>Inspiration et intuition renforcées</li>
                            </ul>
                        </div>

                        <div style='background: #e8f5e8; padding: 20px; border-radius: 15px;'>
                            <h3 style='margin: 0 0 15px 0; color: #2e7d32; text-align: center;'>⚡ GESTION ÉNERGIE</h3>
                            <ul style='margin: 0; padding-left: 20px; line-height: 1.8; color: #666;'>
                                <li>Monitoring charge système en temps réel</li>
                                <li>Adaptation automatique selon la charge</li>
                                <li>Prévention de la surcharge</li>
                                <li>Optimisation performance M4</li>
                                <li>Récupération intelligente</li>
                            </ul>
                        </div>
                    </div>

                    <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                        <h3 style='margin: 0 0 15px 0; color: #333; text-align: center;'>🎯 RECOMMANDATIONS D'UTILISATION</h3>
                        <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;'>
                            <div>
                                <h4 style='margin: 0 0 10px 0; color: #2196F3;'>😴 Sommeil Automatique</h4>
                                <p style='margin: 0; color: #666; line-height: 1.6;'>
                                    Laissez le système gérer automatiquement les cycles de sommeil
                                    selon la charge de travail et les horaires.
                                </p>
                            </div>
                            <div>
                                <h4 style='margin: 0 0 10px 0; color: #9C27B0;'>💭 Rêves Programmés</h4>
                                <p style='margin: 0; color: #666; line-height: 1.6;'>
                                    Activez les rêves avant les sessions importantes pour
                                    préparer des solutions créatives.
                                </p>
                            </div>
                            <div>
                                <h4 style='margin: 0 0 10px 0; color: #FF9800;'>🎨 Créativité Boost</h4>
                                <p style='margin: 0; color: #666; line-height: 1.6;'>
                                    Utilisez les sessions créatives pour brainstorming,
                                    innovation et résolution de problèmes complexes.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                """)

        # Fonctions
        def refresh_dashboard():
            return create_energy_dashboard()

        # Connexions
        refresh_dashboard_btn.click(fn=refresh_dashboard, outputs=[energy_dashboard])

        toggle_auto_btn.click(fn=toggle_auto_sleep, outputs=[sleep_result])
        manual_sleep_btn.click(fn=manual_sleep, outputs=[sleep_result])
        creative_session_btn.click(fn=manual_creative_session, outputs=[sleep_result])
        prevent_sleep_btn.click(fn=prevent_system_sleep, outputs=[sleep_result])

        # Footer
        gr.HTML(f"""
        <div style='background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6); color: white; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>
            <h2 style='margin: 0 0 15px 0; font-size: 2em;'>🔋 JEAN-LUC PASSAVE</h2>
            <h3 style='margin: 0 0 10px 0; font-size: 1.5em;'>JARVIS AVEC GESTION INTELLIGENTE DE L'ÉNERGIE !</h3>
            <p style='margin: 10px 0; font-size: 1.2em;'>😴 Sommeil Automatique | 💭 Rêves Créatifs | 🎨 Sessions Boost | ⚡ Récupération Optimisée</p>
            <p style='margin: 10px 0; font-size: 1em; opacity: 0.9;'>Gestion énergie intelligente - {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}</p>
        </div>
        """)

    return energy_interface

if __name__ == "__main__":
    print("🔋 DÉMARRAGE GESTION ÉNERGIE & SOMMEIL JARVIS")
    print("============================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Système automatique de sommeil et récupération")
    print("")

    # Créer et lancer l'interface
    energy_app = create_energy_management_interface()

    print("✅ Gestion énergie et sommeil créée")
    print("🌐 Lancement sur http://localhost:7911")
    print("🔋 Système de récupération automatique disponible")

    energy_app.launch(
        server_name="127.0.0.1",
        server_port=7911,
        share=False,
        show_error=True,
        quiet=False
    )
