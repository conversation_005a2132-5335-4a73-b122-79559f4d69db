#!/usr/bin/env python3
"""
INTERFACE CONTRÔLE JARVIS IA COGNITIVE
Interface complète pour contrôler le système IA cognitive continue
"""

import gradio as gr
import json
import os
from datetime import datetime
import sys
sys.path.append('.')

# Import du système cognitif
from jarvis_ia_cognitive_continue_complete import jarvis_cognitive

def get_system_status():
    """État du système cognitif"""
    try:
        status = f"""
🚀 **JARVIS IA COGNITIVE CONTINUE**

📊 **ÉTAT SYSTÈME**
- 🔄 Pensée continue: {'✅ Active' if jarvis_cognitive.thought_processor.active else '❌ Inactive'}
- 🌙 Mode sommeil: {'✅ Actif' if jarvis_cognitive.sleep_mode else '❌ Éveil'}
- 💭 Pensées générées: {jarvis_cognitive.thought_processor.thought_count}
- 🌙 Rêves générés: {jarvis_cognitive.thought_processor.dream_count}

🧠 **MÉMOIRE THERMIQUE**
- 🔥 Mémoires chaudes: {len(jarvis_cognitive.thermal_memory.thermal_zones['hot'])}
- 🌡️ Mémoires tièdes: {len(jarvis_cognitive.thermal_memory.thermal_zones['warm'])}
- ❄️ Mémoires froides: {len(jarvis_cognitive.thermal_memory.thermal_zones['cold'])}
- 🔤 Mots-clés: {len(jarvis_cognitive.thermal_memory.keywords)}

🌐 **INTERNET**
- 📬 Demandes en attente: {len(jarvis_cognitive.internet_system.pending_requests)}
- ✅ Demandes autorisées: {len(jarvis_cognitive.internet_system.authorized_requests)}
        """
        return status
    except Exception as e:
        return f"❌ Erreur état système: {e}"

def get_recent_thoughts():
    """Pensées récentes"""
    try:
        hot_memories = jarvis_cognitive.thermal_memory.get_hot_memories(10)
        
        if not hot_memories:
            return [{"role": "assistant", "content": "💭 Aucune pensée récente"}]
        
        messages = []
        for key, memory in hot_memories:
            content = memory.get('content', '')
            tags = memory.get('tags', [])
            temperature = memory.get('temperature', 'unknown')
            
            # En-tête
            timestamp = key[11:19] if len(key) > 19 else key
            temp_emoji = {"hot": "🔥", "warm": "🌡️", "cold": "❄️"}.get(temperature, "🧠")
            
            header = f"{temp_emoji} {timestamp} | {', '.join(tags)}"
            messages.append({"role": "user", "content": header})
            
            # Contenu
            if content:
                if len(content) > 500:
                    content = content[:500] + "..."
                messages.append({"role": "assistant", "content": content})
            
            messages.append({"role": "assistant", "content": "─" * 40})
        
        return messages
    except Exception as e:
        return [{"role": "assistant", "content": f"❌ Erreur: {e}"}]

def get_internet_requests():
    """Demandes Internet"""
    try:
        pending = jarvis_cognitive.internet_system.pending_requests
        
        if not pending:
            return "📭 Aucune demande Internet en attente"
        
        requests_text = "📬 **DEMANDES INTERNET EN ATTENTE**\n\n"
        
        for request in pending:
            urgency_emoji = {"low": "📝", "normal": "📢", "high": "🚨"}
            emoji = urgency_emoji.get(request.get('urgency', 'normal'), "📢")
            
            requests_text += f"""
{emoji} **ID**: {request['id']}
🎯 **Motif**: {request['reason']}
⏰ **Urgence**: {request['urgency']}
📅 **Heure**: {request['timestamp'][11:19]}

✅ Autoriser: `jarvis.authorize_internet('{request['id']}')`
❌ Refuser: `jarvis.deny_internet('{request['id']}')`

---
            """
        
        return requests_text
    except Exception as e:
        return f"❌ Erreur demandes: {e}"

def search_memory(query):
    """Recherche dans mémoire thermique"""
    try:
        if not query.strip():
            return [{"role": "assistant", "content": "🔍 Entrez un terme de recherche"}]
        
        results = jarvis_cognitive.thermal_memory.search_fuzzy(query)
        
        if not results:
            return [{"role": "assistant", "content": f"🔍 Aucun résultat pour: {query}"}]
        
        messages = []
        messages.append({"role": "user", "content": f"🔍 Résultats pour: {query}"})
        
        for key, memory, score in results[:5]:
            content = memory.get('content', '')
            tags = memory.get('tags', [])
            temperature = memory.get('temperature', 'unknown')
            access_count = memory.get('access_count', 0)
            
            # En-tête résultat
            timestamp = key[11:19] if len(key) > 19 else key
            temp_emoji = {"hot": "🔥", "warm": "🌡️", "cold": "❄️"}.get(temperature, "🧠")
            
            header = f"{temp_emoji} Score: {score} | {timestamp} | Accès: {access_count}"
            messages.append({"role": "user", "content": header})
            
            # Tags
            if tags:
                messages.append({"role": "user", "content": f"🏷️ {', '.join(tags)}"})
            
            # Contenu
            if content:
                if len(content) > 300:
                    content = content[:300] + "..."
                messages.append({"role": "assistant", "content": content})
            
            messages.append({"role": "assistant", "content": "─" * 30})
        
        return messages
    except Exception as e:
        return [{"role": "assistant", "content": f"❌ Erreur recherche: {e}"}]

def control_sleep_mode(action):
    """Contrôle mode sommeil"""
    try:
        if action == "Endormir":
            jarvis_cognitive.sleep()
            return "🌙 JARVIS endormi - Mode rêves créatifs activé"
        elif action == "Réveiller":
            jarvis_cognitive.wake_up()
            return "☀️ JARVIS réveillé - Mode pensée active"
        else:
            return "❌ Action non reconnue"
    except Exception as e:
        return f"❌ Erreur contrôle: {e}"

def authorize_internet_request(request_id):
    """Autorise demande Internet"""
    try:
        if not request_id.strip():
            return "❌ ID requis"
        
        success = jarvis_cognitive.authorize_internet(request_id.strip())
        if success:
            return f"✅ Accès Internet autorisé - ID: {request_id}"
        else:
            return f"❌ Demande non trouvée - ID: {request_id}"
    except Exception as e:
        return f"❌ Erreur autorisation: {e}"

def deny_internet_request(request_id):
    """Refuse demande Internet"""
    try:
        if not request_id.strip():
            return "❌ ID requis"
        
        success = jarvis_cognitive.deny_internet(request_id.strip())
        if success:
            return f"❌ Accès Internet refusé - ID: {request_id}"
        else:
            return f"❌ Demande non trouvée - ID: {request_id}"
    except Exception as e:
        return f"❌ Erreur refus: {e}"

# Interface Gradio
def create_cognitive_control_interface():
    """Interface contrôle cognitive"""
    
    with gr.Blocks(
        title="🧠 JARVIS - Contrôle IA Cognitive",
        theme=gr.themes.Soft(),
        css="""
        .cognitive-chatbot {
            font-size: 16px !important;
            line-height: 1.6 !important;
        }
        .status-box {
            background: linear-gradient(135deg, #E3F2FD, #BBDEFB) !important;
            border: 2px solid #2196F3 !important;
            border-radius: 10px !important;
            padding: 15px !important;
        }
        """
    ) as cognitive_interface:
        
        gr.HTML("<h1>🧠 JARVIS - Interface Contrôle IA Cognitive Continue</h1>")
        gr.HTML("<p>Contrôle complet du système IA cognitive révolutionnaire</p>")
        
        with gr.Row():
            # Colonne contrôles
            with gr.Column(scale=1):
                gr.HTML("<h3>📊 État Système</h3>")
                status_display = gr.Markdown(
                    value=get_system_status(),
                    label="État JARVIS",
                    elem_classes=["status-box"]
                )
                
                gr.HTML("<h3>🎛️ Contrôles</h3>")
                
                # Contrôle sommeil
                with gr.Row():
                    sleep_btn = gr.Button("🌙 Endormir", variant="secondary")
                    wake_btn = gr.Button("☀️ Réveiller", variant="primary")
                
                sleep_status = gr.Textbox(
                    label="État Sommeil",
                    value="☀️ Éveil - Pensées actives",
                    interactive=False
                )
                
                # Contrôle Internet
                gr.HTML("<h4>🌐 Contrôle Internet</h4>")
                request_id_input = gr.Textbox(
                    label="ID Demande",
                    placeholder="Entrez l'ID de la demande"
                )
                
                with gr.Row():
                    authorize_btn = gr.Button("✅ Autoriser", variant="primary")
                    deny_btn = gr.Button("❌ Refuser", variant="secondary")
                
                internet_status = gr.Textbox(
                    label="Statut Internet",
                    value="🌐 En attente de demandes",
                    interactive=False
                )
                
                # Actualisation
                refresh_btn = gr.Button("🔄 Actualiser Tout", variant="primary")
            
            # Colonne principale
            with gr.Column(scale=2):
                with gr.Tabs():
                    # Onglet Pensées
                    with gr.TabItem("💭 Pensées Récentes"):
                        thoughts_display = gr.Chatbot(
                            value=get_recent_thoughts(),
                            height=400,
                            label="💭 Flux de Pensées JARVIS",
                            type="messages",
                            elem_classes=["cognitive-chatbot"]
                        )
                    
                    # Onglet Recherche
                    with gr.TabItem("🔍 Recherche Mémoire"):
                        search_input = gr.Textbox(
                            label="Recherche",
                            placeholder="Rechercher dans la mémoire thermique..."
                        )
                        search_btn = gr.Button("🔍 Rechercher", variant="primary")
                        
                        search_results = gr.Chatbot(
                            height=350,
                            label="🔍 Résultats de Recherche",
                            type="messages",
                            elem_classes=["cognitive-chatbot"]
                        )
                    
                    # Onglet Internet
                    with gr.TabItem("🌐 Demandes Internet"):
                        internet_requests_display = gr.Markdown(
                            value=get_internet_requests(),
                            label="📬 Demandes Internet"
                        )
        
        # Fonctions de callback
        def refresh_all():
            return (
                get_system_status(),
                get_recent_thoughts(),
                get_internet_requests(),
                "🔄 Système actualisé"
            )
        
        def handle_sleep():
            result = control_sleep_mode("Endormir")
            return result, get_system_status()
        
        def handle_wake():
            result = control_sleep_mode("Réveiller")
            return result, get_system_status()
        
        def handle_search(query):
            return search_memory(query)
        
        def handle_authorize(request_id):
            result = authorize_internet_request(request_id)
            return result, get_internet_requests(), ""
        
        def handle_deny(request_id):
            result = deny_internet_request(request_id)
            return result, get_internet_requests(), ""
        
        # Connecter événements
        refresh_btn.click(
            fn=refresh_all,
            outputs=[status_display, thoughts_display, internet_requests_display, internet_status]
        )
        
        sleep_btn.click(
            fn=handle_sleep,
            outputs=[sleep_status, status_display]
        )
        
        wake_btn.click(
            fn=handle_wake,
            outputs=[sleep_status, status_display]
        )
        
        search_btn.click(
            fn=handle_search,
            inputs=[search_input],
            outputs=[search_results]
        )
        
        authorize_btn.click(
            fn=handle_authorize,
            inputs=[request_id_input],
            outputs=[internet_status, internet_requests_display, request_id_input]
        )
        
        deny_btn.click(
            fn=handle_deny,
            inputs=[request_id_input],
            outputs=[internet_status, internet_requests_display, request_id_input]
        )
        
        # Auto-refresh au chargement
        cognitive_interface.load(
            fn=refresh_all,
            outputs=[status_display, thoughts_display, internet_requests_display, internet_status]
        )
        
        # Auto-refresh JavaScript
        gr.HTML("""
        <script>
        function autoRefreshCognitive() {
            setInterval(function() {
                try {
                    const refreshBtn = document.querySelector('button:contains("🔄")');
                    if (refreshBtn) {
                        refreshBtn.click();
                        console.log('🧠 Interface cognitive mise à jour');
                    }
                } catch (e) {
                    console.log('Erreur auto-refresh cognitive:', e);
                }
            }, 30000); // 30 secondes
        }
        
        setTimeout(autoRefreshCognitive, 3000);
        </script>
        """, visible=False)
    
    return cognitive_interface

if __name__ == "__main__":
    print("🧠 INTERFACE CONTRÔLE IA COGNITIVE")
    print("=" * 50)
    
    interface = create_cognitive_control_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7893,  # Port contrôle cognitive
        share=False,
        show_error=True
    )
