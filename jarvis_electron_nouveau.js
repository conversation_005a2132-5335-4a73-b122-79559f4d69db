const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const os = require('os');

let mainWindow;
let jarvisProcess = null;

// Informations système
const systemInfo = {
    arch: os.arch(),
    platform: os.platform(),
    cpus: os.cpus().length,
    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024)
};

console.log('🍎 NOUVEAU JARVIS ELECTRON');
console.log('=====================================');
console.log('🔧 Architecture:', systemInfo.arch);
console.log('🍎 Apple Silicon:', systemInfo.arch === 'arm64' ? 'OUI' : 'NON');
console.log('💾 RAM Totale:', systemInfo.totalMemory, 'GB');
console.log('⚡ Cœurs CPU:', systemInfo.cpus);

function createWindow() {
    console.log('🖥️ CRÉATION FENÊTRE PRINCIPALE...');
    
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false
        },
        icon: path.join(__dirname, 'assets', 'jarvis-icon.png'),
        titleBarStyle: 'hiddenInset',
        show: false
    });

    // HTML complet dans le fichier
    const htmlContent = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 JARVIS M4 FINAL COMPLET</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }
        
        .header {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            text-align: center;
            border-bottom: 2px solid rgba(255,255,255,0.2);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 250px;
            background: rgba(0,0,0,0.4);
            padding: 20px;
            border-right: 2px solid rgba(255,255,255,0.2);
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #4CAF50;
        }
        
        .status-item {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            font-size: 12px;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .interface-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .interface-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .interface-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
            background: linear-gradient(45deg, #45a049, #4CAF50);
        }
        
        .interface-btn:active {
            transform: translateY(0);
        }
        
        .chat-section {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .chat-input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .send-btn {
            background: #FF5722;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .send-btn:hover {
            background: #E64A19;
        }
        
        .iframe-container {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .back-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .interface-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 JARVIS M4 FINAL COMPLET</h1>
        <p>Jean-Luc Passave - Interface Complète avec Micro Natif</p>
        <div style="margin-top: 10px;">
            <span style="background: #4CAF50; padding: 4px 8px; border-radius: 3px; margin: 0 5px;">🍎 Apple Silicon M4</span>
            <span style="background: #2196F3; padding: 4px 8px; border-radius: 3px; margin: 0 5px;">🎤 Micro Natif</span>
            <span style="background: #FF9800; padding: 4px 8px; border-radius: 3px; margin: 0 5px;">📹 Webcam</span>
            <span style="background: #9C27B0; padding: 4px 8px; border-radius: 3px; margin: 0 5px;">🧠 IA Complète</span>
        </div>
    </div>
    
    <div class="main-container">
        <div class="sidebar">
            <h3>🔧 Statut JARVIS</h3>
            <div class="status-item">
                <div>🤖 Agent Principal</div>
                <div id="agent-status">😴 Connexion...</div>
            </div>
            
            <h3>🍎 Apple M4</h3>
            <div class="status-item">
                <div>⚡ P-cores: 6 actifs</div>
                <div>🔋 E-cores: 4 actifs</div>
                <div>🧠 Neural Engine: ✅</div>
                <div>💾 RAM: ${systemInfo.totalMemory} GB</div>
                <div id="qi-display">🔧 QI: <span id="qi-value">120</span></div>
                <div id="neuron-display">🧠 Neurones: <span id="neuron-count">1,000,000</span></div>
            </div>
            
            <h3>🎤 Audio Status</h3>
            <div class="status-item">
                <div id="audio-status">😴 Initialisation...</div>
            </div>
            
            <h3>📹 Webcam Status</h3>
            <div class="status-item">
                <div id="webcam-status">😴 Initialisation...</div>
            </div>
        </div>
        
        <div class="main-content" id="main-content">
            <h2>🌐 Interfaces JARVIS</h2>
            
            <div class="interface-grid">
                <button class="interface-btn" id="jarvis-btn">
                    🏠<br>JARVIS
                </button>
                <button class="interface-btn" id="v2pro-btn">
                    🚀<br>V2 PRO
                </button>
                <button class="interface-btn" id="docs-btn">
                    📚<br>API Docs
                </button>
                <button class="interface-btn" id="diagnostic-btn">
                    🔧<br>Diagnostic
                </button>
                <button class="interface-btn" id="dashboard-btn">
                    🏠<br>Dashboard
                </button>
                <button class="interface-btn" id="chat-btn">
                    💬<br>Chat
                </button>
                <button class="interface-btn" id="code-btn">
                    💻<br>Code
                </button>
                <button class="interface-btn" id="pensees-btn">
                    🧠<br>Pensées
                </button>
                <button class="interface-btn" id="memory-cleanup-btn" style="background: linear-gradient(45deg, #f44336, #d32f2f);">
                    🚨<br>Nettoyage RAM
                </button>
            </div>
            
            <div class="chat-section">
                <h3>💬 CHAT JARVIS AVEC MICRO NATIF</h3>
                <input type="text" class="chat-input" id="chat-input" placeholder="Tapez votre message à JARVIS...">
                <button class="send-btn" id="send-btn">🚀 Envoyer</button>
                <p style="margin-top: 10px; font-size: 12px; opacity: 0.7;">
                    🎤 Cliquez sur le micro pour parler à JARVIS
                </p>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 INITIALISATION JARVIS ELECTRON...');
        
        // Variables globales
        let originalContent = null;

        // SYSTÈME DE QI THERMIQUE ÉVOLUTIF
        let thermalMemory = {
            volume: 0,
            relevance: 0,
            accessSpeed: 100,
            generativity: 0,
            interactions: 0,
            learningRate: 0,
            creativity: 0,
            lastUpdate: Date.now()
        };

        // Formule QI Thermique: QI_total = QI_initial + (α × Volume_Mémoire) + (β × Pertinence) + (γ × Vitesse_Accès) + (δ × Générativité)
        const QI_CONFIG = {
            initial: 120,
            alpha: 0.1,    // Coefficient volume mémoire
            beta: 0.15,    // Coefficient pertinence
            gamma: 0.05,   // Coefficient vitesse d'accès
            delta: 0.2,    // Coefficient générativité
            epsilon: 0.1   // Coefficient créativité
        };

        function calculateThermalIQ() {
            const qi = QI_CONFIG.initial +
                      (QI_CONFIG.alpha * thermalMemory.volume) +
                      (QI_CONFIG.beta * thermalMemory.relevance) +
                      (QI_CONFIG.gamma * thermalMemory.accessSpeed) +
                      (QI_CONFIG.delta * thermalMemory.generativity) +
                      (QI_CONFIG.epsilon * thermalMemory.creativity);

            return Math.round(qi * 100) / 100; // Arrondi à 2 décimales
        }

        function calculateNeuronCount() {
            // Base de 1M de neurones + croissance basée sur l'apprentissage
            const baseNeurons = 1000000;
            const growthFactor = thermalMemory.interactions * 1000 + thermalMemory.volume * 500;
            return Math.floor(baseNeurons + growthFactor);
        }

        function updateThermalMemory(interaction) {
            const now = Date.now();
            const timeDelta = (now - thermalMemory.lastUpdate) / 1000; // en secondes

            // Mise à jour basée sur l'interaction
            thermalMemory.interactions++;
            thermalMemory.volume += 0.1;
            thermalMemory.relevance += Math.random() * 0.5; // Simulation pertinence
            thermalMemory.generativity += 0.2;
            thermalMemory.creativity += Math.random() * 0.3;
            thermalMemory.learningRate = thermalMemory.interactions / (timeDelta / 60); // interactions par minute

            // Dégradation thermique (refroidissement)
            const coolingFactor = timeDelta / 3600; // refroidissement par heure
            thermalMemory.relevance = Math.max(0, thermalMemory.relevance - coolingFactor * 0.1);
            thermalMemory.accessSpeed = Math.max(50, thermalMemory.accessSpeed - coolingFactor * 0.05);

            thermalMemory.lastUpdate = now;

            // Mise à jour de l'affichage
            updateQIDisplay();
        }

        function updateQIDisplay() {
            const currentQI = calculateThermalIQ();
            const neuronCount = calculateNeuronCount();

            const qiElement = document.getElementById('qi-value');
            const neuronElement = document.getElementById('neuron-count');

            if (qiElement) {
                qiElement.textContent = currentQI;
                // Couleur basée sur le QI
                if (currentQI > 150) {
                    qiElement.style.color = '#4CAF50'; // Vert
                } else if (currentQI > 130) {
                    qiElement.style.color = '#FF9800'; // Orange
                } else {
                    qiElement.style.color = '#2196F3'; // Bleu
                }
            }

            if (neuronElement) {
                neuronElement.textContent = neuronCount.toLocaleString();
            }

            console.log('🧠 QI Thermique mis à jour:', currentQI, '| Neurones:', neuronCount.toLocaleString());
        }
        
        // Fonction pour charger une interface dans la fenêtre
        function loadInterface(url, title) {
            console.log('🌐 Chargement:', url, '- Titre:', title);

            // Mise à jour mémoire thermique
            updateThermalMemory('interface_load');

            const mainContent = document.getElementById('main-content');
            if (!originalContent) {
                originalContent = mainContent.innerHTML;
            }
            
            // Vérifier si l'URL est accessible
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Interface accessible:', url);
                        
                        // Créer le contenu iframe
                        mainContent.innerHTML = \`
                            <div class="iframe-container">
                                <button class="back-btn" onclick="returnHome()">🏠 Retour Dashboard</button>
                                <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 8px 12px; border-radius: 5px; font-size: 14px; z-index: 1000;">
                                    📱 \${title}
                                </div>
                                <iframe class="interface-iframe" src="\${url}"></iframe>
                            </div>
                        \`;
                        
                        console.log('✅ Interface chargée:', title);
                    } else {
                        alert('❌ Interface "' + title + '" non accessible\\n\\nURL: ' + url + '\\n\\nVérifiez que le service JARVIS est démarré.');
                    }
                })
                .catch(error => {
                    console.error('❌ Erreur:', error);
                    alert('❌ Impossible de se connecter à "' + title + '"\\n\\nURL: ' + url + '\\n\\nErreur: ' + error.message);
                });
        }
        
        // Fonction pour retourner au dashboard
        function returnHome() {
            console.log('🏠 Retour au dashboard');
            const mainContent = document.getElementById('main-content');
            if (originalContent) {
                mainContent.innerHTML = originalContent;
                initializeButtons();
            }
        }
        
        // Fonction pour initialiser tous les boutons
        function initializeButtons() {
            console.log('🔧 Initialisation des boutons...');
            
            // Boutons d'interface
            document.getElementById('jarvis-btn').onclick = () => loadInterface('http://localhost:7863', 'JARVIS Chat');
            document.getElementById('v2pro-btn').onclick = () => loadInterface('http://localhost:8000/dashboard', 'V2 PRO Dashboard');
            document.getElementById('docs-btn').onclick = () => loadInterface('http://localhost:8000/docs', 'API Documentation');
            document.getElementById('diagnostic-btn').onclick = () => loadInterface('http://localhost:8000/diagnostic', 'Diagnostic');
            document.getElementById('dashboard-btn').onclick = () => loadInterface('http://localhost:7867', 'Dashboard Principal');
            document.getElementById('chat-btn').onclick = () => loadInterface('http://localhost:7866', 'Communication');
            document.getElementById('code-btn').onclick = () => loadInterface('http://localhost:7868', 'Éditeur Code');
            document.getElementById('pensees-btn').onclick = () => loadInterface('http://localhost:7869', 'Pensées JARVIS');
            document.getElementById('memory-cleanup-btn').onclick = () => {
                console.log('🚨 Nettoyage mémoire demandé');
                emergencyMemoryCleanup();
                alert('🚨 Nettoyage mémoire effectué !\\n\\nLa mémoire thermique a été optimisée.');
            };

            // Bouton d'envoi de message
            document.getElementById('send-btn').onclick = sendMessage;
            document.getElementById('chat-input').onkeypress = (e) => {
                if (e.key === 'Enter') sendMessage();
            };
            
            console.log('✅ Boutons initialisés');
        }
        
        // Fonction d'envoi de message
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            if (message) {
                console.log('💬 Message envoyé:', message);

                // Mise à jour mémoire thermique pour interaction
                updateThermalMemory('chat_message');

                // Afficher le message dans l'interface
                showMessage('Vous', message, 'user');

                // Envoyer à JARVIS via API
                fetch('http://localhost:8000/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'deepseek-r1:8b',
                        messages: [
                            {
                                role: 'user',
                                content: message
                            }
                        ],
                        temperature: 0.7,
                        max_tokens: 1000
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.choices && data.choices[0]) {
                        const reply = data.choices[0].message.content;
                        showMessage('JARVIS', reply, 'assistant');
                    }
                })
                .catch(error => {
                    console.error('❌ Erreur JARVIS:', error);
                    showMessage('JARVIS', '❌ Erreur de connexion avec JARVIS', 'error');
                });

                input.value = '';
            }
        }

        // Fonction pour afficher les messages
        function showMessage(sender, message, type) {
            const chatSection = document.querySelector('.chat-section');
            if (!document.getElementById('chat-messages')) {
                const messagesDiv = document.createElement('div');
                messagesDiv.id = 'chat-messages';
                messagesDiv.style.cssText = \`
                    max-height: 200px;
                    overflow-y: auto;
                    margin: 10px 0;
                    padding: 10px;
                    background: rgba(255,255,255,0.1);
                    border-radius: 5px;
                \`;
                chatSection.insertBefore(messagesDiv, chatSection.lastElementChild.previousElementSibling);
            }

            const messagesDiv = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = \`
                margin: 5px 0;
                padding: 8px;
                border-radius: 5px;
                background: \${type === 'user' ? 'rgba(76, 175, 80, 0.3)' : type === 'error' ? 'rgba(244, 67, 54, 0.3)' : 'rgba(33, 150, 243, 0.3)'};
            \`;
            messageDiv.innerHTML = \`<strong>\${sender}:</strong> \${message}\`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        // Fonction de vérification du statut des services
        function checkServicesStatus() {
            console.log('🔍 Vérification statut des services...');

            const services = [
                { name: 'JARVIS Chat', url: 'http://localhost:7863', element: 'agent-status' },
                { name: 'V2 PRO', url: 'http://localhost:8000', element: null },
                { name: 'Dashboard', url: 'http://localhost:7867', element: null },
                { name: 'Communication', url: 'http://localhost:7866', element: null }
            ];

            services.forEach(service => {
                fetch(service.url, { method: 'HEAD', mode: 'no-cors' })
                    .then(() => {
                        console.log('✅', service.name, 'disponible');
                        if (service.element) {
                            const element = document.getElementById(service.element);
                            if (element) {
                                element.innerHTML = '✅ Connecté';
                                element.style.color = '#4CAF50';
                            }
                        }
                    })
                    .catch(() => {
                        console.log('❌', service.name, 'indisponible');
                        if (service.element) {
                            const element = document.getElementById(service.element);
                            if (element) {
                                element.innerHTML = '❌ Déconnecté';
                                element.style.color = '#f44336';
                            }
                        }
                    });
            });
        }

        // Vérification périodique du statut OPTIMISÉE
        function startStatusMonitoring() {
            checkServicesStatus();

            // Arrêter le timer existant s'il y en a un
            if (statusTimer) {
                clearInterval(statusTimer);
            }

            statusTimer = setInterval(() => {
                try {
                    checkServicesStatus();
                } catch (error) {
                    console.error('❌ Erreur monitoring:', error);
                }
            }, 60000); // Réduit à toutes les 60 secondes pour économiser la mémoire
        }

        // Variables pour contrôler les timers
        let thermalIQTimer = null;
        let statusTimer = null;

        // Mise à jour automatique du QI thermique OPTIMISÉE
        function startThermalIQMonitoring() {
            updateQIDisplay(); // Mise à jour initiale

            // Arrêter le timer existant s'il y en a un
            if (thermalIQTimer) {
                clearInterval(thermalIQTimer);
            }

            thermalIQTimer = setInterval(() => {
                try {
                    // Simulation d'activité cognitive de fond LIMITÉE
                    thermalMemory.creativity = Math.min(100, thermalMemory.creativity + Math.random() * 0.05);
                    thermalMemory.volume = Math.min(1000, thermalMemory.volume + 0.005);

                    // Nettoyage mémoire périodique
                    if (thermalMemory.interactions % 100 === 0) {
                        // Réinitialisation partielle pour éviter l'accumulation
                        thermalMemory.relevance = Math.max(0, thermalMemory.relevance * 0.9);
                        thermalMemory.creativity = Math.max(0, thermalMemory.creativity * 0.95);
                    }

                    updateQIDisplay();
                } catch (error) {
                    console.error('❌ Erreur QI thermique:', error);
                }
            }, 10000); // Réduit à toutes les 10 secondes pour économiser la mémoire
        }

        // Fonction de nettoyage mémoire d'urgence
        function emergencyMemoryCleanup() {
            console.log('🚨 NETTOYAGE MÉMOIRE D\'URGENCE');

            // Arrêter tous les timers
            if (thermalIQTimer) {
                clearInterval(thermalIQTimer);
                thermalIQTimer = null;
            }
            if (statusTimer) {
                clearInterval(statusTimer);
                statusTimer = null;
            }

            // Réinitialiser la mémoire thermique
            thermalMemory = {
                volume: Math.min(100, thermalMemory.volume),
                relevance: Math.min(50, thermalMemory.relevance),
                accessSpeed: 100,
                generativity: Math.min(50, thermalMemory.generativity),
                interactions: Math.min(1000, thermalMemory.interactions),
                learningRate: 0,
                creativity: Math.min(50, thermalMemory.creativity),
                lastUpdate: Date.now()
            };

            // Forcer le garbage collection si disponible
            if (window.gc) {
                window.gc();
            }

            // Redémarrer les timers avec des intervalles plus longs
            startThermalIQMonitoring();
            startStatusMonitoring();

            console.log('✅ Nettoyage mémoire terminé');
        }

        // Monitoring automatique de la mémoire système
        function startMemoryMonitoring() {
            setInterval(() => {
                // Vérifier l'utilisation mémoire (approximative)
                const memoryInfo = performance.memory;
                if (memoryInfo) {
                    const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
                    const totalMB = Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024);
                    const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024);

                    console.log('💾 Mémoire JS:', usedMB + 'MB /' + totalMB + 'MB (Limite:' + limitMB + 'MB)');

                    // Nettoyage automatique si mémoire > 80%
                    if (usedMB > limitMB * 0.8) {
                        console.log('🚨 ALERTE MÉMOIRE: Nettoyage automatique déclenché');
                        emergencyMemoryCleanup();
                    }
                }
            }, 30000); // Vérification toutes les 30 secondes
        }

        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', () => {
            console.log('✅ DOM chargé');
            initializeButtons();
            startStatusMonitoring();
            startThermalIQMonitoring();
            startMemoryMonitoring();
        });

        // Initialisation immédiate
        initializeButtons();
        startStatusMonitoring();
        startThermalIQMonitoring();
        startMemoryMonitoring();

        console.log('✅ JARVIS ELECTRON INITIALISÉ');
    </script>
</body>
</html>
    `;

    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent));

    mainWindow.once('ready-to-show', () => {
        console.log('✅ Fenêtre prête à afficher');
        mainWindow.show();
        console.log('✅ NOUVEAU JARVIS ELECTRON LANCÉ');
    });

    mainWindow.on('closed', () => {
        console.log('🔄 Fermeture application...');
        mainWindow = null;
        if (jarvisProcess) {
            jarvisProcess.kill();
        }
    });
}

// Démarrage automatique de JARVIS
function startJarvis() {
    console.log('🚀 Démarrage automatique JARVIS...');
    
    try {
        jarvisProcess = spawn('python3', ['jarvis_architecture_multi_fenetres.py'], {
            cwd: __dirname,
            stdio: 'pipe'
        });
        
        console.log('✅ JARVIS démarré automatiquement (PID:', jarvisProcess.pid + ')');
        
        jarvisProcess.on('error', (error) => {
            console.log('❌ Erreur JARVIS:', error.message);
        });
        
    } catch (error) {
        console.log('❌ Impossible de démarrer JARVIS:', error.message);
    }
}

app.whenReady().then(() => {
    createWindow();
    startJarvis();
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});
