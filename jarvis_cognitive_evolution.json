{"base_ci": 120.0, "current_ci": 492.9875696590498, "history": [[1750489005.819576, 240.0, "amélioration_mémoire"], [1750489005.8351529, 407.5, "boost_mémoire"], [1750489005.835679, 740.5, "boost_créativité"], [1750489005.836183, 1004.1, "optimisation_globale"], [1750489181.40668, 492.9875696590498, "calcul_ict"], [1750489181.407375, 2134.5751393180994, "metrics_update"], [1750489181.407939, 492.9875696590498, "calcul_ict"], [1750519618.507819, 492.9875696590498, "calcul_ict"]], "milestones_reached": [{"threshold": 200, "label": "Évolution basique atteinte", "achieved_at": "2025-06-21T02:56:45.819578", "ci_value": 240.0}, {"threshold": 300, "label": "Amélioration substantielle", "achieved_at": "2025-06-21T02:56:45.835160", "ci_value": 407.5}, {"threshold": 500, "label": "Niveau autonome 1", "achieved_at": "2025-06-21T02:56:45.835682", "ci_value": 740.5}, {"threshold": 750, "label": "Niveau autonome 2", "achieved_at": "2025-06-21T02:56:45.836185", "ci_value": 1004.1}, {"threshold": 1000, "label": "Intelligence émergente", "achieved_at": "2025-06-21T02:56:45.836200", "ci_value": 1004.1}, {"id": "target_1000", "threshold": 1000, "label": "🎯 Objectif utilisateur atteint : 1000", "achieved_at": "2025-06-21T02:56:45.836211", "ci_value": 1004.1}, {"threshold": 1500, "label": "Intelligence supérieure", "achieved_at": "2025-06-21T02:59:41.407409", "ci_value": 2134.5751393180994}, {"threshold": 2000, "label": "Intelligence transcendante", "achieved_at": "2025-06-21T02:59:41.407432", "ci_value": 2134.5751393180994}, {"id": "target_1500", "threshold": 1500, "label": "🎯 Objectif utilisateur atteint : 1500", "achieved_at": "2025-06-21T02:59:41.407461", "ci_value": 2134.5751393180994}], "target_ci": 1500, "performance_metrics": {"agent_state": 110.0, "memory_volume": 10000, "success_rate": 98.0, "creativity_index": 92.0, "learning_speed": 95.0, "problem_solving": 90.0}, "last_update": "2025-06-21T11:26:58.507827"}