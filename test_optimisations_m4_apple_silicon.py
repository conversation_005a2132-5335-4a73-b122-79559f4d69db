#!/usr/bin/env python3
"""
TEST OPTIMISATIONS M4 APPLE SILICON - JEAN-LUC PASSAVE
Test des optimisations spécifiques pour les puces Apple Silicon M4
"""

import time
import platform
from jarvis_optimisation_m4_apple_silicon import get_apple_silicon_optimizer, is_apple_silicon, get_optimal_settings_for_m4
from memoire_thermique_turbo_adaptatif import get_memoire_thermique

def test_detection_apple_silicon():
    """Test de détection de l'architecture Apple Silicon"""
    
    print("🍎 TEST DÉTECTION APPLE SILICON")
    print("=" * 50)
    
    # Détection architecture
    architecture = platform.machine()
    is_m4 = is_apple_silicon()
    
    print(f"📱 Architecture détectée: {architecture}")
    print(f"🍎 Apple Silicon: {'✅ OUI' if is_m4 else '❌ NON'}")
    
    if is_m4:
        print("🎉 EXCELLENT ! Optimisations M4 disponibles")
        
        # Obtenir les paramètres optimaux
        settings = get_optimal_settings_for_m4()
        print(f"\n⚙️ Paramètres optimaux M4:")
        print(f"   ⚡ P-cores: {settings['performance_cores']}")
        print(f"   🔋 E-cores: {settings['efficiency_cores']}")
        print(f"   🧠 Neural Engine: {'✅' if settings['neural_engine'] else '❌'}")
        print(f"   💾 Unified Memory: {'✅' if settings['unified_memory'] else '❌'}")
        print(f"   📊 Max Threads: {settings['max_threads']}")
        print(f"   🗜️ Chunk Size: {settings['chunk_size'] // 1024} KB")
        
        return True
    else:
        print("ℹ️ Architecture non-Apple Silicon détectée")
        print("   Utilisation des optimisations standard")
        return False

def test_optimiseur_apple_silicon():
    """Test de l'optimiseur Apple Silicon"""
    
    print("\n🔧 TEST OPTIMISEUR APPLE SILICON")
    print("=" * 50)
    
    optimizer = get_apple_silicon_optimizer()
    
    # Statistiques système
    stats = optimizer.get_memory_stats()
    print(f"\n📊 Statistiques système:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Test optimisation chunk size
    print(f"\n📏 Test tailles de chunks optimales:")
    test_sizes = [1024, 10240, 102400, 1048576, 10485760]  # 1KB à 10MB
    
    for size in test_sizes:
        optimal_chunk = optimizer.get_optimal_chunk_size(size)
        print(f"   Données {size//1024}KB → Chunk {optimal_chunk//1024}KB")
    
    # Test stratégie d'accès mémoire
    print(f"\n💾 Test stratégies d'accès mémoire:")
    memory_sizes = [10, 100, 1000, 5000]  # MB
    
    for size_mb in memory_sizes:
        strategy = optimizer.optimize_memory_access(size_mb)
        print(f"   {size_mb}MB → Stratégie: {strategy}")
    
    return True

def test_memoire_thermique_m4():
    """Test de la mémoire thermique avec optimisations M4"""
    
    print("\n🧠 TEST MÉMOIRE THERMIQUE M4")
    print("=" * 50)
    
    memoire = get_memoire_thermique()
    
    # Vérifier si les optimisations M4 sont actives
    print(f"🍎 Apple Silicon détecté: {'✅' if memoire.is_apple_silicon else '❌'}")
    
    if memoire.is_apple_silicon:
        print(f"⚡ Facteur cascade M4: {memoire.facteur_cascade}x")
        print(f"🚀 Facteur accélération M4: {memoire.acceleration_factor}x")
        print(f"🔧 Paramètres M4: {len(memoire.m4_settings)} options")
        
        # Test ajout de souvenirs avec optimisations M4
        print(f"\n📝 Test ajout souvenirs optimisé M4:")
        
        start_time = time.time()
        
        # Ajouter plusieurs souvenirs pour tester les optimisations
        for i in range(100):
            memoire.ajouter_souvenir(
                content=f"Test optimisation M4 #{i} - Jean-Luc Passave utilise les P-cores et E-cores",
                tags=["test", "m4", "optimisation", f"batch_{i//10}"],
                important=(i % 10 == 0),
                emotional_context="test_performance"
            )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   ⏱️ Temps pour 100 souvenirs: {duration:.3f}s")
        print(f"   🚀 Vitesse M4: {100/duration:.0f} souvenirs/seconde")
        
        # Test recherche optimisée M4
        print(f"\n🔍 Test recherche optimisée M4:")
        
        start_time = time.time()
        
        for i in range(50):
            resultats = memoire.rechercher_intelligent("optimisation")
        
        end_time = time.time()
        search_duration = end_time - start_time
        
        print(f"   ⏱️ Temps pour 50 recherches: {search_duration:.3f}s")
        print(f"   🔍 Vitesse M4: {50/search_duration:.0f} recherches/seconde")
        
        # Test sauvegarde optimisée M4
        print(f"\n💾 Test sauvegarde optimisée M4:")
        
        start_time = time.time()
        success = memoire.sauvegarder_optimise_m4("test_memoire_m4.json")
        end_time = time.time()
        save_duration = end_time - start_time
        
        if success:
            print(f"   ✅ Sauvegarde M4 réussie en {save_duration:.3f}s")
        else:
            print(f"   ❌ Échec sauvegarde M4")
        
        # Test chargement optimisé M4
        print(f"\n📂 Test chargement optimisé M4:")
        
        start_time = time.time()
        success = memoire.charger_optimise_m4("test_memoire_m4.json")
        end_time = time.time()
        load_duration = end_time - start_time
        
        if success:
            print(f"   ✅ Chargement M4 réussi en {load_duration:.3f}s")
        else:
            print(f"   ❌ Échec chargement M4")
        
        return True
    else:
        print("ℹ️ Optimisations M4 non disponibles sur cette architecture")
        return False

def test_performance_comparative():
    """Test de performance comparative M4 vs standard"""
    
    print("\n📊 TEST PERFORMANCE COMPARATIVE")
    print("=" * 50)
    
    memoire = get_memoire_thermique()
    
    if not memoire.is_apple_silicon:
        print("ℹ️ Test de performance non disponible (architecture non-Apple)")
        return False
    
    # Test 1: Ajout massif de données
    print(f"\n🚀 Test 1: Ajout massif (1000 souvenirs)")
    
    start_time = time.time()
    
    for i in range(1000):
        memoire.ajouter_souvenir(
            content=f"Performance test M4 #{i} - Utilisation optimale des cœurs Apple Silicon",
            tags=["performance", "m4", "test", f"serie_{i//100}"],
            important=(i % 50 == 0),
            emotional_context="performance_test"
        )
    
    end_time = time.time()
    mass_add_duration = end_time - start_time
    
    print(f"   ⏱️ Temps: {mass_add_duration:.3f}s")
    print(f"   🚀 Vitesse: {1000/mass_add_duration:.0f} souvenirs/seconde")
    
    # Test 2: Recherche massive
    print(f"\n🔍 Test 2: Recherche massive (500 recherches)")
    
    search_terms = ["performance", "m4", "test", "apple", "silicon", "optimisation"]
    
    start_time = time.time()
    
    for i in range(500):
        term = search_terms[i % len(search_terms)]
        resultats = memoire.rechercher_intelligent(term)
    
    end_time = time.time()
    mass_search_duration = end_time - start_time
    
    print(f"   ⏱️ Temps: {mass_search_duration:.3f}s")
    print(f"   🔍 Vitesse: {500/mass_search_duration:.0f} recherches/seconde")
    
    # Test 3: Opérations mixtes
    print(f"\n🔄 Test 3: Opérations mixtes (100 cycles)")
    
    start_time = time.time()
    
    for i in range(100):
        # Ajouter un souvenir
        memoire.ajouter_souvenir(
            content=f"Cycle mixte #{i}",
            tags=["mixte", "cycle"],
            emotional_context="test"
        )
        
        # Faire une recherche
        memoire.rechercher_intelligent("cycle")
        
        # Générer des suggestions
        memoire.get_suggestions_intelligentes()
    
    end_time = time.time()
    mixed_duration = end_time - start_time
    
    print(f"   ⏱️ Temps: {mixed_duration:.3f}s")
    print(f"   🔄 Vitesse: {100/mixed_duration:.0f} cycles/seconde")
    
    return True

def main():
    """Test principal des optimisations M4"""
    
    print("🍎 TEST COMPLET OPTIMISATIONS M4 APPLE SILICON")
    print("=" * 80)
    print("👤 Jean-Luc Passave - Validation optimisations puces M4")
    print("=" * 80)
    
    try:
        # Test 1: Détection Apple Silicon
        is_m4_detected = test_detection_apple_silicon()
        
        # Test 2: Optimiseur Apple Silicon
        optimizer_ok = test_optimiseur_apple_silicon()
        
        # Test 3: Mémoire thermique M4
        memory_m4_ok = test_memoire_thermique_m4()
        
        # Test 4: Performance comparative
        performance_ok = test_performance_comparative()
        
        # Résumé final
        print("\n🎉 RÉSUMÉ FINAL OPTIMISATIONS M4")
        print("=" * 50)
        print(f"🍎 Apple Silicon détecté: {'✅' if is_m4_detected else '❌'}")
        print(f"🔧 Optimiseur fonctionnel: {'✅' if optimizer_ok else '❌'}")
        print(f"🧠 Mémoire M4 optimisée: {'✅' if memory_m4_ok else '❌'}")
        print(f"📊 Tests performance: {'✅' if performance_ok else '❌'}")
        
        if is_m4_detected and optimizer_ok and memory_m4_ok:
            print("\n🎉 PARFAIT ! Optimisations M4 parfaitement fonctionnelles")
            print("✅ Votre JARVIS utilise maintenant toute la puissance du M4")
            print("🚀 P-cores, E-cores et Neural Engine optimisés")
            print("💾 Mémoire unifiée exploitée au maximum")
            print("⚡ Performance maximale atteinte !")
            
            return True
        elif not is_m4_detected:
            print("\nℹ️ Architecture non-Apple Silicon")
            print("✅ Optimisations standard actives")
            return True
        else:
            print("\n⚠️ Certaines optimisations M4 ont des problèmes")
            return False
        
    except Exception as e:
        print(f"\n❌ ERREUR DURANT LES TESTS M4: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
