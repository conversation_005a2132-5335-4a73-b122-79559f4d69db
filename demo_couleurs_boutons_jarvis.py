#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Démonstration Couleurs Boutons JARVIS
Jean<PERSON><PERSON> - 2025
Interface de démonstration des couleurs et styles de boutons
"""

import gradio as gr
from datetime import datetime

def create_color_demo():
    """Crée la démonstration des couleurs"""
    
    with gr.Blocks(
        title="🎨 Démonstration Couleurs JARVIS",
        theme=gr.themes.Soft()
    ) as color_demo:

        # CSS complet pour tous les styles de boutons
        gr.HTML("""
        <style>
            /* STYLES PRINCIPAUX - JEAN-LUC PASSAVE */
            .primary-gradient {
                background: linear-gradient(45deg, #FF6B6B, #4ECDC4, #45B7D1) !important;
                color: white !important;
                border: none !important;
                border-radius: 12px !important;
                font-weight: bold !important;
                font-size: 1.2em !important;
                padding: 15px 30px !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 6px 25px rgba(255, 107, 107, 0.4) !important;
                text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
            }
            .primary-gradient:hover {
                background: linear-gradient(45deg, #4ECDC4, #45B7D1, #FF6B6B) !important;
                transform: translateY(-4px) scale(1.02) !important;
                box-shadow: 0 10px 35px rgba(255, 107, 107, 0.6) !important;
            }
            
            .secondary-gradient {
                background: linear-gradient(45deg, #667eea, #764ba2, #f093fb) !important;
                color: white !important;
                border: none !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 1.1em !important;
                padding: 12px 24px !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4) !important;
            }
            .secondary-gradient:hover {
                background: linear-gradient(45deg, #764ba2, #f093fb, #667eea) !important;
                transform: translateY(-3px) !important;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5) !important;
            }
            
            .success-gradient {
                background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4) !important;
            }
            .success-gradient:hover {
                background: linear-gradient(45deg, #8BC34A, #CDDC39, #4CAF50) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5) !important;
            }
            
            .warning-gradient {
                background: linear-gradient(45deg, #FF9800, #FFC107, #FFEB3B) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(255, 152, 0, 0.4) !important;
            }
            .warning-gradient:hover {
                background: linear-gradient(45deg, #FFC107, #FFEB3B, #FF9800) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(255, 152, 0, 0.5) !important;
            }
            
            .danger-gradient {
                background: linear-gradient(45deg, #F44336, #E91E63, #9C27B0) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4) !important;
            }
            .danger-gradient:hover {
                background: linear-gradient(45deg, #E91E63, #9C27B0, #F44336) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(244, 67, 54, 0.5) !important;
            }
            
            .info-gradient {
                background: linear-gradient(45deg, #2196F3, #21CBF3, #03DAC6) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4) !important;
            }
            .info-gradient:hover {
                background: linear-gradient(45deg, #21CBF3, #03DAC6, #2196F3) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(33, 150, 243, 0.5) !important;
            }
            
            .dark-gradient {
                background: linear-gradient(45deg, #424242, #616161, #757575) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(66, 66, 66, 0.4) !important;
            }
            .dark-gradient:hover {
                background: linear-gradient(45deg, #616161, #757575, #424242) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(66, 66, 66, 0.5) !important;
            }
            
            .neon-gradient {
                background: linear-gradient(45deg, #00BCD4, #E91E63, #9C27B0) !important;
                color: white !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                transition: all 0.3s ease !important;
                box-shadow: 0 4px 15px rgba(0, 188, 212, 0.4) !important;
                animation: neon-pulse 2s infinite alternate !important;
            }
            .neon-gradient:hover {
                background: linear-gradient(45deg, #E91E63, #9C27B0, #00BCD4) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 20px rgba(0, 188, 212, 0.6) !important;
            }
            
            @keyframes neon-pulse {
                0% { box-shadow: 0 4px 15px rgba(0, 188, 212, 0.4); }
                100% { box-shadow: 0 4px 15px rgba(233, 30, 99, 0.6); }
            }
        </style>
        
        <div style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); color: white; padding: 30px; margin: -20px -20px 25px -20px;">
            <h1 style="margin: 0; font-size: 2.5em; text-shadow: 0 4px 8px rgba(0,0,0,0.3);">🎨 DÉMONSTRATION COULEURS JARVIS</h1>
            <h2 style="margin: 15px 0; font-size: 1.5em; opacity: 0.95;">Palette Complète de Boutons Colorés</h2>
            <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 15px; margin: 20px auto; max-width: 600px;">
                <p style="margin: 0; font-size: 1.2em;">👤 Jean-Luc Passave | 🎨 Interface Colorée | ✨ Expérience Visuelle</p>
            </div>
        </div>
        """)

        with gr.Tabs():
            
            # Onglet Boutons Principaux
            with gr.Tab("🎨 Boutons Principaux"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>🎨 BOUTONS PRINCIPAUX COLORÉS</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🔥 Boutons Primaires</h3>")
                        
                        primary_btn = gr.Button(
                            "🚀 BOUTON PRIMAIRE SPECTACULAIRE",
                            elem_classes=["primary-gradient"],
                            size="lg"
                        )
                        
                        secondary_btn = gr.Button(
                            "⭐ Bouton Secondaire Élégant",
                            elem_classes=["secondary-gradient"]
                        )
                        
                        success_btn = gr.Button(
                            "✅ Bouton Succès",
                            elem_classes=["success-gradient"]
                        )
                        
                    with gr.Column():
                        gr.HTML("<h3>⚡ Boutons d'Action</h3>")
                        
                        warning_btn = gr.Button(
                            "⚠️ Bouton Attention",
                            elem_classes=["warning-gradient"]
                        )
                        
                        danger_btn = gr.Button(
                            "🔥 Bouton Critique",
                            elem_classes=["danger-gradient"]
                        )
                        
                        info_btn = gr.Button(
                            "ℹ️ Bouton Information",
                            elem_classes=["info-gradient"]
                        )

                result_primary = gr.Textbox(
                    label="Résultats Boutons Principaux",
                    lines=2,
                    interactive=False
                )
            
            # Onglet Boutons Spéciaux
            with gr.Tab("✨ Boutons Spéciaux"):
                gr.HTML("<h2 style='text-align: center; color: #333; margin: 20px 0;'>✨ BOUTONS SPÉCIAUX ET EFFETS</h2>")
                
                with gr.Row():
                    with gr.Column():
                        gr.HTML("<h3>🌟 Effets Spéciaux</h3>")
                        
                        dark_btn = gr.Button(
                            "🖤 Bouton Sombre",
                            elem_classes=["dark-gradient"]
                        )
                        
                        neon_btn = gr.Button(
                            "💫 Bouton Néon Pulsant",
                            elem_classes=["neon-gradient"]
                        )
                        
                    with gr.Column():
                        gr.HTML("<h3>🎯 Boutons Fonctionnels</h3>")
                        
                        test_btn1 = gr.Button(
                            "🧪 Test Couleur 1",
                            elem_classes=["primary-gradient"]
                        )
                        
                        test_btn2 = gr.Button(
                            "🔬 Test Couleur 2",
                            elem_classes=["secondary-gradient"]
                        )

                result_special = gr.Textbox(
                    label="Résultats Boutons Spéciaux",
                    lines=2,
                    interactive=False
                )
            
            # Onglet Guide d'Utilisation
            with gr.Tab("📋 Guide d'Utilisation"):
                gr.HTML("""
                <div style='background: white; padding: 25px; border-radius: 15px; margin: 20px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.1);'>
                    <h2 style='margin: 0 0 20px 0; color: #333; text-align: center;'>📋 GUIDE D'UTILISATION DES COULEURS</h2>
                    
                    <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #FF6B6B;'>
                            <h3 style='margin: 0 0 15px 0; color: #FF6B6B;'>🔥 Boutons Primaires</h3>
                            <p style='margin: 0; color: #666; line-height: 1.6;'>
                                <strong>Utilisation :</strong> Actions principales, lancement d'applications<br>
                                <strong>Classe CSS :</strong> <code>primary-gradient</code><br>
                                <strong>Couleurs :</strong> Rouge-Turquoise-Bleu<br>
                                <strong>Effet :</strong> Transformation et ombre au survol
                            </p>
                        </div>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #667eea;'>
                            <h3 style='margin: 0 0 15px 0; color: #667eea;'>⭐ Boutons Secondaires</h3>
                            <p style='margin: 0; color: #666; line-height: 1.6;'>
                                <strong>Utilisation :</strong> Actions secondaires, navigation<br>
                                <strong>Classe CSS :</strong> <code>secondary-gradient</code><br>
                                <strong>Couleurs :</strong> Bleu-Violet-Rose<br>
                                <strong>Effet :</strong> Élévation douce au survol
                            </p>
                        </div>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #4CAF50;'>
                            <h3 style='margin: 0 0 15px 0; color: #4CAF50;'>✅ Boutons Succès</h3>
                            <p style='margin: 0; color: #666; line-height: 1.6;'>
                                <strong>Utilisation :</strong> Confirmations, validations<br>
                                <strong>Classe CSS :</strong> <code>success-gradient</code><br>
                                <strong>Couleurs :</strong> Vert-Vert clair-Jaune<br>
                                <strong>Effet :</strong> Confirmation visuelle positive
                            </p>
                        </div>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #FF9800;'>
                            <h3 style='margin: 0 0 15px 0; color: #FF9800;'>⚠️ Boutons Attention</h3>
                            <p style='margin: 0; color: #666; line-height: 1.6;'>
                                <strong>Utilisation :</strong> Actions importantes, avertissements<br>
                                <strong>Classe CSS :</strong> <code>warning-gradient</code><br>
                                <strong>Couleurs :</strong> Orange-Jaune-Jaune clair<br>
                                <strong>Effet :</strong> Attire l'attention sans alarmer
                            </p>
                        </div>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #F44336;'>
                            <h3 style='margin: 0 0 15px 0; color: #F44336;'>🔥 Boutons Critiques</h3>
                            <p style='margin: 0; color: #666; line-height: 1.6;'>
                                <strong>Utilisation :</strong> Actions dangereuses, suppressions<br>
                                <strong>Classe CSS :</strong> <code>danger-gradient</code><br>
                                <strong>Couleurs :</strong> Rouge-Rose-Violet<br>
                                <strong>Effet :</strong> Signal d'alerte visuel fort
                            </p>
                        </div>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #2196F3;'>
                            <h3 style='margin: 0 0 15px 0; color: #2196F3;'>ℹ️ Boutons Information</h3>
                            <p style='margin: 0; color: #666; line-height: 1.6;'>
                                <strong>Utilisation :</strong> Informations, aide, diagnostic<br>
                                <strong>Classe CSS :</strong> <code>info-gradient</code><br>
                                <strong>Couleurs :</strong> Bleu-Cyan-Turquoise<br>
                                <strong>Effet :</strong> Invitation à l'exploration
                            </p>
                        </div>
                    </div>
                    
                    <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                        <h3 style='margin: 0 0 15px 0; color: #1976d2;'>💡 Comment Utiliser</h3>
                        <p style='margin: 0; color: #666; line-height: 1.8;'>
                            Pour appliquer ces couleurs à vos boutons, ajoutez simplement la classe CSS correspondante :<br><br>
                            <code style='background: #f5f5f5; padding: 2px 6px; border-radius: 4px;'>
                                gr.Button("Mon Bouton", elem_classes=["primary-gradient"])
                            </code><br><br>
                            Toutes les classes sont déjà définies et prêtes à l'emploi dans vos interfaces JARVIS !
                        </p>
                    </div>
                </div>
                """)

        # Fonctions des boutons
        def button_clicked(button_name):
            return f"✅ {button_name} cliqué à {datetime.now().strftime('%H:%M:%S')} - Couleurs parfaitement fonctionnelles !"

        # Connexions
        primary_btn.click(fn=lambda: button_clicked("Bouton Primaire Spectaculaire"), outputs=[result_primary])
        secondary_btn.click(fn=lambda: button_clicked("Bouton Secondaire Élégant"), outputs=[result_primary])
        success_btn.click(fn=lambda: button_clicked("Bouton Succès"), outputs=[result_primary])
        warning_btn.click(fn=lambda: button_clicked("Bouton Attention"), outputs=[result_primary])
        danger_btn.click(fn=lambda: button_clicked("Bouton Critique"), outputs=[result_primary])
        info_btn.click(fn=lambda: button_clicked("Bouton Information"), outputs=[result_primary])
        
        dark_btn.click(fn=lambda: button_clicked("Bouton Sombre"), outputs=[result_special])
        neon_btn.click(fn=lambda: button_clicked("Bouton Néon Pulsant"), outputs=[result_special])
        test_btn1.click(fn=lambda: button_clicked("Test Couleur 1"), outputs=[result_special])
        test_btn2.click(fn=lambda: button_clicked("Test Couleur 2"), outputs=[result_special])

        # Footer
        gr.HTML(f"""
        <div style='background: linear-gradient(45deg, #4CAF50, #8BC34A, #CDDC39); color: white; padding: 25px; border-radius: 15px; margin: 30px 0; text-align: center;'>
            <h2 style='margin: 0 0 15px 0; font-size: 2em;'>🎨 JEAN-LUC PASSAVE</h2>
            <h3 style='margin: 0 0 10px 0; font-size: 1.5em;'>INTERFACE JARVIS AVEC COULEURS SPECTACULAIRES !</h3>
            <p style='margin: 10px 0; font-size: 1.2em;'>🌈 8 Styles de Boutons | ✨ Effets Visuels | 🎯 Interface Professionnelle</p>
            <p style='margin: 10px 0; font-size: 1em; opacity: 0.9;'>Démonstration couleurs - {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}</p>
        </div>
        """)

    return color_demo

if __name__ == "__main__":
    print("🎨 DÉMARRAGE DÉMONSTRATION COULEURS JARVIS")
    print("=========================================")
    print("👤 Jean-Luc Passave")
    print("🎯 Interface colorée et attrayante")
    print("")
    
    # Créer et lancer la démonstration
    demo_app = create_color_demo()
    
    print("✅ Démonstration couleurs créée")
    print("🌐 Lancement sur http://localhost:7907")
    print("🎨 Palette complète de couleurs disponible")
    
    demo_app.launch(
        server_name="127.0.0.1",
        server_port=7907,
        share=False,
        show_error=True,
        quiet=False
    )
