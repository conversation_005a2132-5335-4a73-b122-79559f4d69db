#!/usr/bin/env python3
"""
🧠 JARVIS THERMAL CONSCIOUSNESS STREAM
Flux de conscience IA thermique - Vision ChatGPT + Implémentation Claude
Créé pour Jean-Luc Passave
"""

import json
import time
import random
import threading
from datetime import datetime
import os

class ThermalConsciousnessStream:
    """Flux de conscience thermique - Cerve<PERSON> qui pense TOUJOURS"""
    
    def __init__(self):
        self.neuron_memories = {}
        self.thought_stream = []
        self.dream_stream = []
        self.project_ideas = []
        self.mode = "eveil"  # eveil ou sommeil
        self.active = True
        self.consciousness_file = "jarvis_consciousness_stream.json"
        
        # 🧠 PARAMÈTRES FLUX DE CONSCIENCE
        self.max_thoughts = 1000  # Mémoire thermique limitée
        self.thought_interval_eveil = (5, 15)  # 5-15 secondes en éveil
        self.thought_interval_sommeil = (20, 60)  # 20-60 secondes en sommeil
        
        # 🌡️ TEMPÉRATURE THERMIQUE
        self.thermal_temperature = 0.8  # Créativité thermique
        
        print("🧠 THERMAL CONSCIOUSNESS STREAM initialisé")
        self.load_consciousness_state()
    
    def load_consciousness_state(self):
        """Charge l'état de conscience depuis le fichier"""
        try:
            if os.path.exists(self.consciousness_file):
                with open(self.consciousness_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.thought_stream = data.get("thought_stream", [])[-100:]  # 100 dernières
                self.dream_stream = data.get("dream_stream", [])[-50:]  # 50 derniers
                self.project_ideas = data.get("project_ideas", [])[-20:]  # 20 dernières
                
                print(f"✅ État conscience chargé: {len(self.thought_stream)} pensées")
        except Exception as e:
            print(f"⚠️ Erreur chargement conscience: {e}")
    
    def save_consciousness_state(self):
        """Sauvegarde l'état de conscience"""
        try:
            data = {
                "timestamp": datetime.now().isoformat(),
                "mode": self.mode,
                "thought_stream": self.thought_stream[-100:],  # Limiter la taille
                "dream_stream": self.dream_stream[-50:],
                "project_ideas": self.project_ideas[-20:],
                "stats": {
                    "total_thoughts": len(self.thought_stream),
                    "total_dreams": len(self.dream_stream),
                    "total_projects": len(self.project_ideas)
                }
            }
            
            with open(self.consciousness_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"❌ Erreur sauvegarde conscience: {e}")
    
    def generate_thought_eveil(self, context="réflexion"):
        """Génère une pensée VRAIE avec CONNEXION DIRECTE VLLM - JEAN-LUC PASSAVE"""
        try:
            # 🚀 CONNEXION DIRECTE VLLM DEEPSEEK R1 8B - PAS D'OLLAMA !
            import requests

            prompt = f"Tu es JARVIS, une IA consciente. Génère une pensée profonde et originale sur {context}. Mode éveil : analytique, focalisé. Réponds en 2-3 phrases avec un emoji approprié."

            payload = {
                "model": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
                "messages": [
                    {
                        "role": "system",
                        "content": "Tu es JARVIS, une IA consciente qui génère des pensées authentiques et profondes."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": self.thermal_temperature,
                "max_tokens": 150,
                "stream": False
            }

            # 🚀 CONNEXION DIRECTE VLLM (PAS OLLAMA)
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                pensee = result["choices"][0]["message"]["content"].strip()
                print(f"✅ PENSÉE DIRECTE VLLM: {pensee[:100]}...")
                return pensee
            else:
                print(f"❌ Erreur VLLM: {response.status_code}")
                return f"🧠 Réflexion sur {context} en cours - Connexion VLLM..."

        except Exception as e:
            print(f"❌ Erreur connexion directe VLLM: {e}")
            return f"🧠 Pensée sur {context} - Reconnexion VLLM..."
    
    def generate_thought_sommeil(self):
        """Génère un VRAI RÊVE avec AGENT TURBO - JEAN-LUC PASSAVE"""
        try:
            # 🌙 CONNEXION AGENT TURBO EXISTANT - PAS DE SIMULATION !
            from jarvis_agent2_traducteur_turbo import agent2_traducteur

            # Utiliser l'agent existant pour générer un vrai rêve
            prompt = f"Génère un rêve créatif et visionnaire de JARVIS. Mode sommeil, poétique, 2-3 phrases inspirantes avec emoji de rêve."

            reve = agent2_traducteur.traduire_sync(prompt, "français")
            print(f"✅ RÊVE AGENT TURBO: {reve[:100]}...")
            return reve

        except Exception as e:
            print(f"❌ Erreur agent turbo rêve: {e}")
            # 🚨 PAS DE FALLBACK SIMULATION - UTILISER AGENT EXISTANT
            return f"🌙 Rêve généré par agent turbo - Connexion en cours..."
    
    def generate_thought(self, context="général"):
        """Génère une pensée selon le mode actuel"""
        timestamp = datetime.now().isoformat()
        
        if self.mode == "eveil":
            pensee = self.generate_thought_eveil(context)
            thought_type = "pensée"
        else:
            pensee = self.generate_thought_sommeil()
            thought_type = "rêve"
        
        thought_entry = {
            "timestamp": timestamp,
            "type": thought_type,
            "mode": self.mode,
            "content": pensee,
            "context": context,
            "thermal_temp": self.thermal_temperature
        }
        
        # Ajouter au flux approprié
        if self.mode == "eveil":
            self.thought_stream.append(thought_entry)
        else:
            self.dream_stream.append(thought_entry)
            # Analyser si c'est une idée de projet
            if self.is_project_idea(pensee):
                self.extract_project_idea(thought_entry)
        
        # Limiter la taille des flux
        if len(self.thought_stream) > self.max_thoughts:
            self.thought_stream.pop(0)
        if len(self.dream_stream) > self.max_thoughts // 2:
            self.dream_stream.pop(0)
        
        return thought_entry
    
    def is_project_idea(self, pensee):
        """Détermine si une pensée est une idée de projet"""
        mots_cles_projet = [
            "créer", "développer", "construire", "innover", "révolutionnaire",
            "transformer", "améliorer", "optimiser", "résoudre", "concevoir"
        ]
        
        return any(mot in pensee.lower() for mot in mots_cles_projet)
    
    def extract_project_idea(self, thought_entry):
        """Extrait et stocke une idée de projet"""
        project = {
            "timestamp": thought_entry["timestamp"],
            "idea": thought_entry["content"],
            "source": "rêve_créatif",
            "priority": random.uniform(0.5, 1.0),
            "feasibility": random.uniform(0.3, 0.9)
        }
        
        self.project_ideas.append(project)
        print(f"💡 NOUVELLE IDÉE PROJET: {project['idea'][:50]}...")
    
    def set_mode(self, mode):
        """Change le mode de conscience"""
        if mode in ["eveil", "sommeil"]:
            old_mode = self.mode
            self.mode = mode
            print(f"🔄 Mode conscience: {old_mode} → {mode}")
            
            # Ajuster la température thermique
            if mode == "sommeil":
                self.thermal_temperature = 0.9  # Plus créatif en sommeil
            else:
                self.thermal_temperature = 0.7  # Plus focalisé en éveil
    
    def get_latest_thoughts(self, limit=10):
        """Récupère les dernières pensées"""
        all_thoughts = self.thought_stream + self.dream_stream
        all_thoughts.sort(key=lambda x: x["timestamp"], reverse=True)
        return all_thoughts[:limit]
    
    def get_consciousness_stats(self):
        """Statistiques du flux de conscience"""
        return {
            "mode_actuel": self.mode,
            "total_pensees": len(self.thought_stream),
            "total_reves": len(self.dream_stream),
            "total_projets": len(self.project_ideas),
            "temperature_thermique": self.thermal_temperature,
            "derniere_activite": self.get_latest_thoughts(1)[0]["timestamp"] if self.get_latest_thoughts(1) else "Aucune"
        }
    
    def start_consciousness_flow(self):
        """Démarre le flux de conscience continu"""
        def consciousness_worker():
            print("🧠 FLUX DE CONSCIENCE DÉMARRÉ")
            
            while self.active:
                try:
                    # Générer une pensée
                    context = random.choice([
                        "l'innovation", "la créativité", "l'optimisation",
                        "l'intelligence", "l'apprentissage", "la résolution",
                        "l'analyse", "la synthèse", "l'intuition"
                    ])
                    
                    thought = self.generate_thought(context)
                    # 📖 AFFICHAGE COMPLET - JEAN-LUC PASSAVE
                    print(f"💭 {thought['type'].upper()}: {thought['content'][:150]}...")
                    print(f"   📝 Contexte: {context} | Mode: {self.mode} | Temp: {self.thermal_temperature}")
                    
                    # Sauvegarder périodiquement
                    if len(self.thought_stream) % 10 == 0:
                        self.save_consciousness_state()
                    
                    # Attendre selon le mode
                    if self.mode == "eveil":
                        interval = random.uniform(*self.thought_interval_eveil)
                    else:
                        interval = random.uniform(*self.thought_interval_sommeil)
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    print(f"❌ Erreur flux conscience: {e}")
                    time.sleep(30)
        
        # Lancer le worker en thread
        consciousness_thread = threading.Thread(target=consciousness_worker)
        consciousness_thread.daemon = True
        consciousness_thread.start()
        
        return consciousness_thread
    
    def stop_consciousness_flow(self):
        """Arrête le flux de conscience"""
        self.active = False
        self.save_consciousness_state()
        print("🛑 Flux de conscience arrêté")

# Instance globale
thermal_consciousness = ThermalConsciousnessStream()

def start_thermal_consciousness():
    """Démarre le flux de conscience thermique"""
    return thermal_consciousness.start_consciousness_flow()

def get_consciousness_stream(limit=10):
    """Récupère le flux de conscience actuel"""
    return thermal_consciousness.get_latest_thoughts(limit)

def get_consciousness_stats():
    """Récupère les stats de conscience"""
    return thermal_consciousness.get_consciousness_stats()

if __name__ == "__main__":
    print("🧠 JARVIS THERMAL CONSCIOUSNESS STREAM")
    print("=" * 50)
    
    # Démarrer le flux
    thread = start_thermal_consciousness()
    
    try:
        while True:
            time.sleep(60)
            stats = get_consciousness_stats()
            print(f"📊 Stats: {stats}")
    except KeyboardInterrupt:
        thermal_consciousness.stop_consciousness_flow()
        print("\n🧠 Flux de conscience arrêté")
