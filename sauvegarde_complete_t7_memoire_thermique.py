#!/usr/bin/env python3
"""
SAUVEGARDE COMPLÈTE T7 MÉMOIRE THERMIQUE - JEAN-LUC PASSAVE
Sauvegarde complète de la mémoire thermique et nettoyage pour faire de la place
"""

import os
import shutil
import json
import gzip
from datetime import datetime
import subprocess

def detecter_t7():
    """Détecte automatiquement le disque T7"""
    
    print("🔍 Détection du disque T7...")
    
    # Chemins possibles pour le T7
    chemins_t7 = [
        "/Volumes/T7",
        "/Volumes/T7 Shield",
        "/Volumes/Samsung_T7",
        "/Volumes/SAMSUNG_T7",
        "/media/t7",
        "/mnt/t7"
    ]
    
    for chemin in chemins_t7:
        if os.path.exists(chemin):
            print(f"✅ T7 détecté: {chemin}")
            return chemin
    
    # Recherche automatique
    try:
        result = subprocess.run(['df', '-h'], capture_output=True, text=True)
        for ligne in result.stdout.split('\n'):
            if 'T7' in ligne or 'Samsung' in ligne:
                parties = ligne.split()
                if len(parties) > 5:
                    chemin = parties[-1]
                    if os.path.exists(chemin):
                        print(f"✅ T7 détecté automatiquement: {chemin}")
                        return chemin
    except:
        pass
    
    print("❌ T7 non détecté")
    return None

def creer_structure_sauvegarde(chemin_t7):
    """Crée la structure de sauvegarde sur le T7"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dossier_sauvegarde = os.path.join(chemin_t7, f"JARVIS_MEMOIRE_THERMIQUE_{timestamp}")
    
    # Créer les dossiers
    dossiers = [
        "memoire_thermique",
        "accelerateurs",
        "neurones",
        "conversations",
        "backups",
        "logs"
    ]
    
    for dossier in dossiers:
        chemin_dossier = os.path.join(dossier_sauvegarde, dossier)
        os.makedirs(chemin_dossier, exist_ok=True)
    
    print(f"📁 Structure créée: {dossier_sauvegarde}")
    return dossier_sauvegarde

def sauvegarder_memoire_thermique(dossier_sauvegarde):
    """Sauvegarde la mémoire thermique complète"""
    
    print("💾 Sauvegarde mémoire thermique...")
    
    fichiers_memoire = [
        "thermal_memory_persistent.json",
        "thermal_memory_persistent.json.backup",
        "thermal_memory_persistent.json.optimized",
        "thermal_memory_persistent.json.global_compressed",
        "conversations_permanentes.json",
        "analyse_semantique_conversations.json"
    ]
    
    dossier_memoire = os.path.join(dossier_sauvegarde, "memoire_thermique")
    fichiers_sauvegardes = 0
    
    for fichier in fichiers_memoire:
        if os.path.exists(fichier):
            # Copie normale
            shutil.copy2(fichier, dossier_memoire)
            
            # Copie compressée
            with open(fichier, 'rb') as f_in:
                with gzip.open(os.path.join(dossier_memoire, f"{fichier}.gz"), 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            fichiers_sauvegardes += 1
            print(f"  ✅ {fichier}")
    
    print(f"💾 {fichiers_sauvegardes} fichiers de mémoire sauvegardés")
    return fichiers_sauvegardes

def sauvegarder_accelerateurs(dossier_sauvegarde):
    """Sauvegarde les accélérateurs"""
    
    print("⚡ Sauvegarde accélérateurs...")
    
    fichiers_accelerateurs = [
        "accelerateur_global_memoire_thermique.py",
        "jarvis_accelerateur_global_unifie.py",
        "jarvis_accelerateurs_compression.py",
        "jarvis_accelerateurs_cascade_persistant.json",
        "super_accelerateur_global_unifie.py"
    ]
    
    dossier_accelerateurs = os.path.join(dossier_sauvegarde, "accelerateurs")
    fichiers_sauvegardes = 0
    
    for fichier in fichiers_accelerateurs:
        if os.path.exists(fichier):
            shutil.copy2(fichier, dossier_accelerateurs)
            fichiers_sauvegardes += 1
            print(f"  ✅ {fichier}")
    
    print(f"⚡ {fichiers_sauvegardes} accélérateurs sauvegardés")
    return fichiers_sauvegardes

def analyser_memoire_thermique():
    """Analyse la mémoire thermique avant sauvegarde"""
    
    print("📊 Analyse mémoire thermique...")
    
    if not os.path.exists("thermal_memory_persistent.json"):
        print("❌ Fichier mémoire thermique introuvable")
        return {}
    
    try:
        with open("thermal_memory_persistent.json", 'r', encoding='utf-8') as f:
            memoire = json.load(f)
        
        # Statistiques
        stats = {
            "timestamp_analyse": datetime.now().isoformat(),
            "taille_fichier_mb": os.path.getsize("thermal_memory_persistent.json") / (1024*1024),
            "nombre_neurones": len(memoire.get("neuron_memories", [])),
            "derniere_modification": datetime.fromtimestamp(os.path.getmtime("thermal_memory_persistent.json")).isoformat()
        }
        
        # Analyser les neurones
        neurones = memoire.get("neuron_memories", [])
        if neurones:
            dates = [n.get("calendar_data", {}).get("date", "") for n in neurones]
            dates_valides = [d for d in dates if d]
            
            if dates_valides:
                stats["premiere_date"] = min(dates_valides)
                stats["derniere_date"] = max(dates_valides)
                stats["periode_jours"] = len(set(dates_valides))
        
        print(f"📊 Analyse terminée:")
        print(f"  📁 Taille: {stats['taille_fichier_mb']:.2f} MB")
        print(f"  🧠 Neurones: {stats['nombre_neurones']:,}")
        print(f"  📅 Dernière modif: {stats['derniere_modification']}")
        
        return stats
        
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        return {}

def creer_rapport_sauvegarde(dossier_sauvegarde, stats):
    """Crée un rapport de sauvegarde"""
    
    rapport = {
        "timestamp_sauvegarde": datetime.now().isoformat(),
        "dossier_sauvegarde": dossier_sauvegarde,
        "stats_memoire": stats,
        "utilisateur": "Jean-Luc Passave",
        "version_jarvis": "2.0 Multi-Fenêtres",
        "statut": "SAUVEGARDE_COMPLETE_REUSSIE"
    }
    
    fichier_rapport = os.path.join(dossier_sauvegarde, "rapport_sauvegarde.json")
    with open(fichier_rapport, 'w', encoding='utf-8') as f:
        json.dump(rapport, f, indent=2, ensure_ascii=False)
    
    print(f"📋 Rapport créé: {fichier_rapport}")
    return rapport

def main():
    """Fonction principale de sauvegarde"""
    
    print("🚀 SAUVEGARDE COMPLÈTE T7 MÉMOIRE THERMIQUE")
    print("=" * 60)
    print("Jean-Luc Passave - Sauvegarde sécurisée")
    print("=" * 60)
    
    # 1. Détecter le T7
    chemin_t7 = detecter_t7()
    if not chemin_t7:
        print("❌ Impossible de continuer sans T7")
        return False
    
    # 2. Analyser la mémoire
    stats = analyser_memoire_thermique()
    
    # 3. Créer la structure
    dossier_sauvegarde = creer_structure_sauvegarde(chemin_t7)
    
    # 4. Sauvegarder
    fichiers_memoire = sauvegarder_memoire_thermique(dossier_sauvegarde)
    fichiers_accelerateurs = sauvegarder_accelerateurs(dossier_sauvegarde)
    
    # 5. Créer le rapport
    rapport = creer_rapport_sauvegarde(dossier_sauvegarde, stats)
    
    # 6. Résumé final
    print(f"\n🎉 SAUVEGARDE TERMINÉE AVEC SUCCÈS !")
    print(f"📁 Dossier: {dossier_sauvegarde}")
    print(f"💾 Fichiers mémoire: {fichiers_memoire}")
    print(f"⚡ Accélérateurs: {fichiers_accelerateurs}")
    print(f"📊 Taille mémoire: {stats.get('taille_fichier_mb', 0):.2f} MB")
    print(f"🧠 Neurones: {stats.get('nombre_neurones', 0):,}")
    
    return True

if __name__ == "__main__":
    main()
